apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaMirrorMaker2
metadata:
  name: data-platform-mirrormaker
  namespace: kafka
spec:
  version: {{ .Chart.AppVersion }}
  replicas: 1
  connectCluster: data-platform-cluster
  logging:
    type: inline
    loggers:
      org.apache.kafka.clients.NetworkClient: DEBUG
      org.apache.kafka.clients.Metadata: DEBUG
      org.apache.kafka.common.network.SslChannelBuilder: DEBUG
  clusters:
    - alias: schema-registry-cluster
      bootstrapServers: schema-registry-kafka.kafka.svc.cluster.local:9092
    - alias: data-platform-cluster
      bootstrapServers: data-platform-cluster-kafka-bootstrap.kafka.svc.cluster.local:9093
      tls:
        trustedCertificates:
          - secretName: data-platform-cluster-cluster-ca-cert
            certificate: ca.crt
      authentication:
        type: tls
        certificateAndKey:
          certificate: user.crt
          key: user.key
          secretName: super-user
  mirrors:
    - sourceCluster: schema-registry-cluster
      targetCluster: data-platform-cluster
      sourceConnector:
        tasksMax: 1
        config:
          replication.policy.class: org.apache.kafka.connect.mirror.IdentityReplicationPolicy
          replication.factor: 3
          offset-syncs.topic.replication.factor: 3
          refresh.topics.enabled: false
          sync.topic.acls.enabled: false
          sync.topic.configs.enabled: false
          exclude.internal.topics: true
          refresh.topics.interval.seconds: 300
          auto.offset.reset: earliest
          metadata.max.age.ms: 600000
      heartbeatConnector:
        config:
          heartbeats.topic.replication.factor: 1
      checkpointConnector:
        config:
          replication.policy.class: org.apache.kafka.connect.mirror.IdentityReplicationPolicy
          checkpoints.topic.replication.factor: 3
          sync.group.offsets.enabled: false
          refresh.groups.interval.seconds: 60
      topicsPattern: "^_schemas$|^heartbeats$"
      groupsPattern: "^$" # Matcha inga consumer groups
