deployments:
  authorizations:
    job:
      parallelism: 1
    jobManager:
      resource:
        memory: "2048Mi"
    taskManager:
      replicas: 1
      resource:
        memory: "2048Mi"

kafka:
  sourceGroupId: flink-authorizations-group-20

delta:
  table:
    path: abfss://<EMAIL>/base24/authorizations
  fs:
    defaultFS: abfss://<EMAIL>

storage:
  account:
    name: stageasdlsa
    tenantId: d357e4b0-a5d5-400f-b185-86fb574bef72
    clientId: 52846f3b-b0d9-4a4d-8b9b-65faff977fa9

flink:
  checkpointIntervalMin: 60
