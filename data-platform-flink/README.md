```
kubectl get -n kafka secret data-platform-cluster-cluster-ca-cert -o json 
| jq 'del(.metadata.namespace, .metadata.resourceVersion, .metadata.uid, .metadata.labels, .metadata.creationTimestamp, .metadata.ownerReferences)'
| kubectl apply -n flink -f -

```


```
kubectl get secret flink-authorizations-user -n kafka -o json 
| jq 'del(.metadata.namespace, .metadata.resourceVersion, .metadata.uid, .metadata.labels, .metadata.creationTimestamp, .metadata.ownerReferences)'
| kubectl apply -n flink -f -
```

```
kubectl get secret azure-storage-datalake-sink-sas -n kafka -o json | jq 'del(.metadata.namespace, .metadata.resourceVersion, .metadata.uid, .metadata.labels, .metadata.creationTimestamp, .metadata.ownerReferences)'| kubectl apply -n flink -f -
```


## Access till ADLS container från Flink deployment

1. Skapa ett storage account, ADLS container och en private endpoint i Terraform. Private endpoint så att den kan nås från Synapse via Private DNA Zone.
2. Skapa en UAMI, role, role assignment och en federated identity i Terraform
3. Skapa ett Service Account i Kubernetes (med samma namn som federated identity:n)
4. Lägg till labeln 'azure.workload.identity/use: "true"' på Service Account och Poddar
5. Konfigurera Flink-jobbet (Hadoop config) till att använda UAMI:s clientId och tenantId. 

