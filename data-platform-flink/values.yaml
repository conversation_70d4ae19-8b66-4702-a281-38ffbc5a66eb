image:
  repository: ghcr.io/payex/flink-authorizations
  tag: 2.1.45

namespace: flink

kafka:
  bootstrapServers: data-platform-cluster-kafka-bootstrap.kafka.svc:9093
  sourceTopic: base24-authorizations-curated

schema:
  registry:
    url: http://schema-registry.kafka.svc:8081

delta:
  table:
    path: CHANGE_ME
  fs:
    defaultFS: CHANGE_ME

storage:
  account:
    name: CHANGE_ME
    tenantId: CHANGE_ME
    clientId: CHANGE_ME
