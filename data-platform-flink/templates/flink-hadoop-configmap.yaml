apiVersion: v1
kind: ConfigMap
metadata:
  name: flink-hadoop-config
  namespace: kafka
data:
  core-site.xml: |
    <?xml version="1.0" encoding="UTF-8"?>
    <?xml-stylesheet type="text/xsl" href="configuration.xsl"?>
    <configuration>
        <property>
            <name>fs.azure.account.auth.type.{{ .Values.storage.account.name }}.dfs.core.windows.net</name>
            <value>OAuth</value>
        </property>
        <property>
            <name>fs.azure.account.oauth.provider.type.{{ .Values.storage.account.name }}.dfs.core.windows.net</name>
            <value>org.apache.hadoop.fs.azurebfs.oauth2.WorkloadIdentityTokenProvider</value>
        </property>        
        <property>
            <name>fs.azure.account.oauth2.client.id</name>
            <value>{{ .Values.storage.account.clientId }}</value>
        </property>
        <property>
            <name>fs.azure.account.oauth2.msi.tenant</name>
            <value>{{ .Values.storage.account.tenantId }}</value>
        </property>
    </configuration>    