apiVersion: v1
kind: ConfigMap
metadata:
  name: base24-authorizations-sink-config
data:
  application.properties: |
    kafka.bootstrap.servers={{ .Values.kafka.bootstrapServers }}
    kafka.source.topic={{ .Values.kafka.sourceTopic }}
    kafka.source.group.id={{ .Values.kafka.sourceGroupId }}
    schema.registry.url={{ .Values.schema.registry.url }}
    delta.table.path={{ .Values.delta.table.path }}
    delta.fs.defaultFS={{ .Values.delta.fs.defaultFS }}
    storage.account.name={{ .Values.storage.account.name }}
    storage.account.tenantId={{ .Values.storage.account.tenantId }}
    storage.account.clientId={{ .Values.storage.account.clientId }}
