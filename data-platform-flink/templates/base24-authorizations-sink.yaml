apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  name: flink-authorizations
  namespace: kafka
spec:
  flinkVersion: v1_20
  serviceAccount: flink-deployment-sa
  logConfiguration:
    "log4j-console.properties": |
      rootLogger.level = INFO
      rootLogger.appenderRef.console.ref = ConsoleAppender

      # Appender-konfiguration för konsolen
      appender.console.name = ConsoleAppender
      appender.console.type = CONSOLE
      appender.console.layout.type = PatternLayout
      appender.console.layout.pattern = %d{yyyy-MM-dd HH:mm:ss,SSS} %-5p %-60c %x - %m%n

      # Höj loggnivån för Hadoops filsystem och säkerhetsklasser
      logger.hadoopfs.name = org.apache.hadoop.fs
      logger.hadoopfs.level = TRACE

      logger.hadoopauth.name = org.apache.hadoop.security
      logger.hadoopauth.level = TRACE
  flinkConfiguration:
    "fs.allowed-fallback-filesystems": abfss
    # "fs.default-scheme": abfss
    # "fs.azure.account.auth.type.{{ .Values.storage.account.name }}.dfs.core.windows.net": "OAuth"
    # "fs.azure.account.oauth.provider.type.{{ .Values.storage.account.name }}.dfs.core.windows.net": "org.apache.hadoop.fs.azurebfs.oauth2.WorkloadIdentityTokenProvider"
    # "fs.azure.account.oauth2.client.id": "{{ .Values.storage.account.clientId }}"
    # "fs.azure.account.oauth2.msi.tenant": "{{ .Values.storage.account.tenantId }}"
  job:
    jarURI: local:///opt/flink/jobs/flink-authorizations.jar
    parallelism: {{ .Values.deployments.authorizations.job.parallelism }}
    upgradeMode: stateless
  jobManager:
    resource:
      memory: {{ .Values.deployments.authorizations.jobManager.resource.memory }}
  taskManager:
    replicas: {{ .Values.deployments.authorizations.taskManager.replicas }}
    resource:
      memory: {{ .Values.deployments.authorizations.taskManager.resource.memory }}
  podTemplate:
    metadata:
      labels:
        azure.workload.identity/use: "true" 
    spec:
      securityContext:
        fsGroup: 9999
      imagePullSecrets:
        - name: kafka-connect-image-pull-secret
      containers:
      - name: flink-main-container
        image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
        env:  
          - name: HADOOP_CONF_DIR
            value: /opt/hadoop/conf
          # - name: HADOOP_CLASSPATH
          #   value: /opt/flink/plugins/azure-fs-hadoop/*:/opt/hadoop/conf
          - name: JAVA_TOOL_OPTIONS
            value: "--add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED"
        volumeMounts:
          - name: hadoop-config-volume
            mountPath: /opt/hadoop/conf
            readOnly: true
          - name: data-platform-cluster-cluster-ca-cert
            mountPath: /opt/kafka/ca
            readOnly: true
          - name: kafka-certificates
            mountPath: /opt/kafka/certs
            readOnly: true
          - name: application-config-volume
            mountPath: /opt/flink/application-config
            readOnly: true
      volumes:
        - name: hadoop-config-volume
          configMap:
            name: flink-hadoop-config
        - name: data-platform-cluster-cluster-ca-cert
          secret:
            secretName: data-platform-cluster-cluster-ca-cert
        - name: kafka-certificates
          secret:
            secretName: flink-authorizations-user
        - name: application-config-volume
          configMap:
            name: base24-authorizations-sink-config
