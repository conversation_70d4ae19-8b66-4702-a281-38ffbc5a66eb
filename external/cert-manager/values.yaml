installCRDs: true

startupapicheck:
  timeout: 5m

webhook:
  validatingWebhookConfigurationAnnotations:
    admissions.enforcer/disabled: "true"
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: agentpool
                operator: In
                values:
                  - general
                  - generaltemp

podLabels:
  azure.workload.identity/use: "true"

serviceAccount:
  labels:
    azure.workload.identity/use: "true"

extraArgs:
  - --dns01-recursive-nameservers=168.63.129.16:53
  - --dns01-recursive-nameservers-only=true

affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
        - matchExpressions:
            - key: agentpool
              operator: In
              values:
                - general
                - generaltemp

cainjector:
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: agentpool
                operator: In
                values:
                  - general
                  - generaltemp
