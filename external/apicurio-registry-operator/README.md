# Apicurio

## Install and upgrade

### Go to GH Repo:
https://github.com/Apicurio/apicurio-registry/tree/main/operator/install

### Download latest version:

```
curl -sSLO --output-dir external/apicurio-registry-operator/ https://raw.githubusercontent.com/Apicurio/apicurio-registry/refs/heads/main/operator/install/apicurio-registry-operator-3.0.8.yaml
```

```
curl -sSLO --output-dir external/apicurio-registry-operator/ https://raw.githubusercontent.com/Apicurio/apicurio-registry/refs/heads/main/operator/install/apicurio-registry-operator-3.0.10.yaml
```

Keep the old operator file!

### Update kustomization and ArgoCD app

TODO: per environment overlays of operator version

[kustomization](kustomization.yaml)

[stage](../../argocd-app-of-apps/environments/stage/templates/apicurio-registry-operator.yaml)
[prod](../../argocd-app-of-apps/environments/prod/templates/apicurio-registry-operator.yaml)
