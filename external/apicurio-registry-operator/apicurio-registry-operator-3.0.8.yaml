apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  labels:
    app: apicurio-registry-operator
    app.kubernetes.io/component: operator
    app.kubernetes.io/name: apicurio-registry-operator
    app.kubernetes.io/part-of: apicurio-registry
    app.kubernetes.io/version: 3.0.8
  name: apicurioregistries3.registry.apicur.io
spec:
  group: registry.apicur.io
  names:
    kind: ApicurioRegistry3
    plural: apicurioregistries3
    shortNames:
    - registry3
    singular: apicurioregistry3
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        properties:
          spec:
            properties:
              app:
                description: Configure Apicurio Registry backend (app) component.
                properties:
                  auth:
                    description: |
                      Configure authentication and authorization of Apicurio Registry.
                    properties:
                      anonymousReadsEnabled:
                        description: To allow anonymous users, such as REST API calls
                          with no authentication credentials, to make read-only calls
                          to the REST API, set the following option to true.
                        type: boolean
                      appClientId:
                        description: |-
                          Apicurio Registry backend clientId used for OIDC authentication.
                          In Identity providers like Keycloak, this is the client id used for the Quarkus backend application
                        type: string
                      authServerUrl:
                        description: URL of the identity server.
                        type: string
                      authz:
                        description: Authorization configuration.
                        properties:
                          adminOverride:
                            description: Admin override configuration
                            properties:
                              claimName:
                                description: The name of a JWT token claim to use
                                  for determining admin-override.
                                type: string
                              claimValue:
                                description: The value that the JWT token claim indicated
                                  by the CLAIM variable must be for the user to be
                                  granted admin-override.
                                type: string
                              enabled:
                                description: Auth admin override enabled.
                                type: boolean
                              from:
                                description: Where to look for admin-override information.
                                  Only token is currently supported.
                                type: string
                              role:
                                description: The name of the role that indicates a
                                  user is an admin.
                                type: string
                              type:
                                description: The type of information used to determine
                                  if a user is an admin. Values depend on the value
                                  of the FROM variable, for example, role or claim
                                  when FROM is token.
                                type: string
                            type: object
                          enabled:
                            description: Enabled role-based authorization.
                            type: boolean
                          groupAccessEnabled:
                            description: When owner-only authorization and group owner-only
                              authorization are both enabled, only the user who created
                              an artifact group has write access to that artifact
                              group, for example, to add or remove artifacts in that
                              group.
                            type: boolean
                          ownerOnlyEnabled:
                            description: When owner-only authorization is enabled,
                              only the user who created an artifact can modify or
                              delete that artifact.
                            type: boolean
                          readAccessEnabled:
                            description: When the authenticated read access option
                              is enabled, Apicurio Registry grants at least read-only
                              access to requests from any authenticated user in the
                              same organization, regardless of their user role.
                            type: boolean
                          roles:
                            description: Configure authorization role source and role
                              names.
                            properties:
                              admin:
                                description: The name of the role that indicates a
                                  user is an admin.
                                type: string
                              developer:
                                description: The name of the role that indicates a
                                  user is a developer.
                                type: string
                              readOnly:
                                description: The name of the role that indicates a
                                  user has read-only access.
                                type: string
                              source:
                                description: When set to token, user roles are taken
                                  from the authentication token.
                                type: string
                            type: object
                        type: object
                      basicAuth:
                        description: Client credentials basic auth configuration.
                        properties:
                          cacheExpiration:
                            description: |
                              Client credentials token expiration time. This value is a string representing a duration:

                              | Abbreviation          | Time Unit    |
                              |-----------------------|--------------|
                              | ns, nano, nanos       | Nanosecond   |
                              | us, µs, micro, micros | Microseconds |
                              | ms, milli, millis     | Millisecond  |
                              | s, sec, secs          | Second       |
                              | m, min, mins          | Minute       |
                              | h, hr, hour, hours    | Hour         |
                              | d, day, days          | Day          |
                              | w, wk, week, weeks    | Week         |

                              Example: "1min1s"
                            type: string
                          enabled:
                            description: Enabled client credentials.
                            type: boolean
                        type: object
                      enabled:
                        description: |-
                          Enable Apicurio Registry Authentication.
                          In Identity providers like Keycloak, this is the client id used for the Quarkus backend application
                        type: boolean
                      logoutUrl:
                        description: Apicurio Registry UI redirect URI used for redirection
                          after logout.
                        type: string
                      redirectUri:
                        description: Apicurio Registry UI redirect URI used for redirection
                          after successful authentication.
                        type: string
                      tls:
                        description: |-
                          OIDC TLS configuration.
                          When custom certificates are used, this is the field to be used to configure the trustore
                        properties:
                          tlsVerificationType:
                            description: Verify the identity server certificate.
                            type: string
                          truststorePasswordSecretRef:
                            description: Name of a Secret that contains the TLS truststore
                              password. Key `ca.password` is assumed by default.
                            properties:
                              key:
                                description: Name of the key in the referenced Secret
                                  that contain the target data. This field might be
                                  optional if a default value has been defined.
                                type: string
                              name:
                                description: Name of a Secret that is being referenced.
                                type: string
                            type: object
                          truststoreSecretRef:
                            description: Name of a Secret that contains the TLS truststore
                              (in PKCS12 format). Key `ca.p12` is assumed by default.
                            properties:
                              key:
                                description: Name of the key in the referenced Secret
                                  that contain the target data. This field might be
                                  optional if a default value has been defined.
                                type: string
                              name:
                                description: Name of a Secret that is being referenced.
                                type: string
                            type: object
                        type: object
                      uiClientId:
                        description: |-
                          Apicurio Registry UI clientId used for OIDC authentication.
                          In Identity providers like Keycloak, this is the client id used for the frontend React application
                        type: string
                    type: object
                  env:
                    description: Configure a list of environment variables that will
                      be passed to this components' container.
                    items:
                      properties:
                        name:
                          type: string
                        value:
                          type: string
                        valueFrom:
                          properties:
                            configMapKeyRef:
                              properties:
                                key:
                                  type: string
                                name:
                                  type: string
                                optional:
                                  type: boolean
                              type: object
                            fieldRef:
                              properties:
                                apiVersion:
                                  type: string
                                fieldPath:
                                  type: string
                              type: object
                            resourceFieldRef:
                              properties:
                                containerName:
                                  type: string
                                divisor:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  x-kubernetes-int-or-string: true
                                resource:
                                  type: string
                              type: object
                            secretKeyRef:
                              properties:
                                key:
                                  type: string
                                name:
                                  type: string
                                optional:
                                  type: boolean
                              type: object
                          type: object
                      type: object
                    type: array
                  features:
                    description: |
                      Configure features of the Apicurio Registry backend (app).
                    properties:
                      resourceDeleteEnabled:
                        description: |-
                          Apicurio Registry backend 'allow deletes' feature.
                          If the value is true, the application will be configured to allow Groups, Artifacts, and
                          Artifact Versions to be deleted.  By default, resources in Registry are immutable and so
                          cannot be deleted. Registry can be configured to allow deleting of these resources at a
                          granular level (e.g. only allow deleting artifact versions) using ENV variables.  This
                          option enables deletes for all three resource types.
                        type: boolean
                    type: object
                  host:
                    description: 'DEPRECATED: Use the `(component).ingress.host` field
                      instead. The operator will attempt to update the field automatically.'
                    type: string
                  ingress:
                    description: Configure Ingress for the component.
                    properties:
                      enabled:
                        description: |
                          Whether an Ingress should be managed by the operator.  Defaults to 'true'.

                          Set this to 'false' if you want to create your own custom Ingress.
                        type: boolean
                      host:
                        description: |-
                          Configure hostname of the operator-managed Ingress. If the value is empty, the operator will not create an Ingress resource for the component.

                          IMPORTANT: If the Ingress already exists and the value becomes empty, the Ingress will be deleted.
                        type: string
                    type: object
                  kafkasql:
                    description: |-
                      DEPRECATED: Use the `app.storage.type` and `app.storage.kafkasql` fields instead.
                       The operator will attempt to update the fields automatically.
                    properties:
                      bootstrapServers:
                        description: 'DEPRECATED: Use the `app.storage.kafkasql.bootstrapServers`
                          field instead. The operator will attempt to update the field
                          automatically.'
                        type: string
                    type: object
                  networkPolicy:
                    description: |2
                              Configuration of a NetworkPolicy for the component.
                    properties:
                      enabled:
                        description: |
                          Whether a NetworkPolicy should be managed by the operator.  Defaults to 'true'.

                          Set this to 'false' if you want to create your own custom NetworkPolicy.
                        type: boolean
                    type: object
                  podDisruptionBudget:
                    description: |
                      Configuration of a PodDisruptionBudget for the component.
                    properties:
                      enabled:
                        description: |
                          Whether a PodDisruptionBudget should be managed by the operator.  Defaults to 'true'.

                          Set this to 'false' if you want to create your own custom PodDisruptionBudget.
                        type: boolean
                    type: object
                  podTemplateSpec:
                    description: |-
                      `PodTemplateSpec` describes the data a pod should have when created from a template.

                      This template is used by the operator to create the components' Deployment. The operator first extends the template
                      with default values if required, and then applies additional configuration
                      based on the other contents of `ApicurioRegistry3` CR.
                    properties:
                      metadata:
                        properties:
                          annotations:
                            additionalProperties:
                              type: string
                            type: object
                          creationTimestamp:
                            type: string
                          deletionGracePeriodSeconds:
                            type: integer
                          deletionTimestamp:
                            type: string
                          finalizers:
                            items:
                              type: string
                            type: array
                          generateName:
                            type: string
                          generation:
                            type: integer
                          labels:
                            additionalProperties:
                              type: string
                            type: object
                          managedFields:
                            items:
                              properties:
                                apiVersion:
                                  type: string
                                fieldsType:
                                  type: string
                                fieldsV1:
                                  type: object
                                manager:
                                  type: string
                                operation:
                                  type: string
                                subresource:
                                  type: string
                                time:
                                  type: string
                              type: object
                            type: array
                          name:
                            type: string
                          namespace:
                            type: string
                          ownerReferences:
                            items:
                              properties:
                                apiVersion:
                                  type: string
                                blockOwnerDeletion:
                                  type: boolean
                                controller:
                                  type: boolean
                                kind:
                                  type: string
                                name:
                                  type: string
                                uid:
                                  type: string
                              type: object
                            type: array
                          resourceVersion:
                            type: string
                          selfLink:
                            type: string
                          uid:
                            type: string
                        type: object
                      spec:
                        properties:
                          activeDeadlineSeconds:
                            type: integer
                          affinity:
                            properties:
                              nodeAffinity:
                                properties:
                                  preferredDuringSchedulingIgnoredDuringExecution:
                                    items:
                                      properties:
                                        preference:
                                          properties:
                                            matchExpressions:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                type: object
                                              type: array
                                            matchFields:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                type: object
                                              type: array
                                          type: object
                                        weight:
                                          type: integer
                                      type: object
                                    type: array
                                  requiredDuringSchedulingIgnoredDuringExecution:
                                    properties:
                                      nodeSelectorTerms:
                                        items:
                                          properties:
                                            matchExpressions:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                type: object
                                              type: array
                                            matchFields:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                type: object
                                              type: array
                                          type: object
                                        type: array
                                    type: object
                                type: object
                              podAffinity:
                                properties:
                                  preferredDuringSchedulingIgnoredDuringExecution:
                                    items:
                                      properties:
                                        podAffinityTerm:
                                          properties:
                                            labelSelector:
                                              properties:
                                                matchExpressions:
                                                  items:
                                                    properties:
                                                      key:
                                                        type: string
                                                      operator:
                                                        type: string
                                                      values:
                                                        items:
                                                          type: string
                                                        type: array
                                                    type: object
                                                  type: array
                                                matchLabels:
                                                  additionalProperties:
                                                    type: string
                                                  type: object
                                              type: object
                                            matchLabelKeys:
                                              items:
                                                type: string
                                              type: array
                                            mismatchLabelKeys:
                                              items:
                                                type: string
                                              type: array
                                            namespaceSelector:
                                              properties:
                                                matchExpressions:
                                                  items:
                                                    properties:
                                                      key:
                                                        type: string
                                                      operator:
                                                        type: string
                                                      values:
                                                        items:
                                                          type: string
                                                        type: array
                                                    type: object
                                                  type: array
                                                matchLabels:
                                                  additionalProperties:
                                                    type: string
                                                  type: object
                                              type: object
                                            namespaces:
                                              items:
                                                type: string
                                              type: array
                                            topologyKey:
                                              type: string
                                          type: object
                                        weight:
                                          type: integer
                                      type: object
                                    type: array
                                  requiredDuringSchedulingIgnoredDuringExecution:
                                    items:
                                      properties:
                                        labelSelector:
                                          properties:
                                            matchExpressions:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                type: object
                                              type: array
                                            matchLabels:
                                              additionalProperties:
                                                type: string
                                              type: object
                                          type: object
                                        matchLabelKeys:
                                          items:
                                            type: string
                                          type: array
                                        mismatchLabelKeys:
                                          items:
                                            type: string
                                          type: array
                                        namespaceSelector:
                                          properties:
                                            matchExpressions:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                type: object
                                              type: array
                                            matchLabels:
                                              additionalProperties:
                                                type: string
                                              type: object
                                          type: object
                                        namespaces:
                                          items:
                                            type: string
                                          type: array
                                        topologyKey:
                                          type: string
                                      type: object
                                    type: array
                                type: object
                              podAntiAffinity:
                                properties:
                                  preferredDuringSchedulingIgnoredDuringExecution:
                                    items:
                                      properties:
                                        podAffinityTerm:
                                          properties:
                                            labelSelector:
                                              properties:
                                                matchExpressions:
                                                  items:
                                                    properties:
                                                      key:
                                                        type: string
                                                      operator:
                                                        type: string
                                                      values:
                                                        items:
                                                          type: string
                                                        type: array
                                                    type: object
                                                  type: array
                                                matchLabels:
                                                  additionalProperties:
                                                    type: string
                                                  type: object
                                              type: object
                                            matchLabelKeys:
                                              items:
                                                type: string
                                              type: array
                                            mismatchLabelKeys:
                                              items:
                                                type: string
                                              type: array
                                            namespaceSelector:
                                              properties:
                                                matchExpressions:
                                                  items:
                                                    properties:
                                                      key:
                                                        type: string
                                                      operator:
                                                        type: string
                                                      values:
                                                        items:
                                                          type: string
                                                        type: array
                                                    type: object
                                                  type: array
                                                matchLabels:
                                                  additionalProperties:
                                                    type: string
                                                  type: object
                                              type: object
                                            namespaces:
                                              items:
                                                type: string
                                              type: array
                                            topologyKey:
                                              type: string
                                          type: object
                                        weight:
                                          type: integer
                                      type: object
                                    type: array
                                  requiredDuringSchedulingIgnoredDuringExecution:
                                    items:
                                      properties:
                                        labelSelector:
                                          properties:
                                            matchExpressions:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                type: object
                                              type: array
                                            matchLabels:
                                              additionalProperties:
                                                type: string
                                              type: object
                                          type: object
                                        matchLabelKeys:
                                          items:
                                            type: string
                                          type: array
                                        mismatchLabelKeys:
                                          items:
                                            type: string
                                          type: array
                                        namespaceSelector:
                                          properties:
                                            matchExpressions:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                type: object
                                              type: array
                                            matchLabels:
                                              additionalProperties:
                                                type: string
                                              type: object
                                          type: object
                                        namespaces:
                                          items:
                                            type: string
                                          type: array
                                        topologyKey:
                                          type: string
                                      type: object
                                    type: array
                                type: object
                            type: object
                          automountServiceAccountToken:
                            type: boolean
                          containers:
                            items:
                              properties:
                                args:
                                  items:
                                    type: string
                                  type: array
                                command:
                                  items:
                                    type: string
                                  type: array
                                env:
                                  items:
                                    properties:
                                      name:
                                        type: string
                                      value:
                                        type: string
                                      valueFrom:
                                        properties:
                                          configMapKeyRef:
                                            properties:
                                              key:
                                                type: string
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            type: object
                                          fieldRef:
                                            properties:
                                              apiVersion:
                                                type: string
                                              fieldPath:
                                                type: string
                                            type: object
                                          resourceFieldRef:
                                            properties:
                                              containerName:
                                                type: string
                                              divisor:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                              resource:
                                                type: string
                                            type: object
                                          secretKeyRef:
                                            properties:
                                              key:
                                                type: string
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            type: object
                                        type: object
                                    type: object
                                  type: array
                                envFrom:
                                  items:
                                    properties:
                                      configMapRef:
                                        properties:
                                          name:
                                            type: string
                                          optional:
                                            type: boolean
                                        type: object
                                      prefix:
                                        type: string
                                      secretRef:
                                        properties:
                                          name:
                                            type: string
                                          optional:
                                            type: boolean
                                        type: object
                                    type: object
                                  type: array
                                image:
                                  type: string
                                imagePullPolicy:
                                  type: string
                                lifecycle:
                                  properties:
                                    postStart:
                                      properties:
                                        exec:
                                          properties:
                                            command:
                                              items:
                                                type: string
                                              type: array
                                          type: object
                                        httpGet:
                                          properties:
                                            host:
                                              type: string
                                            httpHeaders:
                                              items:
                                                properties:
                                                  name:
                                                    type: string
                                                  value:
                                                    type: string
                                                type: object
                                              type: array
                                            path:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                            scheme:
                                              type: string
                                          type: object
                                        sleep:
                                          properties:
                                            seconds:
                                              type: integer
                                          type: object
                                        tcpSocket:
                                          properties:
                                            host:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                          type: object
                                      type: object
                                    preStop:
                                      properties:
                                        exec:
                                          properties:
                                            command:
                                              items:
                                                type: string
                                              type: array
                                          type: object
                                        httpGet:
                                          properties:
                                            host:
                                              type: string
                                            httpHeaders:
                                              items:
                                                properties:
                                                  name:
                                                    type: string
                                                  value:
                                                    type: string
                                                type: object
                                              type: array
                                            path:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                            scheme:
                                              type: string
                                          type: object
                                        sleep:
                                          properties:
                                            seconds:
                                              type: integer
                                          type: object
                                        tcpSocket:
                                          properties:
                                            host:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                          type: object
                                      type: object
                                  type: object
                                livenessProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          type: integer
                                        service:
                                          type: string
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      type: object
                                    initialDelaySeconds:
                                      type: integer
                                    periodSeconds:
                                      type: integer
                                    successThreshold:
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      type: object
                                    terminationGracePeriodSeconds:
                                      type: integer
                                    timeoutSeconds:
                                      type: integer
                                  type: object
                                name:
                                  type: string
                                ports:
                                  items:
                                    properties:
                                      containerPort:
                                        type: integer
                                      hostIP:
                                        type: string
                                      hostPort:
                                        type: integer
                                      name:
                                        type: string
                                      protocol:
                                        type: string
                                    type: object
                                  type: array
                                readinessProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          type: integer
                                        service:
                                          type: string
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      type: object
                                    initialDelaySeconds:
                                      type: integer
                                    periodSeconds:
                                      type: integer
                                    successThreshold:
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      type: object
                                    terminationGracePeriodSeconds:
                                      type: integer
                                    timeoutSeconds:
                                      type: integer
                                  type: object
                                resizePolicy:
                                  items:
                                    properties:
                                      resourceName:
                                        type: string
                                      restartPolicy:
                                        type: string
                                    type: object
                                  type: array
                                resources:
                                  properties:
                                    claims:
                                      items:
                                        properties:
                                          name:
                                            type: string
                                        type: object
                                      type: array
                                    limits:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        x-kubernetes-int-or-string: true
                                      type: object
                                    requests:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        x-kubernetes-int-or-string: true
                                      type: object
                                  type: object
                                restartPolicy:
                                  type: string
                                securityContext:
                                  properties:
                                    allowPrivilegeEscalation:
                                      type: boolean
                                    appArmorProfile:
                                      properties:
                                        localhostProfile:
                                          type: string
                                        type:
                                          type: string
                                      type: object
                                    capabilities:
                                      properties:
                                        add:
                                          items:
                                            type: string
                                          type: array
                                        drop:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    privileged:
                                      type: boolean
                                    procMount:
                                      type: string
                                    readOnlyRootFilesystem:
                                      type: boolean
                                    runAsGroup:
                                      type: integer
                                    runAsNonRoot:
                                      type: boolean
                                    runAsUser:
                                      type: integer
                                    seLinuxOptions:
                                      properties:
                                        level:
                                          type: string
                                        role:
                                          type: string
                                        type:
                                          type: string
                                        user:
                                          type: string
                                      type: object
                                    seccompProfile:
                                      properties:
                                        localhostProfile:
                                          type: string
                                        type:
                                          type: string
                                      type: object
                                    windowsOptions:
                                      properties:
                                        gmsaCredentialSpec:
                                          type: string
                                        gmsaCredentialSpecName:
                                          type: string
                                        hostProcess:
                                          type: boolean
                                        runAsUserName:
                                          type: string
                                      type: object
                                  type: object
                                startupProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          type: integer
                                        service:
                                          type: string
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      type: object
                                    initialDelaySeconds:
                                      type: integer
                                    periodSeconds:
                                      type: integer
                                    successThreshold:
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      type: object
                                    terminationGracePeriodSeconds:
                                      type: integer
                                    timeoutSeconds:
                                      type: integer
                                  type: object
                                stdin:
                                  type: boolean
                                stdinOnce:
                                  type: boolean
                                terminationMessagePath:
                                  type: string
                                terminationMessagePolicy:
                                  type: string
                                tty:
                                  type: boolean
                                volumeDevices:
                                  items:
                                    properties:
                                      devicePath:
                                        type: string
                                      name:
                                        type: string
                                    type: object
                                  type: array
                                volumeMounts:
                                  items:
                                    properties:
                                      mountPath:
                                        type: string
                                      mountPropagation:
                                        type: string
                                      name:
                                        type: string
                                      readOnly:
                                        type: boolean
                                      recursiveReadOnly:
                                        type: string
                                      subPath:
                                        type: string
                                      subPathExpr:
                                        type: string
                                    type: object
                                  type: array
                                workingDir:
                                  type: string
                              type: object
                            type: array
                          dnsConfig:
                            properties:
                              nameservers:
                                items:
                                  type: string
                                type: array
                              options:
                                items:
                                  properties:
                                    name:
                                      type: string
                                    value:
                                      type: string
                                  type: object
                                type: array
                              searches:
                                items:
                                  type: string
                                type: array
                            type: object
                          dnsPolicy:
                            type: string
                          enableServiceLinks:
                            type: boolean
                          ephemeralContainers:
                            items:
                              properties:
                                args:
                                  items:
                                    type: string
                                  type: array
                                command:
                                  items:
                                    type: string
                                  type: array
                                env:
                                  items:
                                    properties:
                                      name:
                                        type: string
                                      value:
                                        type: string
                                      valueFrom:
                                        properties:
                                          configMapKeyRef:
                                            properties:
                                              key:
                                                type: string
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            type: object
                                          fieldRef:
                                            properties:
                                              apiVersion:
                                                type: string
                                              fieldPath:
                                                type: string
                                            type: object
                                          resourceFieldRef:
                                            properties:
                                              containerName:
                                                type: string
                                              divisor:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                              resource:
                                                type: string
                                            type: object
                                          secretKeyRef:
                                            properties:
                                              key:
                                                type: string
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            type: object
                                        type: object
                                    type: object
                                  type: array
                                envFrom:
                                  items:
                                    properties:
                                      configMapRef:
                                        properties:
                                          name:
                                            type: string
                                          optional:
                                            type: boolean
                                        type: object
                                      prefix:
                                        type: string
                                      secretRef:
                                        properties:
                                          name:
                                            type: string
                                          optional:
                                            type: boolean
                                        type: object
                                    type: object
                                  type: array
                                image:
                                  type: string
                                imagePullPolicy:
                                  type: string
                                lifecycle:
                                  properties:
                                    postStart:
                                      properties:
                                        exec:
                                          properties:
                                            command:
                                              items:
                                                type: string
                                              type: array
                                          type: object
                                        httpGet:
                                          properties:
                                            host:
                                              type: string
                                            httpHeaders:
                                              items:
                                                properties:
                                                  name:
                                                    type: string
                                                  value:
                                                    type: string
                                                type: object
                                              type: array
                                            path:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                            scheme:
                                              type: string
                                          type: object
                                        sleep:
                                          properties:
                                            seconds:
                                              type: integer
                                          type: object
                                        tcpSocket:
                                          properties:
                                            host:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                          type: object
                                      type: object
                                    preStop:
                                      properties:
                                        exec:
                                          properties:
                                            command:
                                              items:
                                                type: string
                                              type: array
                                          type: object
                                        httpGet:
                                          properties:
                                            host:
                                              type: string
                                            httpHeaders:
                                              items:
                                                properties:
                                                  name:
                                                    type: string
                                                  value:
                                                    type: string
                                                type: object
                                              type: array
                                            path:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                            scheme:
                                              type: string
                                          type: object
                                        sleep:
                                          properties:
                                            seconds:
                                              type: integer
                                          type: object
                                        tcpSocket:
                                          properties:
                                            host:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                          type: object
                                      type: object
                                  type: object
                                livenessProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          type: integer
                                        service:
                                          type: string
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      type: object
                                    initialDelaySeconds:
                                      type: integer
                                    periodSeconds:
                                      type: integer
                                    successThreshold:
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      type: object
                                    terminationGracePeriodSeconds:
                                      type: integer
                                    timeoutSeconds:
                                      type: integer
                                  type: object
                                name:
                                  type: string
                                ports:
                                  items:
                                    properties:
                                      containerPort:
                                        type: integer
                                      hostIP:
                                        type: string
                                      hostPort:
                                        type: integer
                                      name:
                                        type: string
                                      protocol:
                                        type: string
                                    type: object
                                  type: array
                                readinessProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          type: integer
                                        service:
                                          type: string
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      type: object
                                    initialDelaySeconds:
                                      type: integer
                                    periodSeconds:
                                      type: integer
                                    successThreshold:
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      type: object
                                    terminationGracePeriodSeconds:
                                      type: integer
                                    timeoutSeconds:
                                      type: integer
                                  type: object
                                resizePolicy:
                                  items:
                                    properties:
                                      resourceName:
                                        type: string
                                      restartPolicy:
                                        type: string
                                    type: object
                                  type: array
                                resources:
                                  properties:
                                    claims:
                                      items:
                                        properties:
                                          name:
                                            type: string
                                        type: object
                                      type: array
                                    limits:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        x-kubernetes-int-or-string: true
                                      type: object
                                    requests:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        x-kubernetes-int-or-string: true
                                      type: object
                                  type: object
                                restartPolicy:
                                  type: string
                                securityContext:
                                  properties:
                                    allowPrivilegeEscalation:
                                      type: boolean
                                    appArmorProfile:
                                      properties:
                                        localhostProfile:
                                          type: string
                                        type:
                                          type: string
                                      type: object
                                    capabilities:
                                      properties:
                                        add:
                                          items:
                                            type: string
                                          type: array
                                        drop:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    privileged:
                                      type: boolean
                                    procMount:
                                      type: string
                                    readOnlyRootFilesystem:
                                      type: boolean
                                    runAsGroup:
                                      type: integer
                                    runAsNonRoot:
                                      type: boolean
                                    runAsUser:
                                      type: integer
                                    seLinuxOptions:
                                      properties:
                                        level:
                                          type: string
                                        role:
                                          type: string
                                        type:
                                          type: string
                                        user:
                                          type: string
                                      type: object
                                    seccompProfile:
                                      properties:
                                        localhostProfile:
                                          type: string
                                        type:
                                          type: string
                                      type: object
                                    windowsOptions:
                                      properties:
                                        gmsaCredentialSpec:
                                          type: string
                                        gmsaCredentialSpecName:
                                          type: string
                                        hostProcess:
                                          type: boolean
                                        runAsUserName:
                                          type: string
                                      type: object
                                  type: object
                                startupProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          type: integer
                                        service:
                                          type: string
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      type: object
                                    initialDelaySeconds:
                                      type: integer
                                    periodSeconds:
                                      type: integer
                                    successThreshold:
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      type: object
                                    terminationGracePeriodSeconds:
                                      type: integer
                                    timeoutSeconds:
                                      type: integer
                                  type: object
                                stdin:
                                  type: boolean
                                stdinOnce:
                                  type: boolean
                                targetContainerName:
                                  type: string
                                terminationMessagePath:
                                  type: string
                                terminationMessagePolicy:
                                  type: string
                                tty:
                                  type: boolean
                                volumeDevices:
                                  items:
                                    properties:
                                      devicePath:
                                        type: string
                                      name:
                                        type: string
                                    type: object
                                  type: array
                                volumeMounts:
                                  items:
                                    properties:
                                      mountPath:
                                        type: string
                                      mountPropagation:
                                        type: string
                                      name:
                                        type: string
                                      readOnly:
                                        type: boolean
                                      recursiveReadOnly:
                                        type: string
                                      subPath:
                                        type: string
                                      subPathExpr:
                                        type: string
                                    type: object
                                  type: array
                                workingDir:
                                  type: string
                              type: object
                            type: array
                          hostAliases:
                            items:
                              properties:
                                hostnames:
                                  items:
                                    type: string
                                  type: array
                                ip:
                                  type: string
                              type: object
                            type: array
                          hostIPC:
                            type: boolean
                          hostNetwork:
                            type: boolean
                          hostPID:
                            type: boolean
                          hostUsers:
                            type: boolean
                          hostname:
                            type: string
                          imagePullSecrets:
                            items:
                              properties:
                                name:
                                  type: string
                              type: object
                            type: array
                          initContainers:
                            items:
                              properties:
                                args:
                                  items:
                                    type: string
                                  type: array
                                command:
                                  items:
                                    type: string
                                  type: array
                                env:
                                  items:
                                    properties:
                                      name:
                                        type: string
                                      value:
                                        type: string
                                      valueFrom:
                                        properties:
                                          configMapKeyRef:
                                            properties:
                                              key:
                                                type: string
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            type: object
                                          fieldRef:
                                            properties:
                                              apiVersion:
                                                type: string
                                              fieldPath:
                                                type: string
                                            type: object
                                          resourceFieldRef:
                                            properties:
                                              containerName:
                                                type: string
                                              divisor:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                              resource:
                                                type: string
                                            type: object
                                          secretKeyRef:
                                            properties:
                                              key:
                                                type: string
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            type: object
                                        type: object
                                    type: object
                                  type: array
                                envFrom:
                                  items:
                                    properties:
                                      configMapRef:
                                        properties:
                                          name:
                                            type: string
                                          optional:
                                            type: boolean
                                        type: object
                                      prefix:
                                        type: string
                                      secretRef:
                                        properties:
                                          name:
                                            type: string
                                          optional:
                                            type: boolean
                                        type: object
                                    type: object
                                  type: array
                                image:
                                  type: string
                                imagePullPolicy:
                                  type: string
                                lifecycle:
                                  properties:
                                    postStart:
                                      properties:
                                        exec:
                                          properties:
                                            command:
                                              items:
                                                type: string
                                              type: array
                                          type: object
                                        httpGet:
                                          properties:
                                            host:
                                              type: string
                                            httpHeaders:
                                              items:
                                                properties:
                                                  name:
                                                    type: string
                                                  value:
                                                    type: string
                                                type: object
                                              type: array
                                            path:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                            scheme:
                                              type: string
                                          type: object
                                        sleep:
                                          properties:
                                            seconds:
                                              type: integer
                                          type: object
                                        tcpSocket:
                                          properties:
                                            host:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                          type: object
                                      type: object
                                    preStop:
                                      properties:
                                        exec:
                                          properties:
                                            command:
                                              items:
                                                type: string
                                              type: array
                                          type: object
                                        httpGet:
                                          properties:
                                            host:
                                              type: string
                                            httpHeaders:
                                              items:
                                                properties:
                                                  name:
                                                    type: string
                                                  value:
                                                    type: string
                                                type: object
                                              type: array
                                            path:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                            scheme:
                                              type: string
                                          type: object
                                        sleep:
                                          properties:
                                            seconds:
                                              type: integer
                                          type: object
                                        tcpSocket:
                                          properties:
                                            host:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                          type: object
                                      type: object
                                  type: object
                                livenessProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          type: integer
                                        service:
                                          type: string
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      type: object
                                    initialDelaySeconds:
                                      type: integer
                                    periodSeconds:
                                      type: integer
                                    successThreshold:
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      type: object
                                    terminationGracePeriodSeconds:
                                      type: integer
                                    timeoutSeconds:
                                      type: integer
                                  type: object
                                name:
                                  type: string
                                ports:
                                  items:
                                    properties:
                                      containerPort:
                                        type: integer
                                      hostIP:
                                        type: string
                                      hostPort:
                                        type: integer
                                      name:
                                        type: string
                                      protocol:
                                        type: string
                                    type: object
                                  type: array
                                readinessProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          type: integer
                                        service:
                                          type: string
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      type: object
                                    initialDelaySeconds:
                                      type: integer
                                    periodSeconds:
                                      type: integer
                                    successThreshold:
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      type: object
                                    terminationGracePeriodSeconds:
                                      type: integer
                                    timeoutSeconds:
                                      type: integer
                                  type: object
                                resizePolicy:
                                  items:
                                    properties:
                                      resourceName:
                                        type: string
                                      restartPolicy:
                                        type: string
                                    type: object
                                  type: array
                                resources:
                                  properties:
                                    claims:
                                      items:
                                        properties:
                                          name:
                                            type: string
                                        type: object
                                      type: array
                                    limits:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        x-kubernetes-int-or-string: true
                                      type: object
                                    requests:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        x-kubernetes-int-or-string: true
                                      type: object
                                  type: object
                                restartPolicy:
                                  type: string
                                securityContext:
                                  properties:
                                    allowPrivilegeEscalation:
                                      type: boolean
                                    appArmorProfile:
                                      properties:
                                        localhostProfile:
                                          type: string
                                        type:
                                          type: string
                                      type: object
                                    capabilities:
                                      properties:
                                        add:
                                          items:
                                            type: string
                                          type: array
                                        drop:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    privileged:
                                      type: boolean
                                    procMount:
                                      type: string
                                    readOnlyRootFilesystem:
                                      type: boolean
                                    runAsGroup:
                                      type: integer
                                    runAsNonRoot:
                                      type: boolean
                                    runAsUser:
                                      type: integer
                                    seLinuxOptions:
                                      properties:
                                        level:
                                          type: string
                                        role:
                                          type: string
                                        type:
                                          type: string
                                        user:
                                          type: string
                                      type: object
                                    seccompProfile:
                                      properties:
                                        localhostProfile:
                                          type: string
                                        type:
                                          type: string
                                      type: object
                                    windowsOptions:
                                      properties:
                                        gmsaCredentialSpec:
                                          type: string
                                        gmsaCredentialSpecName:
                                          type: string
                                        hostProcess:
                                          type: boolean
                                        runAsUserName:
                                          type: string
                                      type: object
                                  type: object
                                startupProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          type: integer
                                        service:
                                          type: string
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      type: object
                                    initialDelaySeconds:
                                      type: integer
                                    periodSeconds:
                                      type: integer
                                    successThreshold:
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      type: object
                                    terminationGracePeriodSeconds:
                                      type: integer
                                    timeoutSeconds:
                                      type: integer
                                  type: object
                                stdin:
                                  type: boolean
                                stdinOnce:
                                  type: boolean
                                terminationMessagePath:
                                  type: string
                                terminationMessagePolicy:
                                  type: string
                                tty:
                                  type: boolean
                                volumeDevices:
                                  items:
                                    properties:
                                      devicePath:
                                        type: string
                                      name:
                                        type: string
                                    type: object
                                  type: array
                                volumeMounts:
                                  items:
                                    properties:
                                      mountPath:
                                        type: string
                                      mountPropagation:
                                        type: string
                                      name:
                                        type: string
                                      readOnly:
                                        type: boolean
                                      recursiveReadOnly:
                                        type: string
                                      subPath:
                                        type: string
                                      subPathExpr:
                                        type: string
                                    type: object
                                  type: array
                                workingDir:
                                  type: string
                              type: object
                            type: array
                          nodeName:
                            type: string
                          nodeSelector:
                            additionalProperties:
                              type: string
                            type: object
                          os:
                            properties:
                              name:
                                type: string
                            type: object
                          overhead:
                            additionalProperties:
                              anyOf:
                              - type: integer
                              - type: string
                              x-kubernetes-int-or-string: true
                            type: object
                          preemptionPolicy:
                            type: string
                          priority:
                            type: integer
                          priorityClassName:
                            type: string
                          readinessGates:
                            items:
                              properties:
                                conditionType:
                                  type: string
                              type: object
                            type: array
                          resourceClaims:
                            items:
                              properties:
                                name:
                                  type: string
                                source:
                                  properties:
                                    resourceClaimName:
                                      type: string
                                    resourceClaimTemplateName:
                                      type: string
                                  type: object
                              type: object
                            type: array
                          restartPolicy:
                            type: string
                          runtimeClassName:
                            type: string
                          schedulerName:
                            type: string
                          schedulingGates:
                            items:
                              properties:
                                name:
                                  type: string
                              type: object
                            type: array
                          securityContext:
                            properties:
                              appArmorProfile:
                                properties:
                                  localhostProfile:
                                    type: string
                                  type:
                                    type: string
                                type: object
                              fsGroup:
                                type: integer
                              fsGroupChangePolicy:
                                type: string
                              runAsGroup:
                                type: integer
                              runAsNonRoot:
                                type: boolean
                              runAsUser:
                                type: integer
                              seLinuxOptions:
                                properties:
                                  level:
                                    type: string
                                  role:
                                    type: string
                                  type:
                                    type: string
                                  user:
                                    type: string
                                type: object
                              seccompProfile:
                                properties:
                                  localhostProfile:
                                    type: string
                                  type:
                                    type: string
                                type: object
                              supplementalGroups:
                                items:
                                  type: integer
                                type: array
                              sysctls:
                                items:
                                  properties:
                                    name:
                                      type: string
                                    value:
                                      type: string
                                  type: object
                                type: array
                              windowsOptions:
                                properties:
                                  gmsaCredentialSpec:
                                    type: string
                                  gmsaCredentialSpecName:
                                    type: string
                                  hostProcess:
                                    type: boolean
                                  runAsUserName:
                                    type: string
                                type: object
                            type: object
                          serviceAccount:
                            type: string
                          serviceAccountName:
                            type: string
                          setHostnameAsFQDN:
                            type: boolean
                          shareProcessNamespace:
                            type: boolean
                          subdomain:
                            type: string
                          terminationGracePeriodSeconds:
                            type: integer
                          tolerations:
                            items:
                              properties:
                                effect:
                                  type: string
                                key:
                                  type: string
                                operator:
                                  type: string
                                tolerationSeconds:
                                  type: integer
                                value:
                                  type: string
                              type: object
                            type: array
                          topologySpreadConstraints:
                            items:
                              properties:
                                labelSelector:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                        type: object
                                      type: array
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      type: object
                                  type: object
                                matchLabelKeys:
                                  items:
                                    type: string
                                  type: array
                                maxSkew:
                                  type: integer
                                minDomains:
                                  type: integer
                                nodeAffinityPolicy:
                                  type: string
                                nodeTaintsPolicy:
                                  type: string
                                topologyKey:
                                  type: string
                                whenUnsatisfiable:
                                  type: string
                              type: object
                            type: array
                          volumes:
                            items:
                              properties:
                                awsElasticBlockStore:
                                  properties:
                                    fsType:
                                      type: string
                                    partition:
                                      type: integer
                                    readOnly:
                                      type: boolean
                                    volumeID:
                                      type: string
                                  type: object
                                azureDisk:
                                  properties:
                                    cachingMode:
                                      type: string
                                    diskName:
                                      type: string
                                    diskURI:
                                      type: string
                                    fsType:
                                      type: string
                                    kind:
                                      type: string
                                    readOnly:
                                      type: boolean
                                  type: object
                                azureFile:
                                  properties:
                                    readOnly:
                                      type: boolean
                                    secretName:
                                      type: string
                                    shareName:
                                      type: string
                                  type: object
                                cephfs:
                                  properties:
                                    monitors:
                                      items:
                                        type: string
                                      type: array
                                    path:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    secretFile:
                                      type: string
                                    secretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                    user:
                                      type: string
                                  type: object
                                cinder:
                                  properties:
                                    fsType:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    secretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                    volumeID:
                                      type: string
                                  type: object
                                configMap:
                                  properties:
                                    defaultMode:
                                      type: integer
                                    items:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          mode:
                                            type: integer
                                          path:
                                            type: string
                                        type: object
                                      type: array
                                    name:
                                      type: string
                                    optional:
                                      type: boolean
                                  type: object
                                csi:
                                  properties:
                                    driver:
                                      type: string
                                    fsType:
                                      type: string
                                    nodePublishSecretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                    readOnly:
                                      type: boolean
                                    volumeAttributes:
                                      additionalProperties:
                                        type: string
                                      type: object
                                  type: object
                                downwardAPI:
                                  properties:
                                    defaultMode:
                                      type: integer
                                    items:
                                      items:
                                        properties:
                                          fieldRef:
                                            properties:
                                              apiVersion:
                                                type: string
                                              fieldPath:
                                                type: string
                                            type: object
                                          mode:
                                            type: integer
                                          path:
                                            type: string
                                          resourceFieldRef:
                                            properties:
                                              containerName:
                                                type: string
                                              divisor:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                              resource:
                                                type: string
                                            type: object
                                        type: object
                                      type: array
                                  type: object
                                emptyDir:
                                  properties:
                                    medium:
                                      type: string
                                    sizeLimit:
                                      anyOf:
                                      - type: integer
                                      - type: string
                                      x-kubernetes-int-or-string: true
                                  type: object
                                ephemeral:
                                  properties:
                                    volumeClaimTemplate:
                                      properties:
                                        metadata:
                                          properties:
                                            annotations:
                                              additionalProperties:
                                                type: string
                                              type: object
                                            creationTimestamp:
                                              type: string
                                            deletionGracePeriodSeconds:
                                              type: integer
                                            deletionTimestamp:
                                              type: string
                                            finalizers:
                                              items:
                                                type: string
                                              type: array
                                            generateName:
                                              type: string
                                            generation:
                                              type: integer
                                            labels:
                                              additionalProperties:
                                                type: string
                                              type: object
                                            managedFields:
                                              items:
                                                properties:
                                                  apiVersion:
                                                    type: string
                                                  fieldsType:
                                                    type: string
                                                  fieldsV1:
                                                    type: object
                                                  manager:
                                                    type: string
                                                  operation:
                                                    type: string
                                                  subresource:
                                                    type: string
                                                  time:
                                                    type: string
                                                type: object
                                              type: array
                                            name:
                                              type: string
                                            namespace:
                                              type: string
                                            ownerReferences:
                                              items:
                                                properties:
                                                  apiVersion:
                                                    type: string
                                                  blockOwnerDeletion:
                                                    type: boolean
                                                  controller:
                                                    type: boolean
                                                  kind:
                                                    type: string
                                                  name:
                                                    type: string
                                                  uid:
                                                    type: string
                                                type: object
                                              type: array
                                            resourceVersion:
                                              type: string
                                            selfLink:
                                              type: string
                                            uid:
                                              type: string
                                          type: object
                                        spec:
                                          properties:
                                            accessModes:
                                              items:
                                                type: string
                                              type: array
                                            dataSource:
                                              properties:
                                                apiGroup:
                                                  type: string
                                                kind:
                                                  type: string
                                                name:
                                                  type: string
                                              type: object
                                            dataSourceRef:
                                              properties:
                                                apiGroup:
                                                  type: string
                                                kind:
                                                  type: string
                                                name:
                                                  type: string
                                                namespace:
                                                  type: string
                                              type: object
                                            resources:
                                              properties:
                                                limits:
                                                  additionalProperties:
                                                    anyOf:
                                                    - type: integer
                                                    - type: string
                                                    x-kubernetes-int-or-string: true
                                                  type: object
                                                requests:
                                                  additionalProperties:
                                                    anyOf:
                                                    - type: integer
                                                    - type: string
                                                    x-kubernetes-int-or-string: true
                                                  type: object
                                              type: object
                                            selector:
                                              properties:
                                                matchExpressions:
                                                  items:
                                                    properties:
                                                      key:
                                                        type: string
                                                      operator:
                                                        type: string
                                                      values:
                                                        items:
                                                          type: string
                                                        type: array
                                                    type: object
                                                  type: array
                                                matchLabels:
                                                  additionalProperties:
                                                    type: string
                                                  type: object
                                              type: object
                                            storageClassName:
                                              type: string
                                            volumeAttributesClassName:
                                              type: string
                                            volumeMode:
                                              type: string
                                            volumeName:
                                              type: string
                                          type: object
                                      type: object
                                  type: object
                                fc:
                                  properties:
                                    fsType:
                                      type: string
                                    lun:
                                      type: integer
                                    readOnly:
                                      type: boolean
                                    targetWWNs:
                                      items:
                                        type: string
                                      type: array
                                    wwids:
                                      items:
                                        type: string
                                      type: array
                                  type: object
                                flexVolume:
                                  properties:
                                    driver:
                                      type: string
                                    fsType:
                                      type: string
                                    options:
                                      additionalProperties:
                                        type: string
                                      type: object
                                    readOnly:
                                      type: boolean
                                    secretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                  type: object
                                flocker:
                                  properties:
                                    datasetName:
                                      type: string
                                    datasetUUID:
                                      type: string
                                  type: object
                                gcePersistentDisk:
                                  properties:
                                    fsType:
                                      type: string
                                    partition:
                                      type: integer
                                    pdName:
                                      type: string
                                    readOnly:
                                      type: boolean
                                  type: object
                                gitRepo:
                                  properties:
                                    directory:
                                      type: string
                                    repository:
                                      type: string
                                    revision:
                                      type: string
                                  type: object
                                glusterfs:
                                  properties:
                                    endpoints:
                                      type: string
                                    path:
                                      type: string
                                    readOnly:
                                      type: boolean
                                  type: object
                                hostPath:
                                  properties:
                                    path:
                                      type: string
                                    type:
                                      type: string
                                  type: object
                                iscsi:
                                  properties:
                                    chapAuthDiscovery:
                                      type: boolean
                                    chapAuthSession:
                                      type: boolean
                                    fsType:
                                      type: string
                                    initiatorName:
                                      type: string
                                    iqn:
                                      type: string
                                    iscsiInterface:
                                      type: string
                                    lun:
                                      type: integer
                                    portals:
                                      items:
                                        type: string
                                      type: array
                                    readOnly:
                                      type: boolean
                                    secretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                    targetPortal:
                                      type: string
                                  type: object
                                name:
                                  type: string
                                nfs:
                                  properties:
                                    path:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    server:
                                      type: string
                                  type: object
                                persistentVolumeClaim:
                                  properties:
                                    claimName:
                                      type: string
                                    readOnly:
                                      type: boolean
                                  type: object
                                photonPersistentDisk:
                                  properties:
                                    fsType:
                                      type: string
                                    pdID:
                                      type: string
                                  type: object
                                portworxVolume:
                                  properties:
                                    fsType:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    volumeID:
                                      type: string
                                  type: object
                                projected:
                                  properties:
                                    defaultMode:
                                      type: integer
                                    sources:
                                      items:
                                        properties:
                                          clusterTrustBundle:
                                            properties:
                                              labelSelector:
                                                properties:
                                                  matchExpressions:
                                                    items:
                                                      properties:
                                                        key:
                                                          type: string
                                                        operator:
                                                          type: string
                                                        values:
                                                          items:
                                                            type: string
                                                          type: array
                                                      type: object
                                                    type: array
                                                  matchLabels:
                                                    additionalProperties:
                                                      type: string
                                                    type: object
                                                type: object
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                              path:
                                                type: string
                                              signerName:
                                                type: string
                                            type: object
                                          configMap:
                                            properties:
                                              items:
                                                items:
                                                  properties:
                                                    key:
                                                      type: string
                                                    mode:
                                                      type: integer
                                                    path:
                                                      type: string
                                                  type: object
                                                type: array
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            type: object
                                          downwardAPI:
                                            properties:
                                              items:
                                                items:
                                                  properties:
                                                    fieldRef:
                                                      properties:
                                                        apiVersion:
                                                          type: string
                                                        fieldPath:
                                                          type: string
                                                      type: object
                                                    mode:
                                                      type: integer
                                                    path:
                                                      type: string
                                                    resourceFieldRef:
                                                      properties:
                                                        containerName:
                                                          type: string
                                                        divisor:
                                                          anyOf:
                                                          - type: integer
                                                          - type: string
                                                          x-kubernetes-int-or-string: true
                                                        resource:
                                                          type: string
                                                      type: object
                                                  type: object
                                                type: array
                                            type: object
                                          secret:
                                            properties:
                                              items:
                                                items:
                                                  properties:
                                                    key:
                                                      type: string
                                                    mode:
                                                      type: integer
                                                    path:
                                                      type: string
                                                  type: object
                                                type: array
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            type: object
                                          serviceAccountToken:
                                            properties:
                                              audience:
                                                type: string
                                              expirationSeconds:
                                                type: integer
                                              path:
                                                type: string
                                            type: object
                                        type: object
                                      type: array
                                  type: object
                                quobyte:
                                  properties:
                                    group:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    registry:
                                      type: string
                                    tenant:
                                      type: string
                                    user:
                                      type: string
                                    volume:
                                      type: string
                                  type: object
                                rbd:
                                  properties:
                                    fsType:
                                      type: string
                                    image:
                                      type: string
                                    keyring:
                                      type: string
                                    monitors:
                                      items:
                                        type: string
                                      type: array
                                    pool:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    secretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                    user:
                                      type: string
                                  type: object
                                scaleIO:
                                  properties:
                                    fsType:
                                      type: string
                                    gateway:
                                      type: string
                                    protectionDomain:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    secretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                    sslEnabled:
                                      type: boolean
                                    storageMode:
                                      type: string
                                    storagePool:
                                      type: string
                                    system:
                                      type: string
                                    volumeName:
                                      type: string
                                  type: object
                                secret:
                                  properties:
                                    defaultMode:
                                      type: integer
                                    items:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          mode:
                                            type: integer
                                          path:
                                            type: string
                                        type: object
                                      type: array
                                    optional:
                                      type: boolean
                                    secretName:
                                      type: string
                                  type: object
                                storageos:
                                  properties:
                                    fsType:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    secretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                    volumeName:
                                      type: string
                                    volumeNamespace:
                                      type: string
                                  type: object
                                vsphereVolume:
                                  properties:
                                    fsType:
                                      type: string
                                    storagePolicyID:
                                      type: string
                                    storagePolicyName:
                                      type: string
                                    volumePath:
                                      type: string
                                  type: object
                              type: object
                            type: array
                        type: object
                    type: object
                  replicas:
                    description: Number of replicas for the component
                    type: integer
                  sql:
                    description: |-
                      DEPRECATED: Use the `app.storage.type` and `app.storage.sql` fields instead.
                      The operator will attempt to update the fields automatically.
                    properties:
                      dataSource:
                        description: 'DEPRECATED: Use the `app.storage.sql.dataSource`
                          field instead. The operator will attempt to update the field
                          automatically.'
                        properties:
                          password:
                            description: 'DEPRECATED: Use the `app.storage.sql.dataSource.password`
                              field instead. The operator will attempt to update the
                              field automatically.'
                            type: string
                          url:
                            description: 'DEPRECATED: Use the `app.storage.sql.dataSource.url`
                              field instead. The operator will attempt to update the
                              field automatically.'
                            type: string
                          username:
                            description: 'DEPRECATED: Use the `app.storage.sql.dataSource.username`
                              field instead. The operator will attempt to update the
                              field automatically.'
                            type: string
                        type: object
                    type: object
                  storage:
                    description: |
                      Configure storage for Apicurio Registry backend (app).
                    properties:
                      kafkasql:
                        description: Configure KafkaSQL storage type.
                        properties:
                          auth:
                            description: Configure KafkaSQL storage authentication.
                            properties:
                              clientIdRef:
                                description: The clientId used to authenticate to
                                  Kafka.
                                properties:
                                  key:
                                    description: Name of the key in the referenced
                                      Secret that contain the target data. This field
                                      might be optional if a default value has been
                                      defined.
                                    type: string
                                  name:
                                    description: Name of a Secret that is being referenced.
                                    type: string
                                type: object
                              clientSecretRef:
                                description: The client secret used to authenticate
                                  to Kafka.
                                properties:
                                  key:
                                    description: Name of the key in the referenced
                                      Secret that contain the target data. This field
                                      might be optional if a default value has been
                                      defined.
                                    type: string
                                  name:
                                    description: Name of a Secret that is being referenced.
                                    type: string
                                type: object
                              enabled:
                                description: Enables SASL OAuth authentication for
                                  Apicurio Registry storage in Kafka. You must set
                                  this variable to true for the other variables to
                                  have effect.
                                type: boolean
                              loginHandlerClass:
                                description: The login class to be used for login.
                                type: string
                              mechanism:
                                description: The mechanism used to authenticate to
                                  Kafka.
                                type: string
                              tokenEndpoint:
                                description: The URL of the OAuth identity server.
                                type: string
                            type: object
                          bootstrapServers:
                            description: |-
                              Configure Kafka bootstrap servers.

                              Required if `app.storage.type` is `kafkasql`.
                            type: string
                          tls:
                            description: Configure KafkaSQL storage when the access
                              to the Kafka cluster is secured using TLS.
                            properties:
                              keystorePasswordSecretRef:
                                description: |-
                                  Reference to the Secret that contains the TLS keystore password.
                                  Key `user.password` is assumed by default.
                                properties:
                                  key:
                                    description: Name of the key in the referenced
                                      Secret that contain the target data. This field
                                      might be optional if a default value has been
                                      defined.
                                    type: string
                                  name:
                                    description: Name of a Secret that is being referenced.
                                    type: string
                                type: object
                              keystoreSecretRef:
                                description: Reference to the Secret that contains
                                  the TLS keystore (in PKCS12 format). Key `user.p12`
                                  is assumed by default.
                                properties:
                                  key:
                                    description: Name of the key in the referenced
                                      Secret that contain the target data. This field
                                      might be optional if a default value has been
                                      defined.
                                    type: string
                                  name:
                                    description: Name of a Secret that is being referenced.
                                    type: string
                                type: object
                              truststorePasswordSecretRef:
                                description: Name of a Secret that contains the TLS
                                  truststore password. Key `ca.password` is assumed
                                  by default.
                                properties:
                                  key:
                                    description: Name of the key in the referenced
                                      Secret that contain the target data. This field
                                      might be optional if a default value has been
                                      defined.
                                    type: string
                                  name:
                                    description: Name of a Secret that is being referenced.
                                    type: string
                                type: object
                              truststoreSecretRef:
                                description: Name of a Secret that contains the TLS
                                  truststore (in PKCS12 format). Key `ca.p12` is assumed
                                  by default.
                                properties:
                                  key:
                                    description: Name of the key in the referenced
                                      Secret that contain the target data. This field
                                      might be optional if a default value has been
                                      defined.
                                    type: string
                                  name:
                                    description: Name of a Secret that is being referenced.
                                    type: string
                                type: object
                            type: object
                        type: object
                      sql:
                        description: Configure SQL storage types.
                        properties:
                          dataSource:
                            description: Configure SQL data source.
                            properties:
                              password:
                                description: |-
                                  Configure SQL data source password.

                                  References name of a Secret that contains the password. Key `password` is assumed by default.
                                properties:
                                  key:
                                    description: Name of the key in the referenced
                                      Secret that contain the target data. This field
                                      might be optional if a default value has been
                                      defined.
                                    type: string
                                  name:
                                    description: Name of a Secret that is being referenced.
                                    type: string
                                type: object
                              url:
                                description: |-
                                  Configure SQL data source URL.

                                  Example: `***********************************************************`.
                                type: string
                              username:
                                description: Configure SQL data source username.
                                type: string
                            type: object
                        type: object
                      type:
                        description: |-
                          Configure type of storage that Apicurio Registry backend (app) will use:

                            * <empty> - in-memory storage.
                            * `postgresql` - Postgresql storage type, must be further configured using the `app.storage.sql` field.
                            * `kafkasql` - KafkaSQL storage type, must be further configured using the `app.storage.kafkasql` field.

                          IMPORTANT: Defaults to the in-memory storage, which is not suitable for production.
                        enum:
                        - kafkasql
                        - postgresql
                        type: string
                    type: object
                  tls:
                    description: |
                      Configure tls of Apicurio Registry.
                    properties:
                      insecureRequests:
                        description: |
                          If insecure (i.e. http rather than https) requests are allowed. If this is `enabled` then http works as normal. `redirect` will still open the http port, but all requests will be redirected to the HTTPS port. `disabled` will prevent the HTTP port from opening at all.
                        enum:
                        - disabled
                        - enabled
                        - redirect
                        type: string
                      keystorePasswordSecretRef:
                        description: Name of a Secret that contains the TLS keystore
                          password. Key `ca.password` is assumed by default.
                        properties:
                          key:
                            description: Name of the key in the referenced Secret
                              that contain the target data. This field might be optional
                              if a default value has been defined.
                            type: string
                          name:
                            description: Name of a Secret that is being referenced.
                            type: string
                        type: object
                      keystoreSecretRef:
                        description: Name of a Secret that contains the TLS keystore
                          (in PKCS12 format). Key `ca.p12` is assumed by default.
                        properties:
                          key:
                            description: Name of the key in the referenced Secret
                              that contain the target data. This field might be optional
                              if a default value has been defined.
                            type: string
                          name:
                            description: Name of a Secret that is being referenced.
                            type: string
                        type: object
                      truststorePasswordSecretRef:
                        description: Name of a Secret that contains the TLS truststore
                          password. Key `ca.password` is assumed by default.
                        properties:
                          key:
                            description: Name of the key in the referenced Secret
                              that contain the target data. This field might be optional
                              if a default value has been defined.
                            type: string
                          name:
                            description: Name of a Secret that is being referenced.
                            type: string
                        type: object
                      truststoreSecretRef:
                        description: Name of a Secret that contains the TLS truststore
                          (in PKCS12 format). Key `ca.p12` is assumed by default.
                        properties:
                          key:
                            description: Name of the key in the referenced Secret
                              that contain the target data. This field might be optional
                              if a default value has been defined.
                            type: string
                          name:
                            description: Name of a Secret that is being referenced.
                            type: string
                        type: object
                    type: object
                type: object
              ui:
                description: |
                  Configure Apicurio Registry UI component.
                properties:
                  enabled:
                    description: |
                      Indicates whether the operator should deploy the UI component.  Defaults to `true`.
                    type: boolean
                  env:
                    description: Configure a list of environment variables that will
                      be passed to this components' container.
                    items:
                      properties:
                        name:
                          type: string
                        value:
                          type: string
                        valueFrom:
                          properties:
                            configMapKeyRef:
                              properties:
                                key:
                                  type: string
                                name:
                                  type: string
                                optional:
                                  type: boolean
                              type: object
                            fieldRef:
                              properties:
                                apiVersion:
                                  type: string
                                fieldPath:
                                  type: string
                              type: object
                            resourceFieldRef:
                              properties:
                                containerName:
                                  type: string
                                divisor:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  x-kubernetes-int-or-string: true
                                resource:
                                  type: string
                              type: object
                            secretKeyRef:
                              properties:
                                key:
                                  type: string
                                name:
                                  type: string
                                optional:
                                  type: boolean
                              type: object
                          type: object
                      type: object
                    type: array
                  host:
                    description: 'DEPRECATED: Use the `(component).ingress.host` field
                      instead. The operator will attempt to update the field automatically.'
                    type: string
                  ingress:
                    description: Configure Ingress for the component.
                    properties:
                      enabled:
                        description: |
                          Whether an Ingress should be managed by the operator.  Defaults to 'true'.

                          Set this to 'false' if you want to create your own custom Ingress.
                        type: boolean
                      host:
                        description: |-
                          Configure hostname of the operator-managed Ingress. If the value is empty, the operator will not create an Ingress resource for the component.

                          IMPORTANT: If the Ingress already exists and the value becomes empty, the Ingress will be deleted.
                        type: string
                    type: object
                  networkPolicy:
                    description: |2
                              Configuration of a NetworkPolicy for the component.
                    properties:
                      enabled:
                        description: |
                          Whether a NetworkPolicy should be managed by the operator.  Defaults to 'true'.

                          Set this to 'false' if you want to create your own custom NetworkPolicy.
                        type: boolean
                    type: object
                  podDisruptionBudget:
                    description: |
                      Configuration of a PodDisruptionBudget for the component.
                    properties:
                      enabled:
                        description: |
                          Whether a PodDisruptionBudget should be managed by the operator.  Defaults to 'true'.

                          Set this to 'false' if you want to create your own custom PodDisruptionBudget.
                        type: boolean
                    type: object
                  podTemplateSpec:
                    description: |-
                      `PodTemplateSpec` describes the data a pod should have when created from a template.

                      This template is used by the operator to create the components' Deployment. The operator first extends the template
                      with default values if required, and then applies additional configuration
                      based on the other contents of `ApicurioRegistry3` CR.
                    properties:
                      metadata:
                        properties:
                          annotations:
                            additionalProperties:
                              type: string
                            type: object
                          creationTimestamp:
                            type: string
                          deletionGracePeriodSeconds:
                            type: integer
                          deletionTimestamp:
                            type: string
                          finalizers:
                            items:
                              type: string
                            type: array
                          generateName:
                            type: string
                          generation:
                            type: integer
                          labels:
                            additionalProperties:
                              type: string
                            type: object
                          managedFields:
                            items:
                              properties:
                                apiVersion:
                                  type: string
                                fieldsType:
                                  type: string
                                fieldsV1:
                                  type: object
                                manager:
                                  type: string
                                operation:
                                  type: string
                                subresource:
                                  type: string
                                time:
                                  type: string
                              type: object
                            type: array
                          name:
                            type: string
                          namespace:
                            type: string
                          ownerReferences:
                            items:
                              properties:
                                apiVersion:
                                  type: string
                                blockOwnerDeletion:
                                  type: boolean
                                controller:
                                  type: boolean
                                kind:
                                  type: string
                                name:
                                  type: string
                                uid:
                                  type: string
                              type: object
                            type: array
                          resourceVersion:
                            type: string
                          selfLink:
                            type: string
                          uid:
                            type: string
                        type: object
                      spec:
                        properties:
                          activeDeadlineSeconds:
                            type: integer
                          affinity:
                            properties:
                              nodeAffinity:
                                properties:
                                  preferredDuringSchedulingIgnoredDuringExecution:
                                    items:
                                      properties:
                                        preference:
                                          properties:
                                            matchExpressions:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                type: object
                                              type: array
                                            matchFields:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                type: object
                                              type: array
                                          type: object
                                        weight:
                                          type: integer
                                      type: object
                                    type: array
                                  requiredDuringSchedulingIgnoredDuringExecution:
                                    properties:
                                      nodeSelectorTerms:
                                        items:
                                          properties:
                                            matchExpressions:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                type: object
                                              type: array
                                            matchFields:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                type: object
                                              type: array
                                          type: object
                                        type: array
                                    type: object
                                type: object
                              podAffinity:
                                properties:
                                  preferredDuringSchedulingIgnoredDuringExecution:
                                    items:
                                      properties:
                                        podAffinityTerm:
                                          properties:
                                            labelSelector:
                                              properties:
                                                matchExpressions:
                                                  items:
                                                    properties:
                                                      key:
                                                        type: string
                                                      operator:
                                                        type: string
                                                      values:
                                                        items:
                                                          type: string
                                                        type: array
                                                    type: object
                                                  type: array
                                                matchLabels:
                                                  additionalProperties:
                                                    type: string
                                                  type: object
                                              type: object
                                            matchLabelKeys:
                                              items:
                                                type: string
                                              type: array
                                            mismatchLabelKeys:
                                              items:
                                                type: string
                                              type: array
                                            namespaceSelector:
                                              properties:
                                                matchExpressions:
                                                  items:
                                                    properties:
                                                      key:
                                                        type: string
                                                      operator:
                                                        type: string
                                                      values:
                                                        items:
                                                          type: string
                                                        type: array
                                                    type: object
                                                  type: array
                                                matchLabels:
                                                  additionalProperties:
                                                    type: string
                                                  type: object
                                              type: object
                                            namespaces:
                                              items:
                                                type: string
                                              type: array
                                            topologyKey:
                                              type: string
                                          type: object
                                        weight:
                                          type: integer
                                      type: object
                                    type: array
                                  requiredDuringSchedulingIgnoredDuringExecution:
                                    items:
                                      properties:
                                        labelSelector:
                                          properties:
                                            matchExpressions:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                type: object
                                              type: array
                                            matchLabels:
                                              additionalProperties:
                                                type: string
                                              type: object
                                          type: object
                                        matchLabelKeys:
                                          items:
                                            type: string
                                          type: array
                                        mismatchLabelKeys:
                                          items:
                                            type: string
                                          type: array
                                        namespaceSelector:
                                          properties:
                                            matchExpressions:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                type: object
                                              type: array
                                            matchLabels:
                                              additionalProperties:
                                                type: string
                                              type: object
                                          type: object
                                        namespaces:
                                          items:
                                            type: string
                                          type: array
                                        topologyKey:
                                          type: string
                                      type: object
                                    type: array
                                type: object
                              podAntiAffinity:
                                properties:
                                  preferredDuringSchedulingIgnoredDuringExecution:
                                    items:
                                      properties:
                                        podAffinityTerm:
                                          properties:
                                            labelSelector:
                                              properties:
                                                matchExpressions:
                                                  items:
                                                    properties:
                                                      key:
                                                        type: string
                                                      operator:
                                                        type: string
                                                      values:
                                                        items:
                                                          type: string
                                                        type: array
                                                    type: object
                                                  type: array
                                                matchLabels:
                                                  additionalProperties:
                                                    type: string
                                                  type: object
                                              type: object
                                            matchLabelKeys:
                                              items:
                                                type: string
                                              type: array
                                            mismatchLabelKeys:
                                              items:
                                                type: string
                                              type: array
                                            namespaceSelector:
                                              properties:
                                                matchExpressions:
                                                  items:
                                                    properties:
                                                      key:
                                                        type: string
                                                      operator:
                                                        type: string
                                                      values:
                                                        items:
                                                          type: string
                                                        type: array
                                                    type: object
                                                  type: array
                                                matchLabels:
                                                  additionalProperties:
                                                    type: string
                                                  type: object
                                              type: object
                                            namespaces:
                                              items:
                                                type: string
                                              type: array
                                            topologyKey:
                                              type: string
                                          type: object
                                        weight:
                                          type: integer
                                      type: object
                                    type: array
                                  requiredDuringSchedulingIgnoredDuringExecution:
                                    items:
                                      properties:
                                        labelSelector:
                                          properties:
                                            matchExpressions:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                type: object
                                              type: array
                                            matchLabels:
                                              additionalProperties:
                                                type: string
                                              type: object
                                          type: object
                                        matchLabelKeys:
                                          items:
                                            type: string
                                          type: array
                                        mismatchLabelKeys:
                                          items:
                                            type: string
                                          type: array
                                        namespaceSelector:
                                          properties:
                                            matchExpressions:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                type: object
                                              type: array
                                            matchLabels:
                                              additionalProperties:
                                                type: string
                                              type: object
                                          type: object
                                        namespaces:
                                          items:
                                            type: string
                                          type: array
                                        topologyKey:
                                          type: string
                                      type: object
                                    type: array
                                type: object
                            type: object
                          automountServiceAccountToken:
                            type: boolean
                          containers:
                            items:
                              properties:
                                args:
                                  items:
                                    type: string
                                  type: array
                                command:
                                  items:
                                    type: string
                                  type: array
                                env:
                                  items:
                                    properties:
                                      name:
                                        type: string
                                      value:
                                        type: string
                                      valueFrom:
                                        properties:
                                          configMapKeyRef:
                                            properties:
                                              key:
                                                type: string
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            type: object
                                          fieldRef:
                                            properties:
                                              apiVersion:
                                                type: string
                                              fieldPath:
                                                type: string
                                            type: object
                                          resourceFieldRef:
                                            properties:
                                              containerName:
                                                type: string
                                              divisor:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                              resource:
                                                type: string
                                            type: object
                                          secretKeyRef:
                                            properties:
                                              key:
                                                type: string
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            type: object
                                        type: object
                                    type: object
                                  type: array
                                envFrom:
                                  items:
                                    properties:
                                      configMapRef:
                                        properties:
                                          name:
                                            type: string
                                          optional:
                                            type: boolean
                                        type: object
                                      prefix:
                                        type: string
                                      secretRef:
                                        properties:
                                          name:
                                            type: string
                                          optional:
                                            type: boolean
                                        type: object
                                    type: object
                                  type: array
                                image:
                                  type: string
                                imagePullPolicy:
                                  type: string
                                lifecycle:
                                  properties:
                                    postStart:
                                      properties:
                                        exec:
                                          properties:
                                            command:
                                              items:
                                                type: string
                                              type: array
                                          type: object
                                        httpGet:
                                          properties:
                                            host:
                                              type: string
                                            httpHeaders:
                                              items:
                                                properties:
                                                  name:
                                                    type: string
                                                  value:
                                                    type: string
                                                type: object
                                              type: array
                                            path:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                            scheme:
                                              type: string
                                          type: object
                                        sleep:
                                          properties:
                                            seconds:
                                              type: integer
                                          type: object
                                        tcpSocket:
                                          properties:
                                            host:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                          type: object
                                      type: object
                                    preStop:
                                      properties:
                                        exec:
                                          properties:
                                            command:
                                              items:
                                                type: string
                                              type: array
                                          type: object
                                        httpGet:
                                          properties:
                                            host:
                                              type: string
                                            httpHeaders:
                                              items:
                                                properties:
                                                  name:
                                                    type: string
                                                  value:
                                                    type: string
                                                type: object
                                              type: array
                                            path:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                            scheme:
                                              type: string
                                          type: object
                                        sleep:
                                          properties:
                                            seconds:
                                              type: integer
                                          type: object
                                        tcpSocket:
                                          properties:
                                            host:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                          type: object
                                      type: object
                                  type: object
                                livenessProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          type: integer
                                        service:
                                          type: string
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      type: object
                                    initialDelaySeconds:
                                      type: integer
                                    periodSeconds:
                                      type: integer
                                    successThreshold:
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      type: object
                                    terminationGracePeriodSeconds:
                                      type: integer
                                    timeoutSeconds:
                                      type: integer
                                  type: object
                                name:
                                  type: string
                                ports:
                                  items:
                                    properties:
                                      containerPort:
                                        type: integer
                                      hostIP:
                                        type: string
                                      hostPort:
                                        type: integer
                                      name:
                                        type: string
                                      protocol:
                                        type: string
                                    type: object
                                  type: array
                                readinessProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          type: integer
                                        service:
                                          type: string
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      type: object
                                    initialDelaySeconds:
                                      type: integer
                                    periodSeconds:
                                      type: integer
                                    successThreshold:
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      type: object
                                    terminationGracePeriodSeconds:
                                      type: integer
                                    timeoutSeconds:
                                      type: integer
                                  type: object
                                resizePolicy:
                                  items:
                                    properties:
                                      resourceName:
                                        type: string
                                      restartPolicy:
                                        type: string
                                    type: object
                                  type: array
                                resources:
                                  properties:
                                    claims:
                                      items:
                                        properties:
                                          name:
                                            type: string
                                        type: object
                                      type: array
                                    limits:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        x-kubernetes-int-or-string: true
                                      type: object
                                    requests:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        x-kubernetes-int-or-string: true
                                      type: object
                                  type: object
                                restartPolicy:
                                  type: string
                                securityContext:
                                  properties:
                                    allowPrivilegeEscalation:
                                      type: boolean
                                    appArmorProfile:
                                      properties:
                                        localhostProfile:
                                          type: string
                                        type:
                                          type: string
                                      type: object
                                    capabilities:
                                      properties:
                                        add:
                                          items:
                                            type: string
                                          type: array
                                        drop:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    privileged:
                                      type: boolean
                                    procMount:
                                      type: string
                                    readOnlyRootFilesystem:
                                      type: boolean
                                    runAsGroup:
                                      type: integer
                                    runAsNonRoot:
                                      type: boolean
                                    runAsUser:
                                      type: integer
                                    seLinuxOptions:
                                      properties:
                                        level:
                                          type: string
                                        role:
                                          type: string
                                        type:
                                          type: string
                                        user:
                                          type: string
                                      type: object
                                    seccompProfile:
                                      properties:
                                        localhostProfile:
                                          type: string
                                        type:
                                          type: string
                                      type: object
                                    windowsOptions:
                                      properties:
                                        gmsaCredentialSpec:
                                          type: string
                                        gmsaCredentialSpecName:
                                          type: string
                                        hostProcess:
                                          type: boolean
                                        runAsUserName:
                                          type: string
                                      type: object
                                  type: object
                                startupProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          type: integer
                                        service:
                                          type: string
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      type: object
                                    initialDelaySeconds:
                                      type: integer
                                    periodSeconds:
                                      type: integer
                                    successThreshold:
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      type: object
                                    terminationGracePeriodSeconds:
                                      type: integer
                                    timeoutSeconds:
                                      type: integer
                                  type: object
                                stdin:
                                  type: boolean
                                stdinOnce:
                                  type: boolean
                                terminationMessagePath:
                                  type: string
                                terminationMessagePolicy:
                                  type: string
                                tty:
                                  type: boolean
                                volumeDevices:
                                  items:
                                    properties:
                                      devicePath:
                                        type: string
                                      name:
                                        type: string
                                    type: object
                                  type: array
                                volumeMounts:
                                  items:
                                    properties:
                                      mountPath:
                                        type: string
                                      mountPropagation:
                                        type: string
                                      name:
                                        type: string
                                      readOnly:
                                        type: boolean
                                      recursiveReadOnly:
                                        type: string
                                      subPath:
                                        type: string
                                      subPathExpr:
                                        type: string
                                    type: object
                                  type: array
                                workingDir:
                                  type: string
                              type: object
                            type: array
                          dnsConfig:
                            properties:
                              nameservers:
                                items:
                                  type: string
                                type: array
                              options:
                                items:
                                  properties:
                                    name:
                                      type: string
                                    value:
                                      type: string
                                  type: object
                                type: array
                              searches:
                                items:
                                  type: string
                                type: array
                            type: object
                          dnsPolicy:
                            type: string
                          enableServiceLinks:
                            type: boolean
                          ephemeralContainers:
                            items:
                              properties:
                                args:
                                  items:
                                    type: string
                                  type: array
                                command:
                                  items:
                                    type: string
                                  type: array
                                env:
                                  items:
                                    properties:
                                      name:
                                        type: string
                                      value:
                                        type: string
                                      valueFrom:
                                        properties:
                                          configMapKeyRef:
                                            properties:
                                              key:
                                                type: string
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            type: object
                                          fieldRef:
                                            properties:
                                              apiVersion:
                                                type: string
                                              fieldPath:
                                                type: string
                                            type: object
                                          resourceFieldRef:
                                            properties:
                                              containerName:
                                                type: string
                                              divisor:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                              resource:
                                                type: string
                                            type: object
                                          secretKeyRef:
                                            properties:
                                              key:
                                                type: string
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            type: object
                                        type: object
                                    type: object
                                  type: array
                                envFrom:
                                  items:
                                    properties:
                                      configMapRef:
                                        properties:
                                          name:
                                            type: string
                                          optional:
                                            type: boolean
                                        type: object
                                      prefix:
                                        type: string
                                      secretRef:
                                        properties:
                                          name:
                                            type: string
                                          optional:
                                            type: boolean
                                        type: object
                                    type: object
                                  type: array
                                image:
                                  type: string
                                imagePullPolicy:
                                  type: string
                                lifecycle:
                                  properties:
                                    postStart:
                                      properties:
                                        exec:
                                          properties:
                                            command:
                                              items:
                                                type: string
                                              type: array
                                          type: object
                                        httpGet:
                                          properties:
                                            host:
                                              type: string
                                            httpHeaders:
                                              items:
                                                properties:
                                                  name:
                                                    type: string
                                                  value:
                                                    type: string
                                                type: object
                                              type: array
                                            path:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                            scheme:
                                              type: string
                                          type: object
                                        sleep:
                                          properties:
                                            seconds:
                                              type: integer
                                          type: object
                                        tcpSocket:
                                          properties:
                                            host:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                          type: object
                                      type: object
                                    preStop:
                                      properties:
                                        exec:
                                          properties:
                                            command:
                                              items:
                                                type: string
                                              type: array
                                          type: object
                                        httpGet:
                                          properties:
                                            host:
                                              type: string
                                            httpHeaders:
                                              items:
                                                properties:
                                                  name:
                                                    type: string
                                                  value:
                                                    type: string
                                                type: object
                                              type: array
                                            path:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                            scheme:
                                              type: string
                                          type: object
                                        sleep:
                                          properties:
                                            seconds:
                                              type: integer
                                          type: object
                                        tcpSocket:
                                          properties:
                                            host:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                          type: object
                                      type: object
                                  type: object
                                livenessProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          type: integer
                                        service:
                                          type: string
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      type: object
                                    initialDelaySeconds:
                                      type: integer
                                    periodSeconds:
                                      type: integer
                                    successThreshold:
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      type: object
                                    terminationGracePeriodSeconds:
                                      type: integer
                                    timeoutSeconds:
                                      type: integer
                                  type: object
                                name:
                                  type: string
                                ports:
                                  items:
                                    properties:
                                      containerPort:
                                        type: integer
                                      hostIP:
                                        type: string
                                      hostPort:
                                        type: integer
                                      name:
                                        type: string
                                      protocol:
                                        type: string
                                    type: object
                                  type: array
                                readinessProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          type: integer
                                        service:
                                          type: string
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      type: object
                                    initialDelaySeconds:
                                      type: integer
                                    periodSeconds:
                                      type: integer
                                    successThreshold:
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      type: object
                                    terminationGracePeriodSeconds:
                                      type: integer
                                    timeoutSeconds:
                                      type: integer
                                  type: object
                                resizePolicy:
                                  items:
                                    properties:
                                      resourceName:
                                        type: string
                                      restartPolicy:
                                        type: string
                                    type: object
                                  type: array
                                resources:
                                  properties:
                                    claims:
                                      items:
                                        properties:
                                          name:
                                            type: string
                                        type: object
                                      type: array
                                    limits:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        x-kubernetes-int-or-string: true
                                      type: object
                                    requests:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        x-kubernetes-int-or-string: true
                                      type: object
                                  type: object
                                restartPolicy:
                                  type: string
                                securityContext:
                                  properties:
                                    allowPrivilegeEscalation:
                                      type: boolean
                                    appArmorProfile:
                                      properties:
                                        localhostProfile:
                                          type: string
                                        type:
                                          type: string
                                      type: object
                                    capabilities:
                                      properties:
                                        add:
                                          items:
                                            type: string
                                          type: array
                                        drop:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    privileged:
                                      type: boolean
                                    procMount:
                                      type: string
                                    readOnlyRootFilesystem:
                                      type: boolean
                                    runAsGroup:
                                      type: integer
                                    runAsNonRoot:
                                      type: boolean
                                    runAsUser:
                                      type: integer
                                    seLinuxOptions:
                                      properties:
                                        level:
                                          type: string
                                        role:
                                          type: string
                                        type:
                                          type: string
                                        user:
                                          type: string
                                      type: object
                                    seccompProfile:
                                      properties:
                                        localhostProfile:
                                          type: string
                                        type:
                                          type: string
                                      type: object
                                    windowsOptions:
                                      properties:
                                        gmsaCredentialSpec:
                                          type: string
                                        gmsaCredentialSpecName:
                                          type: string
                                        hostProcess:
                                          type: boolean
                                        runAsUserName:
                                          type: string
                                      type: object
                                  type: object
                                startupProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          type: integer
                                        service:
                                          type: string
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      type: object
                                    initialDelaySeconds:
                                      type: integer
                                    periodSeconds:
                                      type: integer
                                    successThreshold:
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      type: object
                                    terminationGracePeriodSeconds:
                                      type: integer
                                    timeoutSeconds:
                                      type: integer
                                  type: object
                                stdin:
                                  type: boolean
                                stdinOnce:
                                  type: boolean
                                targetContainerName:
                                  type: string
                                terminationMessagePath:
                                  type: string
                                terminationMessagePolicy:
                                  type: string
                                tty:
                                  type: boolean
                                volumeDevices:
                                  items:
                                    properties:
                                      devicePath:
                                        type: string
                                      name:
                                        type: string
                                    type: object
                                  type: array
                                volumeMounts:
                                  items:
                                    properties:
                                      mountPath:
                                        type: string
                                      mountPropagation:
                                        type: string
                                      name:
                                        type: string
                                      readOnly:
                                        type: boolean
                                      recursiveReadOnly:
                                        type: string
                                      subPath:
                                        type: string
                                      subPathExpr:
                                        type: string
                                    type: object
                                  type: array
                                workingDir:
                                  type: string
                              type: object
                            type: array
                          hostAliases:
                            items:
                              properties:
                                hostnames:
                                  items:
                                    type: string
                                  type: array
                                ip:
                                  type: string
                              type: object
                            type: array
                          hostIPC:
                            type: boolean
                          hostNetwork:
                            type: boolean
                          hostPID:
                            type: boolean
                          hostUsers:
                            type: boolean
                          hostname:
                            type: string
                          imagePullSecrets:
                            items:
                              properties:
                                name:
                                  type: string
                              type: object
                            type: array
                          initContainers:
                            items:
                              properties:
                                args:
                                  items:
                                    type: string
                                  type: array
                                command:
                                  items:
                                    type: string
                                  type: array
                                env:
                                  items:
                                    properties:
                                      name:
                                        type: string
                                      value:
                                        type: string
                                      valueFrom:
                                        properties:
                                          configMapKeyRef:
                                            properties:
                                              key:
                                                type: string
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            type: object
                                          fieldRef:
                                            properties:
                                              apiVersion:
                                                type: string
                                              fieldPath:
                                                type: string
                                            type: object
                                          resourceFieldRef:
                                            properties:
                                              containerName:
                                                type: string
                                              divisor:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                              resource:
                                                type: string
                                            type: object
                                          secretKeyRef:
                                            properties:
                                              key:
                                                type: string
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            type: object
                                        type: object
                                    type: object
                                  type: array
                                envFrom:
                                  items:
                                    properties:
                                      configMapRef:
                                        properties:
                                          name:
                                            type: string
                                          optional:
                                            type: boolean
                                        type: object
                                      prefix:
                                        type: string
                                      secretRef:
                                        properties:
                                          name:
                                            type: string
                                          optional:
                                            type: boolean
                                        type: object
                                    type: object
                                  type: array
                                image:
                                  type: string
                                imagePullPolicy:
                                  type: string
                                lifecycle:
                                  properties:
                                    postStart:
                                      properties:
                                        exec:
                                          properties:
                                            command:
                                              items:
                                                type: string
                                              type: array
                                          type: object
                                        httpGet:
                                          properties:
                                            host:
                                              type: string
                                            httpHeaders:
                                              items:
                                                properties:
                                                  name:
                                                    type: string
                                                  value:
                                                    type: string
                                                type: object
                                              type: array
                                            path:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                            scheme:
                                              type: string
                                          type: object
                                        sleep:
                                          properties:
                                            seconds:
                                              type: integer
                                          type: object
                                        tcpSocket:
                                          properties:
                                            host:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                          type: object
                                      type: object
                                    preStop:
                                      properties:
                                        exec:
                                          properties:
                                            command:
                                              items:
                                                type: string
                                              type: array
                                          type: object
                                        httpGet:
                                          properties:
                                            host:
                                              type: string
                                            httpHeaders:
                                              items:
                                                properties:
                                                  name:
                                                    type: string
                                                  value:
                                                    type: string
                                                type: object
                                              type: array
                                            path:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                            scheme:
                                              type: string
                                          type: object
                                        sleep:
                                          properties:
                                            seconds:
                                              type: integer
                                          type: object
                                        tcpSocket:
                                          properties:
                                            host:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                          type: object
                                      type: object
                                  type: object
                                livenessProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          type: integer
                                        service:
                                          type: string
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      type: object
                                    initialDelaySeconds:
                                      type: integer
                                    periodSeconds:
                                      type: integer
                                    successThreshold:
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      type: object
                                    terminationGracePeriodSeconds:
                                      type: integer
                                    timeoutSeconds:
                                      type: integer
                                  type: object
                                name:
                                  type: string
                                ports:
                                  items:
                                    properties:
                                      containerPort:
                                        type: integer
                                      hostIP:
                                        type: string
                                      hostPort:
                                        type: integer
                                      name:
                                        type: string
                                      protocol:
                                        type: string
                                    type: object
                                  type: array
                                readinessProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          type: integer
                                        service:
                                          type: string
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      type: object
                                    initialDelaySeconds:
                                      type: integer
                                    periodSeconds:
                                      type: integer
                                    successThreshold:
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      type: object
                                    terminationGracePeriodSeconds:
                                      type: integer
                                    timeoutSeconds:
                                      type: integer
                                  type: object
                                resizePolicy:
                                  items:
                                    properties:
                                      resourceName:
                                        type: string
                                      restartPolicy:
                                        type: string
                                    type: object
                                  type: array
                                resources:
                                  properties:
                                    claims:
                                      items:
                                        properties:
                                          name:
                                            type: string
                                        type: object
                                      type: array
                                    limits:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        x-kubernetes-int-or-string: true
                                      type: object
                                    requests:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        x-kubernetes-int-or-string: true
                                      type: object
                                  type: object
                                restartPolicy:
                                  type: string
                                securityContext:
                                  properties:
                                    allowPrivilegeEscalation:
                                      type: boolean
                                    appArmorProfile:
                                      properties:
                                        localhostProfile:
                                          type: string
                                        type:
                                          type: string
                                      type: object
                                    capabilities:
                                      properties:
                                        add:
                                          items:
                                            type: string
                                          type: array
                                        drop:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    privileged:
                                      type: boolean
                                    procMount:
                                      type: string
                                    readOnlyRootFilesystem:
                                      type: boolean
                                    runAsGroup:
                                      type: integer
                                    runAsNonRoot:
                                      type: boolean
                                    runAsUser:
                                      type: integer
                                    seLinuxOptions:
                                      properties:
                                        level:
                                          type: string
                                        role:
                                          type: string
                                        type:
                                          type: string
                                        user:
                                          type: string
                                      type: object
                                    seccompProfile:
                                      properties:
                                        localhostProfile:
                                          type: string
                                        type:
                                          type: string
                                      type: object
                                    windowsOptions:
                                      properties:
                                        gmsaCredentialSpec:
                                          type: string
                                        gmsaCredentialSpecName:
                                          type: string
                                        hostProcess:
                                          type: boolean
                                        runAsUserName:
                                          type: string
                                      type: object
                                  type: object
                                startupProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          type: integer
                                        service:
                                          type: string
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      type: object
                                    initialDelaySeconds:
                                      type: integer
                                    periodSeconds:
                                      type: integer
                                    successThreshold:
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      type: object
                                    terminationGracePeriodSeconds:
                                      type: integer
                                    timeoutSeconds:
                                      type: integer
                                  type: object
                                stdin:
                                  type: boolean
                                stdinOnce:
                                  type: boolean
                                terminationMessagePath:
                                  type: string
                                terminationMessagePolicy:
                                  type: string
                                tty:
                                  type: boolean
                                volumeDevices:
                                  items:
                                    properties:
                                      devicePath:
                                        type: string
                                      name:
                                        type: string
                                    type: object
                                  type: array
                                volumeMounts:
                                  items:
                                    properties:
                                      mountPath:
                                        type: string
                                      mountPropagation:
                                        type: string
                                      name:
                                        type: string
                                      readOnly:
                                        type: boolean
                                      recursiveReadOnly:
                                        type: string
                                      subPath:
                                        type: string
                                      subPathExpr:
                                        type: string
                                    type: object
                                  type: array
                                workingDir:
                                  type: string
                              type: object
                            type: array
                          nodeName:
                            type: string
                          nodeSelector:
                            additionalProperties:
                              type: string
                            type: object
                          os:
                            properties:
                              name:
                                type: string
                            type: object
                          overhead:
                            additionalProperties:
                              anyOf:
                              - type: integer
                              - type: string
                              x-kubernetes-int-or-string: true
                            type: object
                          preemptionPolicy:
                            type: string
                          priority:
                            type: integer
                          priorityClassName:
                            type: string
                          readinessGates:
                            items:
                              properties:
                                conditionType:
                                  type: string
                              type: object
                            type: array
                          resourceClaims:
                            items:
                              properties:
                                name:
                                  type: string
                                source:
                                  properties:
                                    resourceClaimName:
                                      type: string
                                    resourceClaimTemplateName:
                                      type: string
                                  type: object
                              type: object
                            type: array
                          restartPolicy:
                            type: string
                          runtimeClassName:
                            type: string
                          schedulerName:
                            type: string
                          schedulingGates:
                            items:
                              properties:
                                name:
                                  type: string
                              type: object
                            type: array
                          securityContext:
                            properties:
                              appArmorProfile:
                                properties:
                                  localhostProfile:
                                    type: string
                                  type:
                                    type: string
                                type: object
                              fsGroup:
                                type: integer
                              fsGroupChangePolicy:
                                type: string
                              runAsGroup:
                                type: integer
                              runAsNonRoot:
                                type: boolean
                              runAsUser:
                                type: integer
                              seLinuxOptions:
                                properties:
                                  level:
                                    type: string
                                  role:
                                    type: string
                                  type:
                                    type: string
                                  user:
                                    type: string
                                type: object
                              seccompProfile:
                                properties:
                                  localhostProfile:
                                    type: string
                                  type:
                                    type: string
                                type: object
                              supplementalGroups:
                                items:
                                  type: integer
                                type: array
                              sysctls:
                                items:
                                  properties:
                                    name:
                                      type: string
                                    value:
                                      type: string
                                  type: object
                                type: array
                              windowsOptions:
                                properties:
                                  gmsaCredentialSpec:
                                    type: string
                                  gmsaCredentialSpecName:
                                    type: string
                                  hostProcess:
                                    type: boolean
                                  runAsUserName:
                                    type: string
                                type: object
                            type: object
                          serviceAccount:
                            type: string
                          serviceAccountName:
                            type: string
                          setHostnameAsFQDN:
                            type: boolean
                          shareProcessNamespace:
                            type: boolean
                          subdomain:
                            type: string
                          terminationGracePeriodSeconds:
                            type: integer
                          tolerations:
                            items:
                              properties:
                                effect:
                                  type: string
                                key:
                                  type: string
                                operator:
                                  type: string
                                tolerationSeconds:
                                  type: integer
                                value:
                                  type: string
                              type: object
                            type: array
                          topologySpreadConstraints:
                            items:
                              properties:
                                labelSelector:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                        type: object
                                      type: array
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      type: object
                                  type: object
                                matchLabelKeys:
                                  items:
                                    type: string
                                  type: array
                                maxSkew:
                                  type: integer
                                minDomains:
                                  type: integer
                                nodeAffinityPolicy:
                                  type: string
                                nodeTaintsPolicy:
                                  type: string
                                topologyKey:
                                  type: string
                                whenUnsatisfiable:
                                  type: string
                              type: object
                            type: array
                          volumes:
                            items:
                              properties:
                                awsElasticBlockStore:
                                  properties:
                                    fsType:
                                      type: string
                                    partition:
                                      type: integer
                                    readOnly:
                                      type: boolean
                                    volumeID:
                                      type: string
                                  type: object
                                azureDisk:
                                  properties:
                                    cachingMode:
                                      type: string
                                    diskName:
                                      type: string
                                    diskURI:
                                      type: string
                                    fsType:
                                      type: string
                                    kind:
                                      type: string
                                    readOnly:
                                      type: boolean
                                  type: object
                                azureFile:
                                  properties:
                                    readOnly:
                                      type: boolean
                                    secretName:
                                      type: string
                                    shareName:
                                      type: string
                                  type: object
                                cephfs:
                                  properties:
                                    monitors:
                                      items:
                                        type: string
                                      type: array
                                    path:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    secretFile:
                                      type: string
                                    secretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                    user:
                                      type: string
                                  type: object
                                cinder:
                                  properties:
                                    fsType:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    secretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                    volumeID:
                                      type: string
                                  type: object
                                configMap:
                                  properties:
                                    defaultMode:
                                      type: integer
                                    items:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          mode:
                                            type: integer
                                          path:
                                            type: string
                                        type: object
                                      type: array
                                    name:
                                      type: string
                                    optional:
                                      type: boolean
                                  type: object
                                csi:
                                  properties:
                                    driver:
                                      type: string
                                    fsType:
                                      type: string
                                    nodePublishSecretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                    readOnly:
                                      type: boolean
                                    volumeAttributes:
                                      additionalProperties:
                                        type: string
                                      type: object
                                  type: object
                                downwardAPI:
                                  properties:
                                    defaultMode:
                                      type: integer
                                    items:
                                      items:
                                        properties:
                                          fieldRef:
                                            properties:
                                              apiVersion:
                                                type: string
                                              fieldPath:
                                                type: string
                                            type: object
                                          mode:
                                            type: integer
                                          path:
                                            type: string
                                          resourceFieldRef:
                                            properties:
                                              containerName:
                                                type: string
                                              divisor:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                              resource:
                                                type: string
                                            type: object
                                        type: object
                                      type: array
                                  type: object
                                emptyDir:
                                  properties:
                                    medium:
                                      type: string
                                    sizeLimit:
                                      anyOf:
                                      - type: integer
                                      - type: string
                                      x-kubernetes-int-or-string: true
                                  type: object
                                ephemeral:
                                  properties:
                                    volumeClaimTemplate:
                                      properties:
                                        metadata:
                                          properties:
                                            annotations:
                                              additionalProperties:
                                                type: string
                                              type: object
                                            creationTimestamp:
                                              type: string
                                            deletionGracePeriodSeconds:
                                              type: integer
                                            deletionTimestamp:
                                              type: string
                                            finalizers:
                                              items:
                                                type: string
                                              type: array
                                            generateName:
                                              type: string
                                            generation:
                                              type: integer
                                            labels:
                                              additionalProperties:
                                                type: string
                                              type: object
                                            managedFields:
                                              items:
                                                properties:
                                                  apiVersion:
                                                    type: string
                                                  fieldsType:
                                                    type: string
                                                  fieldsV1:
                                                    type: object
                                                  manager:
                                                    type: string
                                                  operation:
                                                    type: string
                                                  subresource:
                                                    type: string
                                                  time:
                                                    type: string
                                                type: object
                                              type: array
                                            name:
                                              type: string
                                            namespace:
                                              type: string
                                            ownerReferences:
                                              items:
                                                properties:
                                                  apiVersion:
                                                    type: string
                                                  blockOwnerDeletion:
                                                    type: boolean
                                                  controller:
                                                    type: boolean
                                                  kind:
                                                    type: string
                                                  name:
                                                    type: string
                                                  uid:
                                                    type: string
                                                type: object
                                              type: array
                                            resourceVersion:
                                              type: string
                                            selfLink:
                                              type: string
                                            uid:
                                              type: string
                                          type: object
                                        spec:
                                          properties:
                                            accessModes:
                                              items:
                                                type: string
                                              type: array
                                            dataSource:
                                              properties:
                                                apiGroup:
                                                  type: string
                                                kind:
                                                  type: string
                                                name:
                                                  type: string
                                              type: object
                                            dataSourceRef:
                                              properties:
                                                apiGroup:
                                                  type: string
                                                kind:
                                                  type: string
                                                name:
                                                  type: string
                                                namespace:
                                                  type: string
                                              type: object
                                            resources:
                                              properties:
                                                limits:
                                                  additionalProperties:
                                                    anyOf:
                                                    - type: integer
                                                    - type: string
                                                    x-kubernetes-int-or-string: true
                                                  type: object
                                                requests:
                                                  additionalProperties:
                                                    anyOf:
                                                    - type: integer
                                                    - type: string
                                                    x-kubernetes-int-or-string: true
                                                  type: object
                                              type: object
                                            selector:
                                              properties:
                                                matchExpressions:
                                                  items:
                                                    properties:
                                                      key:
                                                        type: string
                                                      operator:
                                                        type: string
                                                      values:
                                                        items:
                                                          type: string
                                                        type: array
                                                    type: object
                                                  type: array
                                                matchLabels:
                                                  additionalProperties:
                                                    type: string
                                                  type: object
                                              type: object
                                            storageClassName:
                                              type: string
                                            volumeAttributesClassName:
                                              type: string
                                            volumeMode:
                                              type: string
                                            volumeName:
                                              type: string
                                          type: object
                                      type: object
                                  type: object
                                fc:
                                  properties:
                                    fsType:
                                      type: string
                                    lun:
                                      type: integer
                                    readOnly:
                                      type: boolean
                                    targetWWNs:
                                      items:
                                        type: string
                                      type: array
                                    wwids:
                                      items:
                                        type: string
                                      type: array
                                  type: object
                                flexVolume:
                                  properties:
                                    driver:
                                      type: string
                                    fsType:
                                      type: string
                                    options:
                                      additionalProperties:
                                        type: string
                                      type: object
                                    readOnly:
                                      type: boolean
                                    secretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                  type: object
                                flocker:
                                  properties:
                                    datasetName:
                                      type: string
                                    datasetUUID:
                                      type: string
                                  type: object
                                gcePersistentDisk:
                                  properties:
                                    fsType:
                                      type: string
                                    partition:
                                      type: integer
                                    pdName:
                                      type: string
                                    readOnly:
                                      type: boolean
                                  type: object
                                gitRepo:
                                  properties:
                                    directory:
                                      type: string
                                    repository:
                                      type: string
                                    revision:
                                      type: string
                                  type: object
                                glusterfs:
                                  properties:
                                    endpoints:
                                      type: string
                                    path:
                                      type: string
                                    readOnly:
                                      type: boolean
                                  type: object
                                hostPath:
                                  properties:
                                    path:
                                      type: string
                                    type:
                                      type: string
                                  type: object
                                iscsi:
                                  properties:
                                    chapAuthDiscovery:
                                      type: boolean
                                    chapAuthSession:
                                      type: boolean
                                    fsType:
                                      type: string
                                    initiatorName:
                                      type: string
                                    iqn:
                                      type: string
                                    iscsiInterface:
                                      type: string
                                    lun:
                                      type: integer
                                    portals:
                                      items:
                                        type: string
                                      type: array
                                    readOnly:
                                      type: boolean
                                    secretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                    targetPortal:
                                      type: string
                                  type: object
                                name:
                                  type: string
                                nfs:
                                  properties:
                                    path:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    server:
                                      type: string
                                  type: object
                                persistentVolumeClaim:
                                  properties:
                                    claimName:
                                      type: string
                                    readOnly:
                                      type: boolean
                                  type: object
                                photonPersistentDisk:
                                  properties:
                                    fsType:
                                      type: string
                                    pdID:
                                      type: string
                                  type: object
                                portworxVolume:
                                  properties:
                                    fsType:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    volumeID:
                                      type: string
                                  type: object
                                projected:
                                  properties:
                                    defaultMode:
                                      type: integer
                                    sources:
                                      items:
                                        properties:
                                          clusterTrustBundle:
                                            properties:
                                              labelSelector:
                                                properties:
                                                  matchExpressions:
                                                    items:
                                                      properties:
                                                        key:
                                                          type: string
                                                        operator:
                                                          type: string
                                                        values:
                                                          items:
                                                            type: string
                                                          type: array
                                                      type: object
                                                    type: array
                                                  matchLabels:
                                                    additionalProperties:
                                                      type: string
                                                    type: object
                                                type: object
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                              path:
                                                type: string
                                              signerName:
                                                type: string
                                            type: object
                                          configMap:
                                            properties:
                                              items:
                                                items:
                                                  properties:
                                                    key:
                                                      type: string
                                                    mode:
                                                      type: integer
                                                    path:
                                                      type: string
                                                  type: object
                                                type: array
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            type: object
                                          downwardAPI:
                                            properties:
                                              items:
                                                items:
                                                  properties:
                                                    fieldRef:
                                                      properties:
                                                        apiVersion:
                                                          type: string
                                                        fieldPath:
                                                          type: string
                                                      type: object
                                                    mode:
                                                      type: integer
                                                    path:
                                                      type: string
                                                    resourceFieldRef:
                                                      properties:
                                                        containerName:
                                                          type: string
                                                        divisor:
                                                          anyOf:
                                                          - type: integer
                                                          - type: string
                                                          x-kubernetes-int-or-string: true
                                                        resource:
                                                          type: string
                                                      type: object
                                                  type: object
                                                type: array
                                            type: object
                                          secret:
                                            properties:
                                              items:
                                                items:
                                                  properties:
                                                    key:
                                                      type: string
                                                    mode:
                                                      type: integer
                                                    path:
                                                      type: string
                                                  type: object
                                                type: array
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            type: object
                                          serviceAccountToken:
                                            properties:
                                              audience:
                                                type: string
                                              expirationSeconds:
                                                type: integer
                                              path:
                                                type: string
                                            type: object
                                        type: object
                                      type: array
                                  type: object
                                quobyte:
                                  properties:
                                    group:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    registry:
                                      type: string
                                    tenant:
                                      type: string
                                    user:
                                      type: string
                                    volume:
                                      type: string
                                  type: object
                                rbd:
                                  properties:
                                    fsType:
                                      type: string
                                    image:
                                      type: string
                                    keyring:
                                      type: string
                                    monitors:
                                      items:
                                        type: string
                                      type: array
                                    pool:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    secretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                    user:
                                      type: string
                                  type: object
                                scaleIO:
                                  properties:
                                    fsType:
                                      type: string
                                    gateway:
                                      type: string
                                    protectionDomain:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    secretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                    sslEnabled:
                                      type: boolean
                                    storageMode:
                                      type: string
                                    storagePool:
                                      type: string
                                    system:
                                      type: string
                                    volumeName:
                                      type: string
                                  type: object
                                secret:
                                  properties:
                                    defaultMode:
                                      type: integer
                                    items:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          mode:
                                            type: integer
                                          path:
                                            type: string
                                        type: object
                                      type: array
                                    optional:
                                      type: boolean
                                    secretName:
                                      type: string
                                  type: object
                                storageos:
                                  properties:
                                    fsType:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    secretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                    volumeName:
                                      type: string
                                    volumeNamespace:
                                      type: string
                                  type: object
                                vsphereVolume:
                                  properties:
                                    fsType:
                                      type: string
                                    storagePolicyID:
                                      type: string
                                    storagePolicyName:
                                      type: string
                                    volumePath:
                                      type: string
                                  type: object
                              type: object
                            type: array
                        type: object
                    type: object
                  replicas:
                    description: Number of replicas for the component
                    type: integer
                type: object
            type: object
          status:
            properties:
              conditions:
                description: Apicurio Registry operator and operand conditions.
                items:
                  properties:
                    lastTransitionTime:
                      description: |
                        The last time the condition transitioned from one status to another.
                      type: string
                    lastUpdateTime:
                      description: |
                        The last time that the condition was updated by the operator.
                        Unlike the `lastTransitionTime` field, this timestamp is updated even if the status has not changed.
                      type: string
                    message:
                      description: message is a human readable message indicating
                        details about the transition. This may be an empty string.
                      type: string
                    reason:
                      description: reason contains a programmatic identifier indicating
                        the reason for the condition's last transition. Producers
                        of specific condition types may define expected values and
                        meanings for this field, and whether the values are considered
                        a guaranteed API. The value should be a CamelCase string.
                        This field may not be empty.
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "False"
                      - "True"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                        --- Many .condition.type values are consistent across resources
                        like Available, but because arbitrary conditions can be useful
                        (see .node.status.conditions), the ability to deconflict is
                        important. The regex it matches is (dns1123SubdomainFmt/)?(qualifiedNameFmt)
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - lastUpdateTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              observedGeneration:
                type: integer
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app: apicurio-registry-operator
    app.kubernetes.io/component: operator
    app.kubernetes.io/name: apicurio-registry-operator
    app.kubernetes.io/part-of: apicurio-registry
    app.kubernetes.io/version: 3.0.8
  name: apicurio-registry-operator
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app: apicurio-registry-operator
    app.kubernetes.io/component: operator
    app.kubernetes.io/name: apicurio-registry-operator
    app.kubernetes.io/part-of: apicurio-registry
    app.kubernetes.io/version: 3.0.8
  name: apicurio-registry-operator-clusterrole
rules:
- apiGroups:
  - registry.apicur.io
  resources:
  - apicurioregistries3
  - apicurioregistries3/status
  verbs:
  - '*'
- apiGroups:
  - apps
  resources:
  - deployments
  verbs:
  - '*'
- apiGroups:
  - events.k8s.io
  resources:
  - events
  verbs:
  - '*'
- apiGroups:
  - ""
  resources:
  - pods
  - services
  verbs:
  - '*'
- apiGroups:
  - apiextensions.k8s.io
  resources:
  - customresourcedefinitions
  verbs:
  - get
- apiGroups:
  - networking.k8s.io
  resources:
  - ingresses
  - networkpolicies
  verbs:
  - '*'
- apiGroups:
  - policy
  resources:
  - poddisruptionbudgets
  verbs:
  - '*'
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - list
  - get
  - create
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    app: apicurio-registry-operator
    app.kubernetes.io/component: operator
    app.kubernetes.io/name: apicurio-registry-operator
    app.kubernetes.io/part-of: apicurio-registry
    app.kubernetes.io/version: 3.0.8
  name: apicurio-registry-operator-clusterrolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: apicurio-registry-operator-clusterrole
subjects:
- kind: ServiceAccount
  name: apicurio-registry-operator
  namespace: PLACEHOLDER_NAMESPACE
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: apicurio-registry-operator
    app.kubernetes.io/component: operator
    app.kubernetes.io/name: apicurio-registry-operator
    app.kubernetes.io/part-of: apicurio-registry
    app.kubernetes.io/version: 3.0.8
  name: apicurio-registry-operator-v3.0.8
  namespace: PLACEHOLDER_NAMESPACE
spec:
  replicas: 1
  selector:
    matchLabels:
      app: apicurio-registry-operator
      app.kubernetes.io/component: operator
      app.kubernetes.io/name: apicurio-registry-operator
      app.kubernetes.io/part-of: apicurio-registry
      app.kubernetes.io/version: 3.0.8
  template:
    metadata:
      labels:
        app: apicurio-registry-operator
        app.kubernetes.io/component: operator
        app.kubernetes.io/name: apicurio-registry-operator
        app.kubernetes.io/part-of: apicurio-registry
        app.kubernetes.io/version: 3.0.8
    spec:
      containers:
      - env:
        - name: APICURIO_OPERATOR_WATCHED_NAMESPACES
          valueFrom:
            fieldRef:
              fieldPath: metadata.annotations['olm.targetNamespaces']
        - name: QUARKUS_PROFILE
          value: prod
        - name: REGISTRY_APP_IMAGE
          value: quay.io/apicurio/apicurio-registry:3.0.8
        - name: REGISTRY_UI_IMAGE
          value: quay.io/apicurio/apicurio-registry-ui:3.0.8
        image: quay.io/apicurio/apicurio-registry-3-operator:3.0.8
        imagePullPolicy: Always
        livenessProbe:
          httpGet:
            path: /q/health/live
            port: 8080
        name: apicurio-registry-operator
        ports:
        - containerPort: 8080
          name: http
          protocol: TCP
        readinessProbe:
          httpGet:
            path: /q/health/ready
            port: 8080
        resources:
          limits:
            cpu: 200m
            memory: 500Mi
          requests:
            cpu: 100m
            memory: 100Mi
        startupProbe:
          failureThreshold: 6
          httpGet:
            path: /q/health/started
            port: 8080
      serviceAccountName: apicurio-registry-operator
