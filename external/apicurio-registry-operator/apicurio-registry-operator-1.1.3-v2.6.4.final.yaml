apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.13.0
  labels:
    apicur.io/name: apicurio-registry-operator
    apicur.io/type: operator
    apicur.io/version: 1.1.3
  name: apicurioregistries.registry.apicur.io
spec:
  group: registry.apicur.io
  names:
    kind: ApicurioRegistry
    listKind: ApicurioRegistryList
    plural: apicurioregistries
    singular: apicurioregistry
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: ApicurioRegistry represents an Apicurio Registry instance
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: ApicurioRegistrySpec defines the desired state of ApicurioRegistry
            properties:
              configuration:
                description: Apicurio Registry application configuration
                properties:
                  env:
                    description: "Environment variables: \n List of additional environment
                      variables that will be provided to the Apicurio Registry application."
                    items:
                      description: EnvVar represents an environment variable present
                        in a Container.
                      properties:
                        name:
                          description: Name of the environment variable. Must be a
                            C_IDENTIFIER.
                          type: string
                        value:
                          description: 'Variable references $(VAR_NAME) are expanded
                            using the previously defined environment variables in
                            the container and any service environment variables. If
                            a variable cannot be resolved, the reference in the input
                            string will be unchanged. Double $$ are reduced to a single
                            $, which allows for escaping the $(VAR_NAME) syntax: i.e.
                            "$$(VAR_NAME)" will produce the string literal "$(VAR_NAME)".
                            Escaped references will never be expanded, regardless
                            of whether the variable exists or not. Defaults to "".'
                          type: string
                        valueFrom:
                          description: Source for the environment variable's value.
                            Cannot be used if value is not empty.
                          properties:
                            configMapKeyRef:
                              description: Selects a key of a ConfigMap.
                              properties:
                                key:
                                  description: The key to select.
                                  type: string
                                name:
                                  description: 'Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                    TODO: Add other useful fields. apiVersion, kind,
                                    uid?'
                                  type: string
                                optional:
                                  description: Specify whether the ConfigMap or its
                                    key must be defined
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                            fieldRef:
                              description: 'Selects a field of the pod: supports metadata.name,
                                metadata.namespace, `metadata.labels[''<KEY>'']`,
                                `metadata.annotations[''<KEY>'']`, spec.nodeName,
                                spec.serviceAccountName, status.hostIP, status.podIP,
                                status.podIPs.'
                              properties:
                                apiVersion:
                                  description: Version of the schema the FieldPath
                                    is written in terms of, defaults to "v1".
                                  type: string
                                fieldPath:
                                  description: Path of the field to select in the
                                    specified API version.
                                  type: string
                              required:
                              - fieldPath
                              type: object
                              x-kubernetes-map-type: atomic
                            resourceFieldRef:
                              description: 'Selects a resource of the container: only
                                resources limits and requests (limits.cpu, limits.memory,
                                limits.ephemeral-storage, requests.cpu, requests.memory
                                and requests.ephemeral-storage) are currently supported.'
                              properties:
                                containerName:
                                  description: 'Container name: required for volumes,
                                    optional for env vars'
                                  type: string
                                divisor:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  description: Specifies the output format of the
                                    exposed resources, defaults to "1"
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                resource:
                                  description: 'Required: resource to select'
                                  type: string
                              required:
                              - resource
                              type: object
                              x-kubernetes-map-type: atomic
                            secretKeyRef:
                              description: Selects a key of a secret in the pod's
                                namespace
                              properties:
                                key:
                                  description: The key of the secret to select from.  Must
                                    be a valid secret key.
                                  type: string
                                name:
                                  description: 'Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                    TODO: Add other useful fields. apiVersion, kind,
                                    uid?'
                                  type: string
                                optional:
                                  description: Specify whether the Secret or its key
                                    must be defined
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                      required:
                      - name
                      type: object
                    type: array
                  kafkasql:
                    description: Configuration of Apicurio Registry KafkaSQL storage
                    properties:
                      bootstrapServers:
                        description: "Kafka bootstrap servers URL: \n URL of one of
                          the Kafka brokers, which provide initial metadata about
                          the Kafka cluster, for example: `<service name>.<namespace>.svc:9092`."
                        type: string
                      security:
                        description: "Kafka security configuration: \n Provide the
                          following configuration options if your Kafka cluster is
                          secured using TLS or SCRAM."
                        properties:
                          scram:
                            description: "SCRAM: \n Kafka is secured using SCRAM."
                            properties:
                              mechanism:
                                description: "Mechanism: \n Name of the SCRAM mechanism,
                                  default value is SCRAM-SHA-512."
                                type: string
                              passwordSecretName:
                                description: "User password Secret name: \n Name of
                                  a Secret that contains password of the SCRAM user
                                  under the `password` key."
                                type: string
                              truststoreSecretName:
                                description: "Truststore Secret name: \n Name of a
                                  Secret that contains TLS truststore (in PKCS12 format)
                                  under the `ca.p12` key, and truststore password
                                  under the `ca.password` key."
                                type: string
                              user:
                                description: User name
                                type: string
                            type: object
                          tls:
                            description: "TLS: \n Kafka is secured using TLS."
                            properties:
                              keystoreSecretName:
                                description: "Keystore Secret name: \n Name of a Secret
                                  that contains TLS keystore (in PKCS12 format) under
                                  the `user.p12` key, and keystore password under
                                  the `user.password` key."
                                type: string
                              truststoreSecretName:
                                description: "Truststore Secret name: \n Name of a
                                  Secret that contains TLS truststore (in PKCS12 format)
                                  under the `ca.p12` key, and truststore password
                                  under the `ca.password` key."
                                type: string
                            type: object
                        type: object
                    type: object
                  logLevel:
                    description: Third-party (non-Apicurio) library log level
                    type: string
                  persistence:
                    description: "Storage: \n Type of storage used by Apicurio Registry,
                      one of: mem, sql, kafkasql. Default value is `mem`."
                    type: string
                  registryLogLevel:
                    description: Apicurio Registry application log level
                    type: string
                  security:
                    description: Security configuration
                    properties:
                      https:
                        description: "HTTPS: \n Configure Apicurio Registry to be
                          accessible using HTTPS."
                        properties:
                          disableHttp:
                            description: "Disable HTTP: \n Disable HTTP if HTTPS is
                              enabled."
                            type: boolean
                          secretName:
                            description: "HTTPS certificate and private key Secret
                              name: \n Name of a Secret that contains HTTPS certificate
                              under the `tls.crt` key, and the private key under the
                              `tls.key` key."
                            type: string
                        type: object
                      keycloak:
                        description: "Keycloak: \n Configure Apicurio Registry to
                          use Keycloak for Identity and Access Management (IAM)."
                        properties:
                          apiClientId:
                            description: Client ID for the REST API
                            type: string
                          realm:
                            description: Keycloak realm
                            type: string
                          uiClientId:
                            description: Client ID for the UI
                            type: string
                          url:
                            description: "Keycloak auth URL: \n URL of the Keycloak
                              auth endpoint, must end with `/auth`."
                            type: string
                        type: object
                    type: object
                  sql:
                    description: Configuration of Apicurio Registry SQL storage
                    properties:
                      dataSource:
                        description: SQL data source
                        properties:
                          password:
                            description: Data source password
                            type: string
                          url:
                            description: "Data source URL: \n URL of the PostgreSQL
                              database, for example: `jdbc:postgresql://<service name>.<namespace>.svc:5432/<database
                              name>`."
                            type: string
                          userName:
                            description: Data source username
                            type: string
                        type: object
                    type: object
                  ui:
                    description: Configuration of Apicurio Registry web console
                    properties:
                      readOnly:
                        description: "Read-only: \n Set the web console to read-only
                          mode. WARNING: This does not affect access to the Apicurio
                          REST API."
                        type: boolean
                    type: object
                type: object
              deployment:
                description: Apicurio Registry deployment configuration
                properties:
                  affinity:
                    description: Affinity
                    properties:
                      nodeAffinity:
                        description: Describes node affinity scheduling rules for
                          the pod.
                        properties:
                          preferredDuringSchedulingIgnoredDuringExecution:
                            description: The scheduler will prefer to schedule pods
                              to nodes that satisfy the affinity expressions specified
                              by this field, but it may choose a node that violates
                              one or more of the expressions. The node that is most
                              preferred is the one with the greatest sum of weights,
                              i.e. for each node that meets all of the scheduling
                              requirements (resource request, requiredDuringScheduling
                              affinity expressions, etc.), compute a sum by iterating
                              through the elements of this field and adding "weight"
                              to the sum if the node matches the corresponding matchExpressions;
                              the node(s) with the highest sum are the most preferred.
                            items:
                              description: An empty preferred scheduling term matches
                                all objects with implicit weight 0 (i.e. it's a no-op).
                                A null preferred scheduling term matches no objects
                                (i.e. is also a no-op).
                              properties:
                                preference:
                                  description: A node selector term, associated with
                                    the corresponding weight.
                                  properties:
                                    matchExpressions:
                                      description: A list of node selector requirements
                                        by node's labels.
                                      items:
                                        description: A node selector requirement is
                                          a selector that contains values, a key,
                                          and an operator that relates the key and
                                          values.
                                        properties:
                                          key:
                                            description: The label key that the selector
                                              applies to.
                                            type: string
                                          operator:
                                            description: Represents a key's relationship
                                              to a set of values. Valid operators
                                              are In, NotIn, Exists, DoesNotExist.
                                              Gt, and Lt.
                                            type: string
                                          values:
                                            description: An array of string values.
                                              If the operator is In or NotIn, the
                                              values array must be non-empty. If the
                                              operator is Exists or DoesNotExist,
                                              the values array must be empty. If the
                                              operator is Gt or Lt, the values array
                                              must have a single element, which will
                                              be interpreted as an integer. This array
                                              is replaced during a strategic merge
                                              patch.
                                            items:
                                              type: string
                                            type: array
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                    matchFields:
                                      description: A list of node selector requirements
                                        by node's fields.
                                      items:
                                        description: A node selector requirement is
                                          a selector that contains values, a key,
                                          and an operator that relates the key and
                                          values.
                                        properties:
                                          key:
                                            description: The label key that the selector
                                              applies to.
                                            type: string
                                          operator:
                                            description: Represents a key's relationship
                                              to a set of values. Valid operators
                                              are In, NotIn, Exists, DoesNotExist.
                                              Gt, and Lt.
                                            type: string
                                          values:
                                            description: An array of string values.
                                              If the operator is In or NotIn, the
                                              values array must be non-empty. If the
                                              operator is Exists or DoesNotExist,
                                              the values array must be empty. If the
                                              operator is Gt or Lt, the values array
                                              must have a single element, which will
                                              be interpreted as an integer. This array
                                              is replaced during a strategic merge
                                              patch.
                                            items:
                                              type: string
                                            type: array
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                  type: object
                                  x-kubernetes-map-type: atomic
                                weight:
                                  description: Weight associated with matching the
                                    corresponding nodeSelectorTerm, in the range 1-100.
                                  format: int32
                                  type: integer
                              required:
                              - preference
                              - weight
                              type: object
                            type: array
                          requiredDuringSchedulingIgnoredDuringExecution:
                            description: If the affinity requirements specified by
                              this field are not met at scheduling time, the pod will
                              not be scheduled onto the node. If the affinity requirements
                              specified by this field cease to be met at some point
                              during pod execution (e.g. due to an update), the system
                              may or may not try to eventually evict the pod from
                              its node.
                            properties:
                              nodeSelectorTerms:
                                description: Required. A list of node selector terms.
                                  The terms are ORed.
                                items:
                                  description: A null or empty node selector term
                                    matches no objects. The requirements of them are
                                    ANDed. The TopologySelectorTerm type implements
                                    a subset of the NodeSelectorTerm.
                                  properties:
                                    matchExpressions:
                                      description: A list of node selector requirements
                                        by node's labels.
                                      items:
                                        description: A node selector requirement is
                                          a selector that contains values, a key,
                                          and an operator that relates the key and
                                          values.
                                        properties:
                                          key:
                                            description: The label key that the selector
                                              applies to.
                                            type: string
                                          operator:
                                            description: Represents a key's relationship
                                              to a set of values. Valid operators
                                              are In, NotIn, Exists, DoesNotExist.
                                              Gt, and Lt.
                                            type: string
                                          values:
                                            description: An array of string values.
                                              If the operator is In or NotIn, the
                                              values array must be non-empty. If the
                                              operator is Exists or DoesNotExist,
                                              the values array must be empty. If the
                                              operator is Gt or Lt, the values array
                                              must have a single element, which will
                                              be interpreted as an integer. This array
                                              is replaced during a strategic merge
                                              patch.
                                            items:
                                              type: string
                                            type: array
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                    matchFields:
                                      description: A list of node selector requirements
                                        by node's fields.
                                      items:
                                        description: A node selector requirement is
                                          a selector that contains values, a key,
                                          and an operator that relates the key and
                                          values.
                                        properties:
                                          key:
                                            description: The label key that the selector
                                              applies to.
                                            type: string
                                          operator:
                                            description: Represents a key's relationship
                                              to a set of values. Valid operators
                                              are In, NotIn, Exists, DoesNotExist.
                                              Gt, and Lt.
                                            type: string
                                          values:
                                            description: An array of string values.
                                              If the operator is In or NotIn, the
                                              values array must be non-empty. If the
                                              operator is Exists or DoesNotExist,
                                              the values array must be empty. If the
                                              operator is Gt or Lt, the values array
                                              must have a single element, which will
                                              be interpreted as an integer. This array
                                              is replaced during a strategic merge
                                              patch.
                                            items:
                                              type: string
                                            type: array
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                  type: object
                                  x-kubernetes-map-type: atomic
                                type: array
                            required:
                            - nodeSelectorTerms
                            type: object
                            x-kubernetes-map-type: atomic
                        type: object
                      podAffinity:
                        description: Describes pod affinity scheduling rules (e.g.
                          co-locate this pod in the same node, zone, etc. as some
                          other pod(s)).
                        properties:
                          preferredDuringSchedulingIgnoredDuringExecution:
                            description: The scheduler will prefer to schedule pods
                              to nodes that satisfy the affinity expressions specified
                              by this field, but it may choose a node that violates
                              one or more of the expressions. The node that is most
                              preferred is the one with the greatest sum of weights,
                              i.e. for each node that meets all of the scheduling
                              requirements (resource request, requiredDuringScheduling
                              affinity expressions, etc.), compute a sum by iterating
                              through the elements of this field and adding "weight"
                              to the sum if the node has pods which matches the corresponding
                              podAffinityTerm; the node(s) with the highest sum are
                              the most preferred.
                            items:
                              description: The weights of all of the matched WeightedPodAffinityTerm
                                fields are added per-node to find the most preferred
                                node(s)
                              properties:
                                podAffinityTerm:
                                  description: Required. A pod affinity term, associated
                                    with the corresponding weight.
                                  properties:
                                    labelSelector:
                                      description: A label query over a set of resources,
                                        in this case pods. If it's null, this PodAffinityTerm
                                        matches with no Pods.
                                      properties:
                                        matchExpressions:
                                          description: matchExpressions is a list
                                            of label selector requirements. The requirements
                                            are ANDed.
                                          items:
                                            description: A label selector requirement
                                              is a selector that contains values,
                                              a key, and an operator that relates
                                              the key and values.
                                            properties:
                                              key:
                                                description: key is the label key
                                                  that the selector applies to.
                                                type: string
                                              operator:
                                                description: operator represents a
                                                  key's relationship to a set of values.
                                                  Valid operators are In, NotIn, Exists
                                                  and DoesNotExist.
                                                type: string
                                              values:
                                                description: values is an array of
                                                  string values. If the operator is
                                                  In or NotIn, the values array must
                                                  be non-empty. If the operator is
                                                  Exists or DoesNotExist, the values
                                                  array must be empty. This array
                                                  is replaced during a strategic merge
                                                  patch.
                                                items:
                                                  type: string
                                                type: array
                                            required:
                                            - key
                                            - operator
                                            type: object
                                          type: array
                                        matchLabels:
                                          additionalProperties:
                                            type: string
                                          description: matchLabels is a map of {key,value}
                                            pairs. A single {key,value} in the matchLabels
                                            map is equivalent to an element of matchExpressions,
                                            whose key field is "key", the operator
                                            is "In", and the values array contains
                                            only "value". The requirements are ANDed.
                                          type: object
                                      type: object
                                      x-kubernetes-map-type: atomic
                                    matchLabelKeys:
                                      description: MatchLabelKeys is a set of pod
                                        label keys to select which pods will be taken
                                        into consideration. The keys are used to lookup
                                        values from the incoming pod labels, those
                                        key-value labels are merged with `LabelSelector`
                                        as `key in (value)` to select the group of
                                        existing pods which pods will be taken into
                                        consideration for the incoming pod's pod (anti)
                                        affinity. Keys that don't exist in the incoming
                                        pod labels will be ignored. The default value
                                        is empty. The same key is forbidden to exist
                                        in both MatchLabelKeys and LabelSelector.
                                        Also, MatchLabelKeys cannot be set when LabelSelector
                                        isn't set. This is an alpha field and requires
                                        enabling MatchLabelKeysInPodAffinity feature
                                        gate.
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    mismatchLabelKeys:
                                      description: MismatchLabelKeys is a set of pod
                                        label keys to select which pods will be taken
                                        into consideration. The keys are used to lookup
                                        values from the incoming pod labels, those
                                        key-value labels are merged with `LabelSelector`
                                        as `key notin (value)` to select the group
                                        of existing pods which pods will be taken
                                        into consideration for the incoming pod's
                                        pod (anti) affinity. Keys that don't exist
                                        in the incoming pod labels will be ignored.
                                        The default value is empty. The same key is
                                        forbidden to exist in both MismatchLabelKeys
                                        and LabelSelector. Also, MismatchLabelKeys
                                        cannot be set when LabelSelector isn't set.
                                        This is an alpha field and requires enabling
                                        MatchLabelKeysInPodAffinity feature gate.
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    namespaceSelector:
                                      description: A label query over the set of namespaces
                                        that the term applies to. The term is applied
                                        to the union of the namespaces selected by
                                        this field and the ones listed in the namespaces
                                        field. null selector and null or empty namespaces
                                        list means "this pod's namespace". An empty
                                        selector ({}) matches all namespaces.
                                      properties:
                                        matchExpressions:
                                          description: matchExpressions is a list
                                            of label selector requirements. The requirements
                                            are ANDed.
                                          items:
                                            description: A label selector requirement
                                              is a selector that contains values,
                                              a key, and an operator that relates
                                              the key and values.
                                            properties:
                                              key:
                                                description: key is the label key
                                                  that the selector applies to.
                                                type: string
                                              operator:
                                                description: operator represents a
                                                  key's relationship to a set of values.
                                                  Valid operators are In, NotIn, Exists
                                                  and DoesNotExist.
                                                type: string
                                              values:
                                                description: values is an array of
                                                  string values. If the operator is
                                                  In or NotIn, the values array must
                                                  be non-empty. If the operator is
                                                  Exists or DoesNotExist, the values
                                                  array must be empty. This array
                                                  is replaced during a strategic merge
                                                  patch.
                                                items:
                                                  type: string
                                                type: array
                                            required:
                                            - key
                                            - operator
                                            type: object
                                          type: array
                                        matchLabels:
                                          additionalProperties:
                                            type: string
                                          description: matchLabels is a map of {key,value}
                                            pairs. A single {key,value} in the matchLabels
                                            map is equivalent to an element of matchExpressions,
                                            whose key field is "key", the operator
                                            is "In", and the values array contains
                                            only "value". The requirements are ANDed.
                                          type: object
                                      type: object
                                      x-kubernetes-map-type: atomic
                                    namespaces:
                                      description: namespaces specifies a static list
                                        of namespace names that the term applies to.
                                        The term is applied to the union of the namespaces
                                        listed in this field and the ones selected
                                        by namespaceSelector. null or empty namespaces
                                        list and null namespaceSelector means "this
                                        pod's namespace".
                                      items:
                                        type: string
                                      type: array
                                    topologyKey:
                                      description: This pod should be co-located (affinity)
                                        or not co-located (anti-affinity) with the
                                        pods matching the labelSelector in the specified
                                        namespaces, where co-located is defined as
                                        running on a node whose value of the label
                                        with key topologyKey matches that of any node
                                        on which any of the selected pods is running.
                                        Empty topologyKey is not allowed.
                                      type: string
                                  required:
                                  - topologyKey
                                  type: object
                                weight:
                                  description: weight associated with matching the
                                    corresponding podAffinityTerm, in the range 1-100.
                                  format: int32
                                  type: integer
                              required:
                              - podAffinityTerm
                              - weight
                              type: object
                            type: array
                          requiredDuringSchedulingIgnoredDuringExecution:
                            description: If the affinity requirements specified by
                              this field are not met at scheduling time, the pod will
                              not be scheduled onto the node. If the affinity requirements
                              specified by this field cease to be met at some point
                              during pod execution (e.g. due to a pod label update),
                              the system may or may not try to eventually evict the
                              pod from its node. When there are multiple elements,
                              the lists of nodes corresponding to each podAffinityTerm
                              are intersected, i.e. all terms must be satisfied.
                            items:
                              description: Defines a set of pods (namely those matching
                                the labelSelector relative to the given namespace(s))
                                that this pod should be co-located (affinity) or not
                                co-located (anti-affinity) with, where co-located
                                is defined as running on a node whose value of the
                                label with key <topologyKey> matches that of any node
                                on which a pod of the set of pods is running
                              properties:
                                labelSelector:
                                  description: A label query over a set of resources,
                                    in this case pods. If it's null, this PodAffinityTerm
                                    matches with no Pods.
                                  properties:
                                    matchExpressions:
                                      description: matchExpressions is a list of label
                                        selector requirements. The requirements are
                                        ANDed.
                                      items:
                                        description: A label selector requirement
                                          is a selector that contains values, a key,
                                          and an operator that relates the key and
                                          values.
                                        properties:
                                          key:
                                            description: key is the label key that
                                              the selector applies to.
                                            type: string
                                          operator:
                                            description: operator represents a key's
                                              relationship to a set of values. Valid
                                              operators are In, NotIn, Exists and
                                              DoesNotExist.
                                            type: string
                                          values:
                                            description: values is an array of string
                                              values. If the operator is In or NotIn,
                                              the values array must be non-empty.
                                              If the operator is Exists or DoesNotExist,
                                              the values array must be empty. This
                                              array is replaced during a strategic
                                              merge patch.
                                            items:
                                              type: string
                                            type: array
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      description: matchLabels is a map of {key,value}
                                        pairs. A single {key,value} in the matchLabels
                                        map is equivalent to an element of matchExpressions,
                                        whose key field is "key", the operator is
                                        "In", and the values array contains only "value".
                                        The requirements are ANDed.
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                matchLabelKeys:
                                  description: MatchLabelKeys is a set of pod label
                                    keys to select which pods will be taken into consideration.
                                    The keys are used to lookup values from the incoming
                                    pod labels, those key-value labels are merged
                                    with `LabelSelector` as `key in (value)` to select
                                    the group of existing pods which pods will be
                                    taken into consideration for the incoming pod's
                                    pod (anti) affinity. Keys that don't exist in
                                    the incoming pod labels will be ignored. The default
                                    value is empty. The same key is forbidden to exist
                                    in both MatchLabelKeys and LabelSelector. Also,
                                    MatchLabelKeys cannot be set when LabelSelector
                                    isn't set. This is an alpha field and requires
                                    enabling MatchLabelKeysInPodAffinity feature gate.
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                mismatchLabelKeys:
                                  description: MismatchLabelKeys is a set of pod label
                                    keys to select which pods will be taken into consideration.
                                    The keys are used to lookup values from the incoming
                                    pod labels, those key-value labels are merged
                                    with `LabelSelector` as `key notin (value)` to
                                    select the group of existing pods which pods will
                                    be taken into consideration for the incoming pod's
                                    pod (anti) affinity. Keys that don't exist in
                                    the incoming pod labels will be ignored. The default
                                    value is empty. The same key is forbidden to exist
                                    in both MismatchLabelKeys and LabelSelector. Also,
                                    MismatchLabelKeys cannot be set when LabelSelector
                                    isn't set. This is an alpha field and requires
                                    enabling MatchLabelKeysInPodAffinity feature gate.
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                namespaceSelector:
                                  description: A label query over the set of namespaces
                                    that the term applies to. The term is applied
                                    to the union of the namespaces selected by this
                                    field and the ones listed in the namespaces field.
                                    null selector and null or empty namespaces list
                                    means "this pod's namespace". An empty selector
                                    ({}) matches all namespaces.
                                  properties:
                                    matchExpressions:
                                      description: matchExpressions is a list of label
                                        selector requirements. The requirements are
                                        ANDed.
                                      items:
                                        description: A label selector requirement
                                          is a selector that contains values, a key,
                                          and an operator that relates the key and
                                          values.
                                        properties:
                                          key:
                                            description: key is the label key that
                                              the selector applies to.
                                            type: string
                                          operator:
                                            description: operator represents a key's
                                              relationship to a set of values. Valid
                                              operators are In, NotIn, Exists and
                                              DoesNotExist.
                                            type: string
                                          values:
                                            description: values is an array of string
                                              values. If the operator is In or NotIn,
                                              the values array must be non-empty.
                                              If the operator is Exists or DoesNotExist,
                                              the values array must be empty. This
                                              array is replaced during a strategic
                                              merge patch.
                                            items:
                                              type: string
                                            type: array
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      description: matchLabels is a map of {key,value}
                                        pairs. A single {key,value} in the matchLabels
                                        map is equivalent to an element of matchExpressions,
                                        whose key field is "key", the operator is
                                        "In", and the values array contains only "value".
                                        The requirements are ANDed.
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                namespaces:
                                  description: namespaces specifies a static list
                                    of namespace names that the term applies to. The
                                    term is applied to the union of the namespaces
                                    listed in this field and the ones selected by
                                    namespaceSelector. null or empty namespaces list
                                    and null namespaceSelector means "this pod's namespace".
                                  items:
                                    type: string
                                  type: array
                                topologyKey:
                                  description: This pod should be co-located (affinity)
                                    or not co-located (anti-affinity) with the pods
                                    matching the labelSelector in the specified namespaces,
                                    where co-located is defined as running on a node
                                    whose value of the label with key topologyKey
                                    matches that of any node on which any of the selected
                                    pods is running. Empty topologyKey is not allowed.
                                  type: string
                              required:
                              - topologyKey
                              type: object
                            type: array
                        type: object
                      podAntiAffinity:
                        description: Describes pod anti-affinity scheduling rules
                          (e.g. avoid putting this pod in the same node, zone, etc.
                          as some other pod(s)).
                        properties:
                          preferredDuringSchedulingIgnoredDuringExecution:
                            description: The scheduler will prefer to schedule pods
                              to nodes that satisfy the anti-affinity expressions
                              specified by this field, but it may choose a node that
                              violates one or more of the expressions. The node that
                              is most preferred is the one with the greatest sum of
                              weights, i.e. for each node that meets all of the scheduling
                              requirements (resource request, requiredDuringScheduling
                              anti-affinity expressions, etc.), compute a sum by iterating
                              through the elements of this field and adding "weight"
                              to the sum if the node has pods which matches the corresponding
                              podAffinityTerm; the node(s) with the highest sum are
                              the most preferred.
                            items:
                              description: The weights of all of the matched WeightedPodAffinityTerm
                                fields are added per-node to find the most preferred
                                node(s)
                              properties:
                                podAffinityTerm:
                                  description: Required. A pod affinity term, associated
                                    with the corresponding weight.
                                  properties:
                                    labelSelector:
                                      description: A label query over a set of resources,
                                        in this case pods. If it's null, this PodAffinityTerm
                                        matches with no Pods.
                                      properties:
                                        matchExpressions:
                                          description: matchExpressions is a list
                                            of label selector requirements. The requirements
                                            are ANDed.
                                          items:
                                            description: A label selector requirement
                                              is a selector that contains values,
                                              a key, and an operator that relates
                                              the key and values.
                                            properties:
                                              key:
                                                description: key is the label key
                                                  that the selector applies to.
                                                type: string
                                              operator:
                                                description: operator represents a
                                                  key's relationship to a set of values.
                                                  Valid operators are In, NotIn, Exists
                                                  and DoesNotExist.
                                                type: string
                                              values:
                                                description: values is an array of
                                                  string values. If the operator is
                                                  In or NotIn, the values array must
                                                  be non-empty. If the operator is
                                                  Exists or DoesNotExist, the values
                                                  array must be empty. This array
                                                  is replaced during a strategic merge
                                                  patch.
                                                items:
                                                  type: string
                                                type: array
                                            required:
                                            - key
                                            - operator
                                            type: object
                                          type: array
                                        matchLabels:
                                          additionalProperties:
                                            type: string
                                          description: matchLabels is a map of {key,value}
                                            pairs. A single {key,value} in the matchLabels
                                            map is equivalent to an element of matchExpressions,
                                            whose key field is "key", the operator
                                            is "In", and the values array contains
                                            only "value". The requirements are ANDed.
                                          type: object
                                      type: object
                                      x-kubernetes-map-type: atomic
                                    matchLabelKeys:
                                      description: MatchLabelKeys is a set of pod
                                        label keys to select which pods will be taken
                                        into consideration. The keys are used to lookup
                                        values from the incoming pod labels, those
                                        key-value labels are merged with `LabelSelector`
                                        as `key in (value)` to select the group of
                                        existing pods which pods will be taken into
                                        consideration for the incoming pod's pod (anti)
                                        affinity. Keys that don't exist in the incoming
                                        pod labels will be ignored. The default value
                                        is empty. The same key is forbidden to exist
                                        in both MatchLabelKeys and LabelSelector.
                                        Also, MatchLabelKeys cannot be set when LabelSelector
                                        isn't set. This is an alpha field and requires
                                        enabling MatchLabelKeysInPodAffinity feature
                                        gate.
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    mismatchLabelKeys:
                                      description: MismatchLabelKeys is a set of pod
                                        label keys to select which pods will be taken
                                        into consideration. The keys are used to lookup
                                        values from the incoming pod labels, those
                                        key-value labels are merged with `LabelSelector`
                                        as `key notin (value)` to select the group
                                        of existing pods which pods will be taken
                                        into consideration for the incoming pod's
                                        pod (anti) affinity. Keys that don't exist
                                        in the incoming pod labels will be ignored.
                                        The default value is empty. The same key is
                                        forbidden to exist in both MismatchLabelKeys
                                        and LabelSelector. Also, MismatchLabelKeys
                                        cannot be set when LabelSelector isn't set.
                                        This is an alpha field and requires enabling
                                        MatchLabelKeysInPodAffinity feature gate.
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    namespaceSelector:
                                      description: A label query over the set of namespaces
                                        that the term applies to. The term is applied
                                        to the union of the namespaces selected by
                                        this field and the ones listed in the namespaces
                                        field. null selector and null or empty namespaces
                                        list means "this pod's namespace". An empty
                                        selector ({}) matches all namespaces.
                                      properties:
                                        matchExpressions:
                                          description: matchExpressions is a list
                                            of label selector requirements. The requirements
                                            are ANDed.
                                          items:
                                            description: A label selector requirement
                                              is a selector that contains values,
                                              a key, and an operator that relates
                                              the key and values.
                                            properties:
                                              key:
                                                description: key is the label key
                                                  that the selector applies to.
                                                type: string
                                              operator:
                                                description: operator represents a
                                                  key's relationship to a set of values.
                                                  Valid operators are In, NotIn, Exists
                                                  and DoesNotExist.
                                                type: string
                                              values:
                                                description: values is an array of
                                                  string values. If the operator is
                                                  In or NotIn, the values array must
                                                  be non-empty. If the operator is
                                                  Exists or DoesNotExist, the values
                                                  array must be empty. This array
                                                  is replaced during a strategic merge
                                                  patch.
                                                items:
                                                  type: string
                                                type: array
                                            required:
                                            - key
                                            - operator
                                            type: object
                                          type: array
                                        matchLabels:
                                          additionalProperties:
                                            type: string
                                          description: matchLabels is a map of {key,value}
                                            pairs. A single {key,value} in the matchLabels
                                            map is equivalent to an element of matchExpressions,
                                            whose key field is "key", the operator
                                            is "In", and the values array contains
                                            only "value". The requirements are ANDed.
                                          type: object
                                      type: object
                                      x-kubernetes-map-type: atomic
                                    namespaces:
                                      description: namespaces specifies a static list
                                        of namespace names that the term applies to.
                                        The term is applied to the union of the namespaces
                                        listed in this field and the ones selected
                                        by namespaceSelector. null or empty namespaces
                                        list and null namespaceSelector means "this
                                        pod's namespace".
                                      items:
                                        type: string
                                      type: array
                                    topologyKey:
                                      description: This pod should be co-located (affinity)
                                        or not co-located (anti-affinity) with the
                                        pods matching the labelSelector in the specified
                                        namespaces, where co-located is defined as
                                        running on a node whose value of the label
                                        with key topologyKey matches that of any node
                                        on which any of the selected pods is running.
                                        Empty topologyKey is not allowed.
                                      type: string
                                  required:
                                  - topologyKey
                                  type: object
                                weight:
                                  description: weight associated with matching the
                                    corresponding podAffinityTerm, in the range 1-100.
                                  format: int32
                                  type: integer
                              required:
                              - podAffinityTerm
                              - weight
                              type: object
                            type: array
                          requiredDuringSchedulingIgnoredDuringExecution:
                            description: If the anti-affinity requirements specified
                              by this field are not met at scheduling time, the pod
                              will not be scheduled onto the node. If the anti-affinity
                              requirements specified by this field cease to be met
                              at some point during pod execution (e.g. due to a pod
                              label update), the system may or may not try to eventually
                              evict the pod from its node. When there are multiple
                              elements, the lists of nodes corresponding to each podAffinityTerm
                              are intersected, i.e. all terms must be satisfied.
                            items:
                              description: Defines a set of pods (namely those matching
                                the labelSelector relative to the given namespace(s))
                                that this pod should be co-located (affinity) or not
                                co-located (anti-affinity) with, where co-located
                                is defined as running on a node whose value of the
                                label with key <topologyKey> matches that of any node
                                on which a pod of the set of pods is running
                              properties:
                                labelSelector:
                                  description: A label query over a set of resources,
                                    in this case pods. If it's null, this PodAffinityTerm
                                    matches with no Pods.
                                  properties:
                                    matchExpressions:
                                      description: matchExpressions is a list of label
                                        selector requirements. The requirements are
                                        ANDed.
                                      items:
                                        description: A label selector requirement
                                          is a selector that contains values, a key,
                                          and an operator that relates the key and
                                          values.
                                        properties:
                                          key:
                                            description: key is the label key that
                                              the selector applies to.
                                            type: string
                                          operator:
                                            description: operator represents a key's
                                              relationship to a set of values. Valid
                                              operators are In, NotIn, Exists and
                                              DoesNotExist.
                                            type: string
                                          values:
                                            description: values is an array of string
                                              values. If the operator is In or NotIn,
                                              the values array must be non-empty.
                                              If the operator is Exists or DoesNotExist,
                                              the values array must be empty. This
                                              array is replaced during a strategic
                                              merge patch.
                                            items:
                                              type: string
                                            type: array
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      description: matchLabels is a map of {key,value}
                                        pairs. A single {key,value} in the matchLabels
                                        map is equivalent to an element of matchExpressions,
                                        whose key field is "key", the operator is
                                        "In", and the values array contains only "value".
                                        The requirements are ANDed.
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                matchLabelKeys:
                                  description: MatchLabelKeys is a set of pod label
                                    keys to select which pods will be taken into consideration.
                                    The keys are used to lookup values from the incoming
                                    pod labels, those key-value labels are merged
                                    with `LabelSelector` as `key in (value)` to select
                                    the group of existing pods which pods will be
                                    taken into consideration for the incoming pod's
                                    pod (anti) affinity. Keys that don't exist in
                                    the incoming pod labels will be ignored. The default
                                    value is empty. The same key is forbidden to exist
                                    in both MatchLabelKeys and LabelSelector. Also,
                                    MatchLabelKeys cannot be set when LabelSelector
                                    isn't set. This is an alpha field and requires
                                    enabling MatchLabelKeysInPodAffinity feature gate.
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                mismatchLabelKeys:
                                  description: MismatchLabelKeys is a set of pod label
                                    keys to select which pods will be taken into consideration.
                                    The keys are used to lookup values from the incoming
                                    pod labels, those key-value labels are merged
                                    with `LabelSelector` as `key notin (value)` to
                                    select the group of existing pods which pods will
                                    be taken into consideration for the incoming pod's
                                    pod (anti) affinity. Keys that don't exist in
                                    the incoming pod labels will be ignored. The default
                                    value is empty. The same key is forbidden to exist
                                    in both MismatchLabelKeys and LabelSelector. Also,
                                    MismatchLabelKeys cannot be set when LabelSelector
                                    isn't set. This is an alpha field and requires
                                    enabling MatchLabelKeysInPodAffinity feature gate.
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                namespaceSelector:
                                  description: A label query over the set of namespaces
                                    that the term applies to. The term is applied
                                    to the union of the namespaces selected by this
                                    field and the ones listed in the namespaces field.
                                    null selector and null or empty namespaces list
                                    means "this pod's namespace". An empty selector
                                    ({}) matches all namespaces.
                                  properties:
                                    matchExpressions:
                                      description: matchExpressions is a list of label
                                        selector requirements. The requirements are
                                        ANDed.
                                      items:
                                        description: A label selector requirement
                                          is a selector that contains values, a key,
                                          and an operator that relates the key and
                                          values.
                                        properties:
                                          key:
                                            description: key is the label key that
                                              the selector applies to.
                                            type: string
                                          operator:
                                            description: operator represents a key's
                                              relationship to a set of values. Valid
                                              operators are In, NotIn, Exists and
                                              DoesNotExist.
                                            type: string
                                          values:
                                            description: values is an array of string
                                              values. If the operator is In or NotIn,
                                              the values array must be non-empty.
                                              If the operator is Exists or DoesNotExist,
                                              the values array must be empty. This
                                              array is replaced during a strategic
                                              merge patch.
                                            items:
                                              type: string
                                            type: array
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      description: matchLabels is a map of {key,value}
                                        pairs. A single {key,value} in the matchLabels
                                        map is equivalent to an element of matchExpressions,
                                        whose key field is "key", the operator is
                                        "In", and the values array contains only "value".
                                        The requirements are ANDed.
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                namespaces:
                                  description: namespaces specifies a static list
                                    of namespace names that the term applies to. The
                                    term is applied to the union of the namespaces
                                    listed in this field and the ones selected by
                                    namespaceSelector. null or empty namespaces list
                                    and null namespaceSelector means "this pod's namespace".
                                  items:
                                    type: string
                                  type: array
                                topologyKey:
                                  description: This pod should be co-located (affinity)
                                    or not co-located (anti-affinity) with the pods
                                    matching the labelSelector in the specified namespaces,
                                    where co-located is defined as running on a node
                                    whose value of the label with key topologyKey
                                    matches that of any node on which any of the selected
                                    pods is running. Empty topologyKey is not allowed.
                                  type: string
                              required:
                              - topologyKey
                              type: object
                            type: array
                        type: object
                    type: object
                  host:
                    description: "Hostname: \n Apicurio Registry application hostname
                      (part of the URL without the protocol and path)."
                    type: string
                  image:
                    description: "Apicurio Registry image: \n Replaces the default
                      Apicurio Registry application image. Overrides the values in
                      the REGISTRY_IMAGE_MEM, REGISTRY_IMAGE_KAFKASQL and REGISTRY_IMAGE_SQL
                      Operator environment variables."
                    type: string
                  imagePullSecrets:
                    description: "Apicurio Registry image pull secrets: \n List of
                      Secrets to use when pulling the Apicurio Registry image."
                    items:
                      description: LocalObjectReference contains enough information
                        to let you locate the referenced object inside the same namespace.
                      properties:
                        name:
                          description: 'Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                            TODO: Add other useful fields. apiVersion, kind, uid?'
                          type: string
                      type: object
                      x-kubernetes-map-type: atomic
                    type: array
                  managedResources:
                    description: "Apicurio Registry managed resources: \n Configure
                      how the Operator manages Kubernetes resources."
                    properties:
                      disableIngress:
                        description: "Disable Ingress: \n Operator will not create
                          or manage an Ingress for Apicurio Registry, so it can be
                          done manually."
                        type: boolean
                      disableNetworkPolicy:
                        description: "Disable NetworkPolicy: \n Operator will not
                          create or manage a NetworkPolicy for Apicurio Registry,
                          so it can be done manually."
                        type: boolean
                      disablePodDisruptionBudget:
                        description: "Disable PodDisruptionBudget: \n Operator will
                          not create or manage a PodDisruptionBudget for Apicurio
                          Registry, so it can be done manually."
                        type: boolean
                    type: object
                  metadata:
                    description: Metadata of the Apicurio Registry pod
                    properties:
                      annotations:
                        additionalProperties:
                          type: string
                        description: "Annotations: \n Additional Apicurio Registry
                          Pod annotations."
                        type: object
                      labels:
                        additionalProperties:
                          type: string
                        description: "Labels: \n Additional Apicurio Registry Pod
                          labels."
                        type: object
                    type: object
                  podTemplateSpecPreview:
                    properties:
                      metadata:
                        properties:
                          annotations:
                            additionalProperties:
                              type: string
                            type: object
                          clusterName:
                            type: string
                          creationTimestamp:
                            format: date-time
                            type: string
                          deletionGracePeriodSeconds:
                            format: int64
                            type: integer
                          deletionTimestamp:
                            format: date-time
                            type: string
                          finalizers:
                            items:
                              type: string
                            type: array
                          generateName:
                            type: string
                          generation:
                            format: int64
                            type: integer
                          labels:
                            additionalProperties:
                              type: string
                            type: object
                          managedFields:
                            items:
                              properties:
                                apiVersion:
                                  type: string
                                fieldsType:
                                  type: string
                                fieldsV1:
                                  type: object
                                manager:
                                  type: string
                                operation:
                                  type: string
                                subresource:
                                  type: string
                                time:
                                  format: date-time
                                  type: string
                              type: object
                            type: array
                          name:
                            type: string
                          namespace:
                            type: string
                          ownerReferences:
                            items:
                              properties:
                                apiVersion:
                                  type: string
                                blockOwnerDeletion:
                                  type: boolean
                                controller:
                                  type: boolean
                                kind:
                                  type: string
                                name:
                                  type: string
                                uid:
                                  type: string
                              required:
                              - apiVersion
                              - kind
                              - name
                              - uid
                              type: object
                              x-kubernetes-map-type: atomic
                            type: array
                          resourceVersion:
                            type: string
                          selfLink:
                            type: string
                          uid:
                            type: string
                        type: object
                      spec:
                        properties:
                          activeDeadlineSeconds:
                            format: int64
                            type: integer
                          affinity:
                            properties:
                              nodeAffinity:
                                properties:
                                  preferredDuringSchedulingIgnoredDuringExecution:
                                    items:
                                      properties:
                                        preference:
                                          properties:
                                            matchExpressions:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                required:
                                                - key
                                                - operator
                                                type: object
                                              type: array
                                            matchFields:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                required:
                                                - key
                                                - operator
                                                type: object
                                              type: array
                                          type: object
                                          x-kubernetes-map-type: atomic
                                        weight:
                                          format: int32
                                          type: integer
                                      required:
                                      - preference
                                      - weight
                                      type: object
                                    type: array
                                  requiredDuringSchedulingIgnoredDuringExecution:
                                    properties:
                                      nodeSelectorTerms:
                                        items:
                                          properties:
                                            matchExpressions:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                required:
                                                - key
                                                - operator
                                                type: object
                                              type: array
                                            matchFields:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                required:
                                                - key
                                                - operator
                                                type: object
                                              type: array
                                          type: object
                                          x-kubernetes-map-type: atomic
                                        type: array
                                    required:
                                    - nodeSelectorTerms
                                    type: object
                                    x-kubernetes-map-type: atomic
                                type: object
                              podAffinity:
                                properties:
                                  preferredDuringSchedulingIgnoredDuringExecution:
                                    items:
                                      properties:
                                        podAffinityTerm:
                                          properties:
                                            labelSelector:
                                              properties:
                                                matchExpressions:
                                                  items:
                                                    properties:
                                                      key:
                                                        type: string
                                                      operator:
                                                        type: string
                                                      values:
                                                        items:
                                                          type: string
                                                        type: array
                                                    required:
                                                    - key
                                                    - operator
                                                    type: object
                                                  type: array
                                                matchLabels:
                                                  additionalProperties:
                                                    type: string
                                                  type: object
                                              type: object
                                              x-kubernetes-map-type: atomic
                                            matchLabelKeys:
                                              items:
                                                type: string
                                              type: array
                                              x-kubernetes-list-type: atomic
                                            mismatchLabelKeys:
                                              items:
                                                type: string
                                              type: array
                                              x-kubernetes-list-type: atomic
                                            namespaceSelector:
                                              properties:
                                                matchExpressions:
                                                  items:
                                                    properties:
                                                      key:
                                                        type: string
                                                      operator:
                                                        type: string
                                                      values:
                                                        items:
                                                          type: string
                                                        type: array
                                                    required:
                                                    - key
                                                    - operator
                                                    type: object
                                                  type: array
                                                matchLabels:
                                                  additionalProperties:
                                                    type: string
                                                  type: object
                                              type: object
                                              x-kubernetes-map-type: atomic
                                            namespaces:
                                              items:
                                                type: string
                                              type: array
                                            topologyKey:
                                              type: string
                                          required:
                                          - topologyKey
                                          type: object
                                        weight:
                                          format: int32
                                          type: integer
                                      required:
                                      - podAffinityTerm
                                      - weight
                                      type: object
                                    type: array
                                  requiredDuringSchedulingIgnoredDuringExecution:
                                    items:
                                      properties:
                                        labelSelector:
                                          properties:
                                            matchExpressions:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                required:
                                                - key
                                                - operator
                                                type: object
                                              type: array
                                            matchLabels:
                                              additionalProperties:
                                                type: string
                                              type: object
                                          type: object
                                          x-kubernetes-map-type: atomic
                                        matchLabelKeys:
                                          items:
                                            type: string
                                          type: array
                                          x-kubernetes-list-type: atomic
                                        mismatchLabelKeys:
                                          items:
                                            type: string
                                          type: array
                                          x-kubernetes-list-type: atomic
                                        namespaceSelector:
                                          properties:
                                            matchExpressions:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                required:
                                                - key
                                                - operator
                                                type: object
                                              type: array
                                            matchLabels:
                                              additionalProperties:
                                                type: string
                                              type: object
                                          type: object
                                          x-kubernetes-map-type: atomic
                                        namespaces:
                                          items:
                                            type: string
                                          type: array
                                        topologyKey:
                                          type: string
                                      required:
                                      - topologyKey
                                      type: object
                                    type: array
                                type: object
                              podAntiAffinity:
                                properties:
                                  preferredDuringSchedulingIgnoredDuringExecution:
                                    items:
                                      properties:
                                        podAffinityTerm:
                                          properties:
                                            labelSelector:
                                              properties:
                                                matchExpressions:
                                                  items:
                                                    properties:
                                                      key:
                                                        type: string
                                                      operator:
                                                        type: string
                                                      values:
                                                        items:
                                                          type: string
                                                        type: array
                                                    required:
                                                    - key
                                                    - operator
                                                    type: object
                                                  type: array
                                                matchLabels:
                                                  additionalProperties:
                                                    type: string
                                                  type: object
                                              type: object
                                              x-kubernetes-map-type: atomic
                                            matchLabelKeys:
                                              items:
                                                type: string
                                              type: array
                                              x-kubernetes-list-type: atomic
                                            mismatchLabelKeys:
                                              items:
                                                type: string
                                              type: array
                                              x-kubernetes-list-type: atomic
                                            namespaceSelector:
                                              properties:
                                                matchExpressions:
                                                  items:
                                                    properties:
                                                      key:
                                                        type: string
                                                      operator:
                                                        type: string
                                                      values:
                                                        items:
                                                          type: string
                                                        type: array
                                                    required:
                                                    - key
                                                    - operator
                                                    type: object
                                                  type: array
                                                matchLabels:
                                                  additionalProperties:
                                                    type: string
                                                  type: object
                                              type: object
                                              x-kubernetes-map-type: atomic
                                            namespaces:
                                              items:
                                                type: string
                                              type: array
                                            topologyKey:
                                              type: string
                                          required:
                                          - topologyKey
                                          type: object
                                        weight:
                                          format: int32
                                          type: integer
                                      required:
                                      - podAffinityTerm
                                      - weight
                                      type: object
                                    type: array
                                  requiredDuringSchedulingIgnoredDuringExecution:
                                    items:
                                      properties:
                                        labelSelector:
                                          properties:
                                            matchExpressions:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                required:
                                                - key
                                                - operator
                                                type: object
                                              type: array
                                            matchLabels:
                                              additionalProperties:
                                                type: string
                                              type: object
                                          type: object
                                          x-kubernetes-map-type: atomic
                                        matchLabelKeys:
                                          items:
                                            type: string
                                          type: array
                                          x-kubernetes-list-type: atomic
                                        mismatchLabelKeys:
                                          items:
                                            type: string
                                          type: array
                                          x-kubernetes-list-type: atomic
                                        namespaceSelector:
                                          properties:
                                            matchExpressions:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                required:
                                                - key
                                                - operator
                                                type: object
                                              type: array
                                            matchLabels:
                                              additionalProperties:
                                                type: string
                                              type: object
                                          type: object
                                          x-kubernetes-map-type: atomic
                                        namespaces:
                                          items:
                                            type: string
                                          type: array
                                        topologyKey:
                                          type: string
                                      required:
                                      - topologyKey
                                      type: object
                                    type: array
                                type: object
                            type: object
                          automountServiceAccountToken:
                            type: boolean
                          containers:
                            items:
                              properties:
                                args:
                                  items:
                                    type: string
                                  type: array
                                command:
                                  items:
                                    type: string
                                  type: array
                                env:
                                  items:
                                    properties:
                                      name:
                                        type: string
                                      value:
                                        type: string
                                      valueFrom:
                                        properties:
                                          configMapKeyRef:
                                            properties:
                                              key:
                                                type: string
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            required:
                                            - key
                                            type: object
                                            x-kubernetes-map-type: atomic
                                          fieldRef:
                                            properties:
                                              apiVersion:
                                                type: string
                                              fieldPath:
                                                type: string
                                            required:
                                            - fieldPath
                                            type: object
                                            x-kubernetes-map-type: atomic
                                          resourceFieldRef:
                                            properties:
                                              containerName:
                                                type: string
                                              divisor:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                                x-kubernetes-int-or-string: true
                                              resource:
                                                type: string
                                            required:
                                            - resource
                                            type: object
                                            x-kubernetes-map-type: atomic
                                          secretKeyRef:
                                            properties:
                                              key:
                                                type: string
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            required:
                                            - key
                                            type: object
                                            x-kubernetes-map-type: atomic
                                        type: object
                                    required:
                                    - name
                                    type: object
                                  type: array
                                envFrom:
                                  items:
                                    properties:
                                      configMapRef:
                                        properties:
                                          name:
                                            type: string
                                          optional:
                                            type: boolean
                                        type: object
                                        x-kubernetes-map-type: atomic
                                      prefix:
                                        type: string
                                      secretRef:
                                        properties:
                                          name:
                                            type: string
                                          optional:
                                            type: boolean
                                        type: object
                                        x-kubernetes-map-type: atomic
                                    type: object
                                  type: array
                                image:
                                  type: string
                                imagePullPolicy:
                                  type: string
                                lifecycle:
                                  properties:
                                    postStart:
                                      properties:
                                        exec:
                                          properties:
                                            command:
                                              items:
                                                type: string
                                              type: array
                                          type: object
                                        httpGet:
                                          properties:
                                            host:
                                              type: string
                                            httpHeaders:
                                              items:
                                                properties:
                                                  name:
                                                    type: string
                                                  value:
                                                    type: string
                                                required:
                                                - name
                                                - value
                                                type: object
                                              type: array
                                            path:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                            scheme:
                                              type: string
                                          required:
                                          - port
                                          type: object
                                        sleep:
                                          properties:
                                            seconds:
                                              format: int64
                                              type: integer
                                          required:
                                          - seconds
                                          type: object
                                        tcpSocket:
                                          properties:
                                            host:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                          required:
                                          - port
                                          type: object
                                      type: object
                                    preStop:
                                      properties:
                                        exec:
                                          properties:
                                            command:
                                              items:
                                                type: string
                                              type: array
                                          type: object
                                        httpGet:
                                          properties:
                                            host:
                                              type: string
                                            httpHeaders:
                                              items:
                                                properties:
                                                  name:
                                                    type: string
                                                  value:
                                                    type: string
                                                required:
                                                - name
                                                - value
                                                type: object
                                              type: array
                                            path:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                            scheme:
                                              type: string
                                          required:
                                          - port
                                          type: object
                                        sleep:
                                          properties:
                                            seconds:
                                              format: int64
                                              type: integer
                                          required:
                                          - seconds
                                          type: object
                                        tcpSocket:
                                          properties:
                                            host:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                          required:
                                          - port
                                          type: object
                                      type: object
                                  type: object
                                livenessProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      format: int32
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          format: int32
                                          type: integer
                                        service:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            required:
                                            - name
                                            - value
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    initialDelaySeconds:
                                      format: int32
                                      type: integer
                                    periodSeconds:
                                      format: int32
                                      type: integer
                                    successThreshold:
                                      format: int32
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      required:
                                      - port
                                      type: object
                                    terminationGracePeriodSeconds:
                                      format: int64
                                      type: integer
                                    timeoutSeconds:
                                      format: int32
                                      type: integer
                                  type: object
                                name:
                                  type: string
                                ports:
                                  items:
                                    properties:
                                      containerPort:
                                        format: int32
                                        type: integer
                                      hostIP:
                                        type: string
                                      hostPort:
                                        format: int32
                                        type: integer
                                      name:
                                        type: string
                                      protocol:
                                        default: TCP
                                        type: string
                                    required:
                                    - containerPort
                                    type: object
                                  type: array
                                  x-kubernetes-list-map-keys:
                                  - containerPort
                                  - protocol
                                  x-kubernetes-list-type: map
                                readinessProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      format: int32
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          format: int32
                                          type: integer
                                        service:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            required:
                                            - name
                                            - value
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    initialDelaySeconds:
                                      format: int32
                                      type: integer
                                    periodSeconds:
                                      format: int32
                                      type: integer
                                    successThreshold:
                                      format: int32
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      required:
                                      - port
                                      type: object
                                    terminationGracePeriodSeconds:
                                      format: int64
                                      type: integer
                                    timeoutSeconds:
                                      format: int32
                                      type: integer
                                  type: object
                                resizePolicy:
                                  items:
                                    properties:
                                      resourceName:
                                        type: string
                                      restartPolicy:
                                        type: string
                                    required:
                                    - resourceName
                                    - restartPolicy
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                resources:
                                  properties:
                                    claims:
                                      items:
                                        properties:
                                          name:
                                            type: string
                                        required:
                                        - name
                                        type: object
                                      type: array
                                      x-kubernetes-list-map-keys:
                                      - name
                                      x-kubernetes-list-type: map
                                    limits:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                        x-kubernetes-int-or-string: true
                                      type: object
                                    requests:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                        x-kubernetes-int-or-string: true
                                      type: object
                                  type: object
                                restartPolicy:
                                  type: string
                                securityContext:
                                  properties:
                                    allowPrivilegeEscalation:
                                      type: boolean
                                    capabilities:
                                      properties:
                                        add:
                                          items:
                                            type: string
                                          type: array
                                        drop:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    privileged:
                                      type: boolean
                                    procMount:
                                      type: string
                                    readOnlyRootFilesystem:
                                      type: boolean
                                    runAsGroup:
                                      format: int64
                                      type: integer
                                    runAsNonRoot:
                                      type: boolean
                                    runAsUser:
                                      format: int64
                                      type: integer
                                    seLinuxOptions:
                                      properties:
                                        level:
                                          type: string
                                        role:
                                          type: string
                                        type:
                                          type: string
                                        user:
                                          type: string
                                      type: object
                                    seccompProfile:
                                      properties:
                                        localhostProfile:
                                          type: string
                                        type:
                                          type: string
                                      required:
                                      - type
                                      type: object
                                    windowsOptions:
                                      properties:
                                        gmsaCredentialSpec:
                                          type: string
                                        gmsaCredentialSpecName:
                                          type: string
                                        hostProcess:
                                          type: boolean
                                        runAsUserName:
                                          type: string
                                      type: object
                                  type: object
                                startupProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      format: int32
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          format: int32
                                          type: integer
                                        service:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            required:
                                            - name
                                            - value
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    initialDelaySeconds:
                                      format: int32
                                      type: integer
                                    periodSeconds:
                                      format: int32
                                      type: integer
                                    successThreshold:
                                      format: int32
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      required:
                                      - port
                                      type: object
                                    terminationGracePeriodSeconds:
                                      format: int64
                                      type: integer
                                    timeoutSeconds:
                                      format: int32
                                      type: integer
                                  type: object
                                stdin:
                                  type: boolean
                                stdinOnce:
                                  type: boolean
                                terminationMessagePath:
                                  type: string
                                terminationMessagePolicy:
                                  type: string
                                tty:
                                  type: boolean
                                volumeDevices:
                                  items:
                                    properties:
                                      devicePath:
                                        type: string
                                      name:
                                        type: string
                                    required:
                                    - devicePath
                                    - name
                                    type: object
                                  type: array
                                volumeMounts:
                                  items:
                                    properties:
                                      mountPath:
                                        type: string
                                      mountPropagation:
                                        type: string
                                      name:
                                        type: string
                                      readOnly:
                                        type: boolean
                                      subPath:
                                        type: string
                                      subPathExpr:
                                        type: string
                                    required:
                                    - mountPath
                                    - name
                                    type: object
                                  type: array
                                workingDir:
                                  type: string
                              required:
                              - name
                              type: object
                            type: array
                          dnsConfig:
                            properties:
                              nameservers:
                                items:
                                  type: string
                                type: array
                              options:
                                items:
                                  properties:
                                    name:
                                      type: string
                                    value:
                                      type: string
                                  type: object
                                type: array
                              searches:
                                items:
                                  type: string
                                type: array
                            type: object
                          dnsPolicy:
                            type: string
                          enableServiceLinks:
                            type: boolean
                          ephemeralContainers:
                            items:
                              properties:
                                args:
                                  items:
                                    type: string
                                  type: array
                                command:
                                  items:
                                    type: string
                                  type: array
                                env:
                                  items:
                                    properties:
                                      name:
                                        type: string
                                      value:
                                        type: string
                                      valueFrom:
                                        properties:
                                          configMapKeyRef:
                                            properties:
                                              key:
                                                type: string
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            required:
                                            - key
                                            type: object
                                            x-kubernetes-map-type: atomic
                                          fieldRef:
                                            properties:
                                              apiVersion:
                                                type: string
                                              fieldPath:
                                                type: string
                                            required:
                                            - fieldPath
                                            type: object
                                            x-kubernetes-map-type: atomic
                                          resourceFieldRef:
                                            properties:
                                              containerName:
                                                type: string
                                              divisor:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                                x-kubernetes-int-or-string: true
                                              resource:
                                                type: string
                                            required:
                                            - resource
                                            type: object
                                            x-kubernetes-map-type: atomic
                                          secretKeyRef:
                                            properties:
                                              key:
                                                type: string
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            required:
                                            - key
                                            type: object
                                            x-kubernetes-map-type: atomic
                                        type: object
                                    required:
                                    - name
                                    type: object
                                  type: array
                                envFrom:
                                  items:
                                    properties:
                                      configMapRef:
                                        properties:
                                          name:
                                            type: string
                                          optional:
                                            type: boolean
                                        type: object
                                        x-kubernetes-map-type: atomic
                                      prefix:
                                        type: string
                                      secretRef:
                                        properties:
                                          name:
                                            type: string
                                          optional:
                                            type: boolean
                                        type: object
                                        x-kubernetes-map-type: atomic
                                    type: object
                                  type: array
                                image:
                                  type: string
                                imagePullPolicy:
                                  type: string
                                lifecycle:
                                  properties:
                                    postStart:
                                      properties:
                                        exec:
                                          properties:
                                            command:
                                              items:
                                                type: string
                                              type: array
                                          type: object
                                        httpGet:
                                          properties:
                                            host:
                                              type: string
                                            httpHeaders:
                                              items:
                                                properties:
                                                  name:
                                                    type: string
                                                  value:
                                                    type: string
                                                required:
                                                - name
                                                - value
                                                type: object
                                              type: array
                                            path:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                            scheme:
                                              type: string
                                          required:
                                          - port
                                          type: object
                                        sleep:
                                          properties:
                                            seconds:
                                              format: int64
                                              type: integer
                                          required:
                                          - seconds
                                          type: object
                                        tcpSocket:
                                          properties:
                                            host:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                          required:
                                          - port
                                          type: object
                                      type: object
                                    preStop:
                                      properties:
                                        exec:
                                          properties:
                                            command:
                                              items:
                                                type: string
                                              type: array
                                          type: object
                                        httpGet:
                                          properties:
                                            host:
                                              type: string
                                            httpHeaders:
                                              items:
                                                properties:
                                                  name:
                                                    type: string
                                                  value:
                                                    type: string
                                                required:
                                                - name
                                                - value
                                                type: object
                                              type: array
                                            path:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                            scheme:
                                              type: string
                                          required:
                                          - port
                                          type: object
                                        sleep:
                                          properties:
                                            seconds:
                                              format: int64
                                              type: integer
                                          required:
                                          - seconds
                                          type: object
                                        tcpSocket:
                                          properties:
                                            host:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                          required:
                                          - port
                                          type: object
                                      type: object
                                  type: object
                                livenessProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      format: int32
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          format: int32
                                          type: integer
                                        service:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            required:
                                            - name
                                            - value
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    initialDelaySeconds:
                                      format: int32
                                      type: integer
                                    periodSeconds:
                                      format: int32
                                      type: integer
                                    successThreshold:
                                      format: int32
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      required:
                                      - port
                                      type: object
                                    terminationGracePeriodSeconds:
                                      format: int64
                                      type: integer
                                    timeoutSeconds:
                                      format: int32
                                      type: integer
                                  type: object
                                name:
                                  type: string
                                ports:
                                  items:
                                    properties:
                                      containerPort:
                                        format: int32
                                        type: integer
                                      hostIP:
                                        type: string
                                      hostPort:
                                        format: int32
                                        type: integer
                                      name:
                                        type: string
                                      protocol:
                                        default: TCP
                                        type: string
                                    required:
                                    - containerPort
                                    type: object
                                  type: array
                                  x-kubernetes-list-map-keys:
                                  - containerPort
                                  - protocol
                                  x-kubernetes-list-type: map
                                readinessProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      format: int32
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          format: int32
                                          type: integer
                                        service:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            required:
                                            - name
                                            - value
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    initialDelaySeconds:
                                      format: int32
                                      type: integer
                                    periodSeconds:
                                      format: int32
                                      type: integer
                                    successThreshold:
                                      format: int32
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      required:
                                      - port
                                      type: object
                                    terminationGracePeriodSeconds:
                                      format: int64
                                      type: integer
                                    timeoutSeconds:
                                      format: int32
                                      type: integer
                                  type: object
                                resizePolicy:
                                  items:
                                    properties:
                                      resourceName:
                                        type: string
                                      restartPolicy:
                                        type: string
                                    required:
                                    - resourceName
                                    - restartPolicy
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                resources:
                                  properties:
                                    claims:
                                      items:
                                        properties:
                                          name:
                                            type: string
                                        required:
                                        - name
                                        type: object
                                      type: array
                                      x-kubernetes-list-map-keys:
                                      - name
                                      x-kubernetes-list-type: map
                                    limits:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                        x-kubernetes-int-or-string: true
                                      type: object
                                    requests:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                        x-kubernetes-int-or-string: true
                                      type: object
                                  type: object
                                restartPolicy:
                                  type: string
                                securityContext:
                                  properties:
                                    allowPrivilegeEscalation:
                                      type: boolean
                                    capabilities:
                                      properties:
                                        add:
                                          items:
                                            type: string
                                          type: array
                                        drop:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    privileged:
                                      type: boolean
                                    procMount:
                                      type: string
                                    readOnlyRootFilesystem:
                                      type: boolean
                                    runAsGroup:
                                      format: int64
                                      type: integer
                                    runAsNonRoot:
                                      type: boolean
                                    runAsUser:
                                      format: int64
                                      type: integer
                                    seLinuxOptions:
                                      properties:
                                        level:
                                          type: string
                                        role:
                                          type: string
                                        type:
                                          type: string
                                        user:
                                          type: string
                                      type: object
                                    seccompProfile:
                                      properties:
                                        localhostProfile:
                                          type: string
                                        type:
                                          type: string
                                      required:
                                      - type
                                      type: object
                                    windowsOptions:
                                      properties:
                                        gmsaCredentialSpec:
                                          type: string
                                        gmsaCredentialSpecName:
                                          type: string
                                        hostProcess:
                                          type: boolean
                                        runAsUserName:
                                          type: string
                                      type: object
                                  type: object
                                startupProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      format: int32
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          format: int32
                                          type: integer
                                        service:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            required:
                                            - name
                                            - value
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    initialDelaySeconds:
                                      format: int32
                                      type: integer
                                    periodSeconds:
                                      format: int32
                                      type: integer
                                    successThreshold:
                                      format: int32
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      required:
                                      - port
                                      type: object
                                    terminationGracePeriodSeconds:
                                      format: int64
                                      type: integer
                                    timeoutSeconds:
                                      format: int32
                                      type: integer
                                  type: object
                                stdin:
                                  type: boolean
                                stdinOnce:
                                  type: boolean
                                targetContainerName:
                                  type: string
                                terminationMessagePath:
                                  type: string
                                terminationMessagePolicy:
                                  type: string
                                tty:
                                  type: boolean
                                volumeDevices:
                                  items:
                                    properties:
                                      devicePath:
                                        type: string
                                      name:
                                        type: string
                                    required:
                                    - devicePath
                                    - name
                                    type: object
                                  type: array
                                volumeMounts:
                                  items:
                                    properties:
                                      mountPath:
                                        type: string
                                      mountPropagation:
                                        type: string
                                      name:
                                        type: string
                                      readOnly:
                                        type: boolean
                                      subPath:
                                        type: string
                                      subPathExpr:
                                        type: string
                                    required:
                                    - mountPath
                                    - name
                                    type: object
                                  type: array
                                workingDir:
                                  type: string
                              required:
                              - name
                              type: object
                            type: array
                          hostAliases:
                            items:
                              properties:
                                hostnames:
                                  items:
                                    type: string
                                  type: array
                                ip:
                                  type: string
                              type: object
                            type: array
                          hostIPC:
                            type: boolean
                          hostNetwork:
                            type: boolean
                          hostPID:
                            type: boolean
                          hostname:
                            type: string
                          imagePullSecrets:
                            items:
                              properties:
                                name:
                                  type: string
                              type: object
                              x-kubernetes-map-type: atomic
                            type: array
                          initContainers:
                            items:
                              properties:
                                args:
                                  items:
                                    type: string
                                  type: array
                                command:
                                  items:
                                    type: string
                                  type: array
                                env:
                                  items:
                                    properties:
                                      name:
                                        type: string
                                      value:
                                        type: string
                                      valueFrom:
                                        properties:
                                          configMapKeyRef:
                                            properties:
                                              key:
                                                type: string
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            required:
                                            - key
                                            type: object
                                            x-kubernetes-map-type: atomic
                                          fieldRef:
                                            properties:
                                              apiVersion:
                                                type: string
                                              fieldPath:
                                                type: string
                                            required:
                                            - fieldPath
                                            type: object
                                            x-kubernetes-map-type: atomic
                                          resourceFieldRef:
                                            properties:
                                              containerName:
                                                type: string
                                              divisor:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                                x-kubernetes-int-or-string: true
                                              resource:
                                                type: string
                                            required:
                                            - resource
                                            type: object
                                            x-kubernetes-map-type: atomic
                                          secretKeyRef:
                                            properties:
                                              key:
                                                type: string
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            required:
                                            - key
                                            type: object
                                            x-kubernetes-map-type: atomic
                                        type: object
                                    required:
                                    - name
                                    type: object
                                  type: array
                                envFrom:
                                  items:
                                    properties:
                                      configMapRef:
                                        properties:
                                          name:
                                            type: string
                                          optional:
                                            type: boolean
                                        type: object
                                        x-kubernetes-map-type: atomic
                                      prefix:
                                        type: string
                                      secretRef:
                                        properties:
                                          name:
                                            type: string
                                          optional:
                                            type: boolean
                                        type: object
                                        x-kubernetes-map-type: atomic
                                    type: object
                                  type: array
                                image:
                                  type: string
                                imagePullPolicy:
                                  type: string
                                lifecycle:
                                  properties:
                                    postStart:
                                      properties:
                                        exec:
                                          properties:
                                            command:
                                              items:
                                                type: string
                                              type: array
                                          type: object
                                        httpGet:
                                          properties:
                                            host:
                                              type: string
                                            httpHeaders:
                                              items:
                                                properties:
                                                  name:
                                                    type: string
                                                  value:
                                                    type: string
                                                required:
                                                - name
                                                - value
                                                type: object
                                              type: array
                                            path:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                            scheme:
                                              type: string
                                          required:
                                          - port
                                          type: object
                                        sleep:
                                          properties:
                                            seconds:
                                              format: int64
                                              type: integer
                                          required:
                                          - seconds
                                          type: object
                                        tcpSocket:
                                          properties:
                                            host:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                          required:
                                          - port
                                          type: object
                                      type: object
                                    preStop:
                                      properties:
                                        exec:
                                          properties:
                                            command:
                                              items:
                                                type: string
                                              type: array
                                          type: object
                                        httpGet:
                                          properties:
                                            host:
                                              type: string
                                            httpHeaders:
                                              items:
                                                properties:
                                                  name:
                                                    type: string
                                                  value:
                                                    type: string
                                                required:
                                                - name
                                                - value
                                                type: object
                                              type: array
                                            path:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                            scheme:
                                              type: string
                                          required:
                                          - port
                                          type: object
                                        sleep:
                                          properties:
                                            seconds:
                                              format: int64
                                              type: integer
                                          required:
                                          - seconds
                                          type: object
                                        tcpSocket:
                                          properties:
                                            host:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                          required:
                                          - port
                                          type: object
                                      type: object
                                  type: object
                                livenessProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      format: int32
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          format: int32
                                          type: integer
                                        service:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            required:
                                            - name
                                            - value
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    initialDelaySeconds:
                                      format: int32
                                      type: integer
                                    periodSeconds:
                                      format: int32
                                      type: integer
                                    successThreshold:
                                      format: int32
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      required:
                                      - port
                                      type: object
                                    terminationGracePeriodSeconds:
                                      format: int64
                                      type: integer
                                    timeoutSeconds:
                                      format: int32
                                      type: integer
                                  type: object
                                name:
                                  type: string
                                ports:
                                  items:
                                    properties:
                                      containerPort:
                                        format: int32
                                        type: integer
                                      hostIP:
                                        type: string
                                      hostPort:
                                        format: int32
                                        type: integer
                                      name:
                                        type: string
                                      protocol:
                                        default: TCP
                                        type: string
                                    required:
                                    - containerPort
                                    type: object
                                  type: array
                                  x-kubernetes-list-map-keys:
                                  - containerPort
                                  - protocol
                                  x-kubernetes-list-type: map
                                readinessProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      format: int32
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          format: int32
                                          type: integer
                                        service:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            required:
                                            - name
                                            - value
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    initialDelaySeconds:
                                      format: int32
                                      type: integer
                                    periodSeconds:
                                      format: int32
                                      type: integer
                                    successThreshold:
                                      format: int32
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      required:
                                      - port
                                      type: object
                                    terminationGracePeriodSeconds:
                                      format: int64
                                      type: integer
                                    timeoutSeconds:
                                      format: int32
                                      type: integer
                                  type: object
                                resizePolicy:
                                  items:
                                    properties:
                                      resourceName:
                                        type: string
                                      restartPolicy:
                                        type: string
                                    required:
                                    - resourceName
                                    - restartPolicy
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                resources:
                                  properties:
                                    claims:
                                      items:
                                        properties:
                                          name:
                                            type: string
                                        required:
                                        - name
                                        type: object
                                      type: array
                                      x-kubernetes-list-map-keys:
                                      - name
                                      x-kubernetes-list-type: map
                                    limits:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                        x-kubernetes-int-or-string: true
                                      type: object
                                    requests:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                        x-kubernetes-int-or-string: true
                                      type: object
                                  type: object
                                restartPolicy:
                                  type: string
                                securityContext:
                                  properties:
                                    allowPrivilegeEscalation:
                                      type: boolean
                                    capabilities:
                                      properties:
                                        add:
                                          items:
                                            type: string
                                          type: array
                                        drop:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    privileged:
                                      type: boolean
                                    procMount:
                                      type: string
                                    readOnlyRootFilesystem:
                                      type: boolean
                                    runAsGroup:
                                      format: int64
                                      type: integer
                                    runAsNonRoot:
                                      type: boolean
                                    runAsUser:
                                      format: int64
                                      type: integer
                                    seLinuxOptions:
                                      properties:
                                        level:
                                          type: string
                                        role:
                                          type: string
                                        type:
                                          type: string
                                        user:
                                          type: string
                                      type: object
                                    seccompProfile:
                                      properties:
                                        localhostProfile:
                                          type: string
                                        type:
                                          type: string
                                      required:
                                      - type
                                      type: object
                                    windowsOptions:
                                      properties:
                                        gmsaCredentialSpec:
                                          type: string
                                        gmsaCredentialSpecName:
                                          type: string
                                        hostProcess:
                                          type: boolean
                                        runAsUserName:
                                          type: string
                                      type: object
                                  type: object
                                startupProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      format: int32
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          format: int32
                                          type: integer
                                        service:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            required:
                                            - name
                                            - value
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    initialDelaySeconds:
                                      format: int32
                                      type: integer
                                    periodSeconds:
                                      format: int32
                                      type: integer
                                    successThreshold:
                                      format: int32
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      required:
                                      - port
                                      type: object
                                    terminationGracePeriodSeconds:
                                      format: int64
                                      type: integer
                                    timeoutSeconds:
                                      format: int32
                                      type: integer
                                  type: object
                                stdin:
                                  type: boolean
                                stdinOnce:
                                  type: boolean
                                terminationMessagePath:
                                  type: string
                                terminationMessagePolicy:
                                  type: string
                                tty:
                                  type: boolean
                                volumeDevices:
                                  items:
                                    properties:
                                      devicePath:
                                        type: string
                                      name:
                                        type: string
                                    required:
                                    - devicePath
                                    - name
                                    type: object
                                  type: array
                                volumeMounts:
                                  items:
                                    properties:
                                      mountPath:
                                        type: string
                                      mountPropagation:
                                        type: string
                                      name:
                                        type: string
                                      readOnly:
                                        type: boolean
                                      subPath:
                                        type: string
                                      subPathExpr:
                                        type: string
                                    required:
                                    - mountPath
                                    - name
                                    type: object
                                  type: array
                                workingDir:
                                  type: string
                              required:
                              - name
                              type: object
                            type: array
                          nodeName:
                            type: string
                          nodeSelector:
                            additionalProperties:
                              type: string
                            type: object
                            x-kubernetes-map-type: atomic
                          os:
                            properties:
                              name:
                                type: string
                            required:
                            - name
                            type: object
                          overhead:
                            additionalProperties:
                              anyOf:
                              - type: integer
                              - type: string
                              pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                              x-kubernetes-int-or-string: true
                            type: object
                          preemptionPolicy:
                            type: string
                          priority:
                            format: int32
                            type: integer
                          priorityClassName:
                            type: string
                          readinessGates:
                            items:
                              properties:
                                conditionType:
                                  type: string
                              required:
                              - conditionType
                              type: object
                            type: array
                          restartPolicy:
                            type: string
                          runtimeClassName:
                            type: string
                          schedulerName:
                            type: string
                          securityContext:
                            properties:
                              fsGroup:
                                format: int64
                                type: integer
                              fsGroupChangePolicy:
                                type: string
                              runAsGroup:
                                format: int64
                                type: integer
                              runAsNonRoot:
                                type: boolean
                              runAsUser:
                                format: int64
                                type: integer
                              seLinuxOptions:
                                properties:
                                  level:
                                    type: string
                                  role:
                                    type: string
                                  type:
                                    type: string
                                  user:
                                    type: string
                                type: object
                              seccompProfile:
                                properties:
                                  localhostProfile:
                                    type: string
                                  type:
                                    type: string
                                required:
                                - type
                                type: object
                              supplementalGroups:
                                items:
                                  format: int64
                                  type: integer
                                type: array
                              sysctls:
                                items:
                                  properties:
                                    name:
                                      type: string
                                    value:
                                      type: string
                                  required:
                                  - name
                                  - value
                                  type: object
                                type: array
                              windowsOptions:
                                properties:
                                  gmsaCredentialSpec:
                                    type: string
                                  gmsaCredentialSpecName:
                                    type: string
                                  hostProcess:
                                    type: boolean
                                  runAsUserName:
                                    type: string
                                type: object
                            type: object
                          serviceAccount:
                            type: string
                          serviceAccountName:
                            type: string
                          setHostnameAsFQDN:
                            type: boolean
                          shareProcessNamespace:
                            type: boolean
                          subdomain:
                            type: string
                          terminationGracePeriodSeconds:
                            format: int64
                            type: integer
                          tolerations:
                            items:
                              properties:
                                effect:
                                  type: string
                                key:
                                  type: string
                                operator:
                                  type: string
                                tolerationSeconds:
                                  format: int64
                                  type: integer
                                value:
                                  type: string
                              type: object
                            type: array
                          topologySpreadConstraints:
                            items:
                              properties:
                                labelSelector:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                matchLabelKeys:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                maxSkew:
                                  format: int32
                                  type: integer
                                minDomains:
                                  format: int32
                                  type: integer
                                nodeAffinityPolicy:
                                  type: string
                                nodeTaintsPolicy:
                                  type: string
                                topologyKey:
                                  type: string
                                whenUnsatisfiable:
                                  type: string
                              required:
                              - maxSkew
                              - topologyKey
                              - whenUnsatisfiable
                              type: object
                            type: array
                            x-kubernetes-list-map-keys:
                            - topologyKey
                            - whenUnsatisfiable
                            x-kubernetes-list-type: map
                          volumes:
                            items:
                              properties:
                                awsElasticBlockStore:
                                  properties:
                                    fsType:
                                      type: string
                                    partition:
                                      format: int32
                                      type: integer
                                    readOnly:
                                      type: boolean
                                    volumeID:
                                      type: string
                                  required:
                                  - volumeID
                                  type: object
                                azureDisk:
                                  properties:
                                    cachingMode:
                                      type: string
                                    diskName:
                                      type: string
                                    diskURI:
                                      type: string
                                    fsType:
                                      type: string
                                    kind:
                                      type: string
                                    readOnly:
                                      type: boolean
                                  required:
                                  - diskName
                                  - diskURI
                                  type: object
                                azureFile:
                                  properties:
                                    readOnly:
                                      type: boolean
                                    secretName:
                                      type: string
                                    shareName:
                                      type: string
                                  required:
                                  - secretName
                                  - shareName
                                  type: object
                                cephfs:
                                  properties:
                                    monitors:
                                      items:
                                        type: string
                                      type: array
                                    path:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    secretFile:
                                      type: string
                                    secretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                      x-kubernetes-map-type: atomic
                                    user:
                                      type: string
                                  required:
                                  - monitors
                                  type: object
                                cinder:
                                  properties:
                                    fsType:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    secretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                      x-kubernetes-map-type: atomic
                                    volumeID:
                                      type: string
                                  required:
                                  - volumeID
                                  type: object
                                configMap:
                                  properties:
                                    defaultMode:
                                      format: int32
                                      type: integer
                                    items:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          mode:
                                            format: int32
                                            type: integer
                                          path:
                                            type: string
                                        required:
                                        - key
                                        - path
                                        type: object
                                      type: array
                                    name:
                                      type: string
                                    optional:
                                      type: boolean
                                  type: object
                                  x-kubernetes-map-type: atomic
                                csi:
                                  properties:
                                    driver:
                                      type: string
                                    fsType:
                                      type: string
                                    nodePublishSecretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                      x-kubernetes-map-type: atomic
                                    readOnly:
                                      type: boolean
                                    volumeAttributes:
                                      additionalProperties:
                                        type: string
                                      type: object
                                  required:
                                  - driver
                                  type: object
                                downwardAPI:
                                  properties:
                                    defaultMode:
                                      format: int32
                                      type: integer
                                    items:
                                      items:
                                        properties:
                                          fieldRef:
                                            properties:
                                              apiVersion:
                                                type: string
                                              fieldPath:
                                                type: string
                                            required:
                                            - fieldPath
                                            type: object
                                            x-kubernetes-map-type: atomic
                                          mode:
                                            format: int32
                                            type: integer
                                          path:
                                            type: string
                                          resourceFieldRef:
                                            properties:
                                              containerName:
                                                type: string
                                              divisor:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                                x-kubernetes-int-or-string: true
                                              resource:
                                                type: string
                                            required:
                                            - resource
                                            type: object
                                            x-kubernetes-map-type: atomic
                                        required:
                                        - path
                                        type: object
                                      type: array
                                  type: object
                                emptyDir:
                                  properties:
                                    medium:
                                      type: string
                                    sizeLimit:
                                      anyOf:
                                      - type: integer
                                      - type: string
                                      pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                      x-kubernetes-int-or-string: true
                                  type: object
                                ephemeral:
                                  properties:
                                    volumeClaimTemplate:
                                      properties:
                                        metadata:
                                          type: object
                                        spec:
                                          properties:
                                            accessModes:
                                              items:
                                                type: string
                                              type: array
                                            dataSource:
                                              properties:
                                                apiGroup:
                                                  type: string
                                                kind:
                                                  type: string
                                                name:
                                                  type: string
                                              required:
                                              - kind
                                              - name
                                              type: object
                                              x-kubernetes-map-type: atomic
                                            dataSourceRef:
                                              properties:
                                                apiGroup:
                                                  type: string
                                                kind:
                                                  type: string
                                                name:
                                                  type: string
                                                namespace:
                                                  type: string
                                              required:
                                              - kind
                                              - name
                                              type: object
                                            resources:
                                              properties:
                                                limits:
                                                  additionalProperties:
                                                    anyOf:
                                                    - type: integer
                                                    - type: string
                                                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                                    x-kubernetes-int-or-string: true
                                                  type: object
                                                requests:
                                                  additionalProperties:
                                                    anyOf:
                                                    - type: integer
                                                    - type: string
                                                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                                    x-kubernetes-int-or-string: true
                                                  type: object
                                              type: object
                                            selector:
                                              properties:
                                                matchExpressions:
                                                  items:
                                                    properties:
                                                      key:
                                                        type: string
                                                      operator:
                                                        type: string
                                                      values:
                                                        items:
                                                          type: string
                                                        type: array
                                                    required:
                                                    - key
                                                    - operator
                                                    type: object
                                                  type: array
                                                matchLabels:
                                                  additionalProperties:
                                                    type: string
                                                  type: object
                                              type: object
                                              x-kubernetes-map-type: atomic
                                            storageClassName:
                                              type: string
                                            volumeAttributesClassName:
                                              type: string
                                            volumeMode:
                                              type: string
                                            volumeName:
                                              type: string
                                          type: object
                                      required:
                                      - spec
                                      type: object
                                  type: object
                                fc:
                                  properties:
                                    fsType:
                                      type: string
                                    lun:
                                      format: int32
                                      type: integer
                                    readOnly:
                                      type: boolean
                                    targetWWNs:
                                      items:
                                        type: string
                                      type: array
                                    wwids:
                                      items:
                                        type: string
                                      type: array
                                  type: object
                                flexVolume:
                                  properties:
                                    driver:
                                      type: string
                                    fsType:
                                      type: string
                                    options:
                                      additionalProperties:
                                        type: string
                                      type: object
                                    readOnly:
                                      type: boolean
                                    secretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                      x-kubernetes-map-type: atomic
                                  required:
                                  - driver
                                  type: object
                                flocker:
                                  properties:
                                    datasetName:
                                      type: string
                                    datasetUUID:
                                      type: string
                                  type: object
                                gcePersistentDisk:
                                  properties:
                                    fsType:
                                      type: string
                                    partition:
                                      format: int32
                                      type: integer
                                    pdName:
                                      type: string
                                    readOnly:
                                      type: boolean
                                  required:
                                  - pdName
                                  type: object
                                gitRepo:
                                  properties:
                                    directory:
                                      type: string
                                    repository:
                                      type: string
                                    revision:
                                      type: string
                                  required:
                                  - repository
                                  type: object
                                glusterfs:
                                  properties:
                                    endpoints:
                                      type: string
                                    path:
                                      type: string
                                    readOnly:
                                      type: boolean
                                  required:
                                  - endpoints
                                  - path
                                  type: object
                                hostPath:
                                  properties:
                                    path:
                                      type: string
                                    type:
                                      type: string
                                  required:
                                  - path
                                  type: object
                                iscsi:
                                  properties:
                                    chapAuthDiscovery:
                                      type: boolean
                                    chapAuthSession:
                                      type: boolean
                                    fsType:
                                      type: string
                                    initiatorName:
                                      type: string
                                    iqn:
                                      type: string
                                    iscsiInterface:
                                      type: string
                                    lun:
                                      format: int32
                                      type: integer
                                    portals:
                                      items:
                                        type: string
                                      type: array
                                    readOnly:
                                      type: boolean
                                    secretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                      x-kubernetes-map-type: atomic
                                    targetPortal:
                                      type: string
                                  required:
                                  - iqn
                                  - lun
                                  - targetPortal
                                  type: object
                                name:
                                  type: string
                                nfs:
                                  properties:
                                    path:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    server:
                                      type: string
                                  required:
                                  - path
                                  - server
                                  type: object
                                persistentVolumeClaim:
                                  properties:
                                    claimName:
                                      type: string
                                    readOnly:
                                      type: boolean
                                  required:
                                  - claimName
                                  type: object
                                photonPersistentDisk:
                                  properties:
                                    fsType:
                                      type: string
                                    pdID:
                                      type: string
                                  required:
                                  - pdID
                                  type: object
                                portworxVolume:
                                  properties:
                                    fsType:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    volumeID:
                                      type: string
                                  required:
                                  - volumeID
                                  type: object
                                projected:
                                  properties:
                                    defaultMode:
                                      format: int32
                                      type: integer
                                    sources:
                                      items:
                                        properties:
                                          clusterTrustBundle:
                                            properties:
                                              labelSelector:
                                                properties:
                                                  matchExpressions:
                                                    items:
                                                      properties:
                                                        key:
                                                          type: string
                                                        operator:
                                                          type: string
                                                        values:
                                                          items:
                                                            type: string
                                                          type: array
                                                      required:
                                                      - key
                                                      - operator
                                                      type: object
                                                    type: array
                                                  matchLabels:
                                                    additionalProperties:
                                                      type: string
                                                    type: object
                                                type: object
                                                x-kubernetes-map-type: atomic
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                              path:
                                                type: string
                                              signerName:
                                                type: string
                                            required:
                                            - path
                                            type: object
                                          configMap:
                                            properties:
                                              items:
                                                items:
                                                  properties:
                                                    key:
                                                      type: string
                                                    mode:
                                                      format: int32
                                                      type: integer
                                                    path:
                                                      type: string
                                                  required:
                                                  - key
                                                  - path
                                                  type: object
                                                type: array
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            type: object
                                            x-kubernetes-map-type: atomic
                                          downwardAPI:
                                            properties:
                                              items:
                                                items:
                                                  properties:
                                                    fieldRef:
                                                      properties:
                                                        apiVersion:
                                                          type: string
                                                        fieldPath:
                                                          type: string
                                                      required:
                                                      - fieldPath
                                                      type: object
                                                      x-kubernetes-map-type: atomic
                                                    mode:
                                                      format: int32
                                                      type: integer
                                                    path:
                                                      type: string
                                                    resourceFieldRef:
                                                      properties:
                                                        containerName:
                                                          type: string
                                                        divisor:
                                                          anyOf:
                                                          - type: integer
                                                          - type: string
                                                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                                          x-kubernetes-int-or-string: true
                                                        resource:
                                                          type: string
                                                      required:
                                                      - resource
                                                      type: object
                                                      x-kubernetes-map-type: atomic
                                                  required:
                                                  - path
                                                  type: object
                                                type: array
                                            type: object
                                          secret:
                                            properties:
                                              items:
                                                items:
                                                  properties:
                                                    key:
                                                      type: string
                                                    mode:
                                                      format: int32
                                                      type: integer
                                                    path:
                                                      type: string
                                                  required:
                                                  - key
                                                  - path
                                                  type: object
                                                type: array
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            type: object
                                            x-kubernetes-map-type: atomic
                                          serviceAccountToken:
                                            properties:
                                              audience:
                                                type: string
                                              expirationSeconds:
                                                format: int64
                                                type: integer
                                              path:
                                                type: string
                                            required:
                                            - path
                                            type: object
                                        type: object
                                      type: array
                                  type: object
                                quobyte:
                                  properties:
                                    group:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    registry:
                                      type: string
                                    tenant:
                                      type: string
                                    user:
                                      type: string
                                    volume:
                                      type: string
                                  required:
                                  - registry
                                  - volume
                                  type: object
                                rbd:
                                  properties:
                                    fsType:
                                      type: string
                                    image:
                                      type: string
                                    keyring:
                                      type: string
                                    monitors:
                                      items:
                                        type: string
                                      type: array
                                    pool:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    secretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                      x-kubernetes-map-type: atomic
                                    user:
                                      type: string
                                  required:
                                  - image
                                  - monitors
                                  type: object
                                scaleIO:
                                  properties:
                                    fsType:
                                      type: string
                                    gateway:
                                      type: string
                                    protectionDomain:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    secretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                      x-kubernetes-map-type: atomic
                                    sslEnabled:
                                      type: boolean
                                    storageMode:
                                      type: string
                                    storagePool:
                                      type: string
                                    system:
                                      type: string
                                    volumeName:
                                      type: string
                                  required:
                                  - gateway
                                  - secretRef
                                  - system
                                  type: object
                                secret:
                                  properties:
                                    defaultMode:
                                      format: int32
                                      type: integer
                                    items:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          mode:
                                            format: int32
                                            type: integer
                                          path:
                                            type: string
                                        required:
                                        - key
                                        - path
                                        type: object
                                      type: array
                                    optional:
                                      type: boolean
                                    secretName:
                                      type: string
                                  type: object
                                storageos:
                                  properties:
                                    fsType:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    secretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                      x-kubernetes-map-type: atomic
                                    volumeName:
                                      type: string
                                    volumeNamespace:
                                      type: string
                                  type: object
                                vsphereVolume:
                                  properties:
                                    fsType:
                                      type: string
                                    storagePolicyID:
                                      type: string
                                    storagePolicyName:
                                      type: string
                                    volumePath:
                                      type: string
                                  required:
                                  - volumePath
                                  type: object
                              required:
                              - name
                              type: object
                            type: array
                        type: object
                    type: object
                  replicas:
                    description: "Replicas: \n The required number of Apicurio Registry
                      pods. Default value is 1."
                    format: int32
                    type: integer
                  tolerations:
                    description: Tolerations
                    items:
                      description: The pod this Toleration is attached to tolerates
                        any taint that matches the triple <key,value,effect> using
                        the matching operator <operator>.
                      properties:
                        effect:
                          description: Effect indicates the taint effect to match.
                            Empty means match all taint effects. When specified, allowed
                            values are NoSchedule, PreferNoSchedule and NoExecute.
                          type: string
                        key:
                          description: Key is the taint key that the toleration applies
                            to. Empty means match all taint keys. If the key is empty,
                            operator must be Exists; this combination means to match
                            all values and all keys.
                          type: string
                        operator:
                          description: Operator represents a key's relationship to
                            the value. Valid operators are Exists and Equal. Defaults
                            to Equal. Exists is equivalent to wildcard for value,
                            so that a pod can tolerate all taints of a particular
                            category.
                          type: string
                        tolerationSeconds:
                          description: TolerationSeconds represents the period of
                            time the toleration (which must be of effect NoExecute,
                            otherwise this field is ignored) tolerates the taint.
                            By default, it is not set, which means tolerate the taint
                            forever (do not evict). Zero and negative values will
                            be treated as 0 (evict immediately) by the system.
                          format: int64
                          type: integer
                        value:
                          description: Value is the taint value the toleration matches
                            to. If the operator is Exists, the value should be empty,
                            otherwise just a regular string.
                          type: string
                      type: object
                    type: array
                type: object
            type: object
          status:
            properties:
              conditions:
                description: "Conditions: \n Apicurio Registry application and Operator
                  conditions."
                items:
                  description: "Condition contains details for one aspect of the current
                    state of this API Resource. --- This struct is intended for direct
                    use as an array at the field path .status.conditions.  For example,
                    \n type FooStatus struct{ // Represents the observations of a
                    foo's current state. // Known .status.conditions.type are: \"Available\",
                    \"Progressing\", and \"Degraded\" // +patchMergeKey=type // +patchStrategy=merge
                    // +listType=map // +listMapKey=type Conditions []metav1.Condition
                    `json:\"conditions,omitempty\" patchStrategy:\"merge\" patchMergeKey:\"type\"
                    protobuf:\"bytes,1,rep,name=conditions\"` \n // other fields }"
                  properties:
                    lastTransitionTime:
                      description: lastTransitionTime is the last time the condition
                        transitioned from one status to another. This should be when
                        the underlying condition changed.  If that is not known, then
                        using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: message is a human readable message indicating
                        details about the transition. This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: observedGeneration represents the .metadata.generation
                        that the condition was set based upon. For instance, if .metadata.generation
                        is currently 12, but the .status.conditions[x].observedGeneration
                        is 9, the condition is out of date with respect to the current
                        state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: reason contains a programmatic identifier indicating
                        the reason for the condition's last transition. Producers
                        of specific condition types may define expected values and
                        meanings for this field, and whether the values are considered
                        a guaranteed API. The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                        --- Many .condition.type values are consistent across resources
                        like Available, but because arbitrary conditions can be useful
                        (see .node.status.conditions), the ability to deconflict is
                        important. The regex it matches is (dns1123SubdomainFmt/)?(qualifiedNameFmt)
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              info:
                description: Information about the Apicurio Registry application
                properties:
                  host:
                    description: Apicurio Registry URL
                    type: string
                type: object
              managedResources:
                description: "Managed Resources: \n Kubernetes resources managed by
                  the Apicurio Registry Operator."
                items:
                  properties:
                    kind:
                      type: string
                    name:
                      type: string
                    namespace:
                      type: string
                  type: object
                type: array
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    apicur.io/name: apicurio-registry-operator
    apicur.io/type: operator
    apicur.io/version: 1.1.3
  name: apicurio-registry-operator
  namespace: apicurio-registry-operator-namespace
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  labels:
    apicur.io/name: apicurio-registry-operator
    apicur.io/type: operator
    apicur.io/version: 1.1.3
  name: apicurio-registry-operator-leader-election-role
  namespace: apicurio-registry-operator-namespace
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    apicur.io/name: apicurio-registry-operator
    apicur.io/type: operator
    apicur.io/version: 1.1.3
  name: apicurio-registry-operator-role
rules:
- apiGroups:
  - apps
  resources:
  - daemonsets
  - deployments
  - replicasets
  - statefulsets
  verbs:
  - '*'
- apiGroups:
  - config.openshift.io
  resources:
  - clusterversions
  verbs:
  - get
- apiGroups:
  - ""
  resources:
  - configmaps
  - endpoints
  - persistentvolumeclaims
  - pods
  - secrets
  - services
  - services/finalizers
  verbs:
  - '*'
- apiGroups:
  - events
  resources:
  - events
  verbs:
  - '*'
- apiGroups:
  - monitoring.coreos.com
  resources:
  - servicemonitors
  verbs:
  - '*'
- apiGroups:
  - networking.k8s.io
  resources:
  - ingresses
  verbs:
  - '*'
- apiGroups:
  - networking.k8s.io
  resources:
  - networkpolicies
  verbs:
  - '*'
- apiGroups:
  - policy
  resources:
  - poddisruptionbudgets
  verbs:
  - '*'
- apiGroups:
  - registry.apicur.io
  resources:
  - apicurioregistries
  verbs:
  - '*'
- apiGroups:
  - registry.apicur.io
  resources:
  - apicurioregistries/finalizers
  verbs:
  - update
- apiGroups:
  - registry.apicur.io
  resources:
  - apicurioregistries/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - route.openshift.io
  resources:
  - routes
  - routes/custom-host
  verbs:
  - '*'
- apiGroups:
  - security.openshift.io
  resources:
  - securitycontextconstraints
  verbs:
  - use
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  labels:
    apicur.io/name: apicurio-registry-operator
    apicur.io/type: operator
    apicur.io/version: 1.1.3
  name: apicurio-registry-operator-leader-election-rolebinding
  namespace: apicurio-registry-operator-namespace
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: apicurio-registry-operator-leader-election-role
subjects:
- kind: ServiceAccount
  name: apicurio-registry-operator
  namespace: apicurio-registry-operator-namespace
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    apicur.io/name: apicurio-registry-operator
    apicur.io/type: operator
    apicur.io/version: 1.1.3
  name: apicurio-registry-operator-rolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: apicurio-registry-operator-role
subjects:
- kind: ServiceAccount
  name: apicurio-registry-operator
  namespace: apicurio-registry-operator-namespace
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    apicur.io/name: apicurio-registry-operator
    apicur.io/type: operator
    apicur.io/version: 1.1.3
    name: apicurio-registry-operator
  name: apicurio-registry-operator
  namespace: apicurio-registry-operator-namespace
spec:
  replicas: 1
  selector:
    matchLabels:
      apicur.io/name: apicurio-registry-operator
      apicur.io/type: operator
      apicur.io/version: 1.1.3
      name: apicurio-registry-operator
  template:
    metadata:
      labels:
        apicur.io/name: apicurio-registry-operator
        apicur.io/type: operator
        apicur.io/version: 1.1.3
        name: apicurio-registry-operator
    spec:
      containers:
      - args:
        - --leader-elect
        command:
        - /manager
        env:
        - name: REGISTRY_VERSION
          value: 2.6.4.Final
        - name: REGISTRY_IMAGE_MEM
          value: quay.io/apicurio/apicurio-registry-mem:2.6.4.Final
        - name: REGISTRY_IMAGE_KAFKASQL
          value: quay.io/apicurio/apicurio-registry-kafkasql:2.6.4.Final
        - name: REGISTRY_IMAGE_SQL
          value: quay.io/apicurio/apicurio-registry-sql:2.6.4.Final
        - name: WATCH_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: OPERATOR_NAME
          value: apicurio-registry-operator
        image: quay.io/apicurio/apicurio-registry-operator:1.1.3
        imagePullPolicy: Always
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8081
          initialDelaySeconds: 15
          periodSeconds: 20
        name: apicurio-registry-operator
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 10
        resources:
          limits:
            cpu: 200m
            memory: 100Mi
          requests:
            cpu: 100m
            memory: 50Mi
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          readOnlyRootFilesystem: false
          runAsNonRoot: true
          seccompProfile:
            type: RuntimeDefault
      serviceAccountName: apicurio-registry-operator
      terminationGracePeriodSeconds: 10
