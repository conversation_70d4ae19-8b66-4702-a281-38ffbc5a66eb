ingress:
  enabled: true
  ingressClassName: nginx
  tls:
    enabled: true
    secretName: "kafka-ui-tls"

existingSecret: kafka-ui-credentials

yamlApplicationConfig:
  kafka:
    clusters:
      - name: data-platform-cluster
        bootstrapServers: data-platform-cluster-kafka-bootstrap.kafka.svc:9093
        schemaRegistry: http://schema-registry.kafka.svc.cluster.local:8081
        kafkaconnect:
          - name: bims-connect-cluster
            address: http://data-platform-connect-cluster-connect-api.kafka.svc.cluster.local:8083
        properties:
          security.protocol: SSL
          ssl.keystore.type: PKCS12
          ssl.keystore.password: ${KEYSTORE_PASSWORD}
          ssl.keystore.location: /etc/certificates/keystore/user.p12
          ssl.truststore.type: PKCS12
          ssl.truststore.password: ${TRUSTSTORE_PASSWORD}
          ssl.truststore.location: /etc/certificates/truststore/ca.p12
  management:
    health:
      ldap:
        enabled: false
probes:
  useHttpsScheme: false
env:
  - name: TRUSTSTORE_PASSWORD
    valueFrom:
      secretKeyRef:
        key: ca.password
        name: data-platform-cluster-cluster-ca-cert
  - name: KEYSTORE_PASSWORD
    valueFrom:
      secretKeyRef:
        key: user.password
        name: super-user
serviceAccount:
  create: false
  name: "workload-identity-sa"
volumeMounts:
  - name: truststore
    mountPath: "/etc/certificates/truststore"
    readOnly: true
  - name: keystore
    mountPath: "/etc/certificates/keystore"
    readOnly: true
  - name: secrets-store
    mountPath: "/secrets-store"
    readOnly: true
volumes:
  - name: truststore
    secret:
      secretName: data-platform-cluster-cluster-ca-cert
  - name: keystore
    secret:
      secretName: super-user
  - name: secrets-store
    csi:
      driver: secrets-store.csi.k8s.io
      readOnly: true
      volumeAttributes:
        secretProviderClass: "kafka-secret-provider"
affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
        - matchExpressions:
            - key: agentpool
              operator: In
              values:
                - general
                - generaltemp
