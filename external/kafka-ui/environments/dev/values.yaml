ingress:
  enabled: true
  ingressClassName: nginx
  tls:
    enabled: true
    secretName: "tls-kafka-ui"
  host: kafka-ui.kafka.dev.payex.net
  annotations:
    cert-manager.io/cluster-issuer: "dataplatform-cluster-issuer"
    cert-manager.io/common-name: "kafka-ui.kafka.dev.payex.net"
    cert-manager.io/alt-name: "kafka-ui.kafka.dev.payex.net"
    cert-manager.io/revision-history-limit: "1"
    external-dns.alpha.kubernetes.io/hostname: "kafka-ui.kafka.dev.payex.net"
