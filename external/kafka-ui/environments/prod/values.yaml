ingress:
  enabled: true
  ingressClassName: nginx
  tls:
    enabled: true
    secretName: "tls-kafka-ui"
  host: kafka-ui.kafka.payex.net
  annotations:
    cert-manager.io/cluster-issuer: "dataplatform-cluster-issuer"
    cert-manager.io/common-name: "kafka-ui.kafka.payex.net"
    cert-manager.io/alt-name: "kafka-ui.kafka.payex.net"
    cert-manager.io/revision-history-limit: "1"
    external-dns.alpha.kubernetes.io/hostname: "kafka-ui.kafka.payex.net"
# yamlApplicationConfig:
#   auth:
#     type: OAUTH2
#     oauth2:
#       client:
#         github:
#           provider: github
#           clientId: b89f0811573c2f967a5b
#           clientSecret: 28603bac3ab85512aeb06645e48d50ca1e84052b
#           scope: read:org
#           user-name-attribute: login
#           custom-params:
#             type: github
