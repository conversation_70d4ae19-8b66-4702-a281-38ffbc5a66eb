podSecurityContext:
  fsGroup: 1000
  runAsGroup: 1000
  runAsUser: 1000

securityContext:
  allowPrivilegeEscalation: false
  capabilities:
    drop:
      - ALL
  readOnlyRootFilesystem: true
  runAsNonRoot: true
  seccompProfile:
    type: RuntimeDefault
  runAsUser: 1000

ingress:
  enabled: true
  ingressClassName: nginx
  tls:
    enabled: true
    secretName: "tls-kafka-ui"
  host: kafka-ui.kafka.stage.payex.net
  annotations:
    cert-manager.io/cluster-issuer: "dataplatform-cluster-issuer"
    cert-manager.io/common-name: "kafka-ui.kafka.stage.payex.net"
    cert-manager.io/alt-name: "kafka-ui.kafka.stage.payex.net"
    cert-manager.io/revision-history-limit: "1"
    external-dns.alpha.kubernetes.io/hostname: "kafka-ui.kafka.stage.payex.net"

yamlApplicationConfig:
  kafka:
    clusters:
      - name: data-platform-cluster
        bootstrapServers: data-platform-cluster-kafka-bootstrap.kafka.svc:9093
        schemaRegistry: http://schema-registry.kafka.svc.cluster.local:8081
        kafkaconnect:
          - name: bims-connect-cluster
            address: http://data-platform-connect-cluster-connect-api.kafka.svc.cluster.local:8083
        properties:
          security.protocol: SSL
          ssl.keystore.type: PKCS12
          ssl.keystore.password: ${KEYSTORE_PASSWORD}
          ssl.keystore.location: /etc/certificates/keystore/user.p12
          ssl.truststore.type: PKCS12
          ssl.truststore.password: ${TRUSTSTORE_PASSWORD}
          ssl.truststore.location: /etc/certificates/truststore/ca.p12
      - name: schema-registry-cluster
        bootstrapServers: schema-registry-kafka.kafka.svc:9092
        readOnly: false
        properties:
          security.protocol: PLAINTEXT
  management:
    health:
      ldap:
        enabled: false
