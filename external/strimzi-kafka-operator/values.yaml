affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
        - matchExpressions:
            - key: agentpool
              operator: In
              values:
                - general
                - generaltemp

resources:
  limits:
    memory: 768Mi
  requests:
    memory: 768Mi

logConfiguration: |-
  name = COConfig
  monitorInterval = 30

  appender.console.type = Console
  appender.console.name = STDOUT
  appender.console.layout.type = JsonLayout
  appender.console.layout.compact = true
  appender.console.layout.eventEol = true

  rootLogger.level = ${env:STRIMZI_LOG_LEVEL:-INFO}
  rootLogger.appenderRefs = stdout
  rootLogger.appenderRef.console.ref = STDOUT

  # Kafka AdminClient logging is a bit noisy at INFO level
  logger.kafka.name = org.apache.kafka
  logger.kafka.level = WARN
