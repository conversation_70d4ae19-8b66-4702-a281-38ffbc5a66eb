operatorVolumeMounts:
  create: true
  data:
    - name: flink-artifacts
      mountPath: /opt/flink/artifacts
    - name: operator-tmp
      mountPath: /tmp

operatorVolumes:
  create: true
  data:
    - name: flink-artifacts
      hostPath:
        path: /tmp/flink/artifacts
        type: DirectoryOrCreate
    - name: operator-tmp
      emptyDir: {}

podSecurityContext:
  runAsUser: 9999
  runAsGroup: 9999
  fsGroup: 9999

operatorSecurityContext:
  allowPrivilegeEscalation: false
  capabilities:
    drop:
      - ALL
  readOnlyRootFilesystem: true
  runAsNonRoot: true
  seccompProfile:
    type: RuntimeDefault
  runAsUser: 9999
  runAsGroup: 9999

webhookSecurityContext:
  readOnlyRootFilesystem: true
  runAsNonRoot: true
  runAsUser: 9999
  runAsGroup: 9999
  allowPrivilegeEscalation: false
  capabilities:
    drop:
      - ALL
  seccompProfile:
    type: RuntimeDefault
