fullnameOverride: external-dns

policy: sync

serviceAccount:
  annotations:
    azure.workload.identity/client-id: USER_ASSIGNED_IDENTITY_CLIENT_ID

podLabels:
  azure.workload.identity/use: "true"

provider: azure

azure:
  secretName: external-dns-azure

affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
        - matchExpressions:
            - key: agentpool
              operator: In
              values:
                - general
                - generaltemp
