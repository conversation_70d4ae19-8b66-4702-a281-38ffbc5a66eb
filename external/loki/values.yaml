test:
  enabled: false

lokiCanary:
  enabled: false

loki:
  podLabels:
    azure.workload.identity/use: "true"
  storage:
    type: azure
    bucketNames:
      chunks: loki-chunks
      ruler: loki-ruler
      admin: loki-admin
    azure:
      useFederatedToken: true
  schemaConfig:
    configs:
      - from: "2024-04-01"
        index:
          period: 24h
          prefix: loki_index_
        object_store: azure
        schema: v13
        store: tsdb
  ingester:
    chunk_encoding: snappy
  tracing:
    enabled: true
  querier:
    max_concurrent: 4

  rulerConfig:
    storage:
      type: local
      local:
        directory: /var/loki/rulestorage
    rule_path: /var/loki/rules-temp
    ring:
      kvstore:
        store: inmemory
    alertmanager_url: http://alertmanager-operated.monitoring:9093
    enable_alertmanager_v2: true

deploymentMode: SimpleScalable

serviceAccount:
  labels:
    azure.workload.identity/use: "true"

gateway:
  replicas: 2
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: agentpool
                operator: In
                values:
                  - general
                  - generaltemp
backend:
  replicas: 2
  persistence:
    volumeClaimsEnabled: false

  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: agentpool
                operator: In
                values:
                  - general
                  - generaltemp

read:
  replicas: 2
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: agentpool
                operator: In
                values:
                  - general
                  - generaltemp
write:
  replicas: 2
  persistence:
    volumeClaimsEnabled: false
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: agentpool
                operator: In
                values:
                  - general
                  - generaltemp

chunksCache:
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: agentpool
                operator: In
                values:
                  - general
                  - generaltemp

resultsCache:
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: agentpool
                operator: In
                values:
                  - general
                  - generaltemp

singleBinary:
  replicas: 0
ingester:
  replicas: 0
querier:
  replicas: 0
queryFrontend:
  replicas: 0
queryScheduler:
  replicas: 0
distributor:
  replicas: 0
compactor:
  replicas: 0
indexGateway:
  replicas: 0
bloomCompactor:
  replicas: 0
bloomGateway:
  replicas: 0

sidecar:
  rules:
    enabled: true
    label: loki_rule
    labelValue: "true"
    folder: /var/loki/rulestorage/1 # 1 is name of tenant. defined in additionalDataSources in kube-prom-stack
    searchNamespace: monitoring
    resource: configmap
