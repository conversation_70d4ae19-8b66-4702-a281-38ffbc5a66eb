loki:
  replicas: 2
  isDefault: false
  persistence:
    enabled: true
    accessModes:
      - ReadWriteOnce
    size: 5Gi
    storageClassName: default
  config:
    chunk_store_config:
      max_look_back_period: 744h
    compactor:
      retention_enabled: true
    limits_config:
      retention_period: 744h
    ingester:
      chunk_target_size: 1572864
  # affinity:
  #   nodeAffinity:
  #     requiredDuringSchedulingIgnoredDuringExecution:
  #       nodeSelectorTerms:
  #       - matchExpressions:
  #         - key: agentpool
  #           operator: In
  #           values:
  #           - application

promtail:
  config:
    snippets:
      pipelineStages:
        - docker: {}
        - cri: {}
        - json:
            expressions:
              level: level
              # logger_name: logger_name
              # thread_name: thread_name
        - labels:
            level: ""
            # logger_name: ''
            # thread_name: ''

test_pod.enabled: false
