auth:
  username: user
  password: password

service:
  type: LoadBalancer
  annotations:
    service.beta.kubernetes.io/azure-load-balancer-internal: "true"
    service.beta.kubernetes.io/azure-load-balancer-internal-subnet: "aks_nodepool_subnet"
    service.beta.kubernetes.io/azure-load-balancer-ipv4: ***********

logs: false # custom logging

replicaCount: 1

clustering:
  enabled: false

queue_leader_locator: balanced

extraConfiguration: |
  load_definitions = /app/load_definition.json

  log.default.level = info
  log.file = false
  log.console = true
  log.console.level = debug
  log.console.formatter = json

  cluster_formation.peer_discovery_backend = rabbit_peer_discovery_k8s
  cluster_formation.k8s.host = rabbitmq-headless
