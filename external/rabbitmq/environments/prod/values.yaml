auth:
  existingPasswordSecret: rabbitmq-credentials
  existingSecretPasswordKey: password

service:
  type: LoadBalancer
  annotations:
    service.beta.kubernetes.io/azure-load-balancer-internal: "true"
    # service.beta.kubernetes.io/azure-load-balancer-ipv4: *************
    service.beta.kubernetes.io/azure-load-balancer-internal-subnet: "aks_nodepool_subnet"
    service.beta.kubernetes.io/azure-load-balancer-ipv4: *************

logs: false # custom logging

replicaCount: 1

# clustering:
#   enabled: true

extraConfiguration: |
  load_definitions = /app/load_definition.json

  log.default.level = info
  log.file = false
  log.console = true
  log.console.level = info
  log.console.formatter = json

  # cluster_formation.peer_discovery_backend = rabbit_peer_discovery_k8s
  # cluster_formation.k8s.host = rabbitmq-headless
