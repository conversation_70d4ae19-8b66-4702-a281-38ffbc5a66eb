## Setup rabbitMQ cluster

```shell
kubectl -n automate exec -it rabbitmq-1 -- rabbitmqctl stop_app
kubectl -n automate exec -it rabbitmq-1 -- rabbitmqctl reset
kubectl -n automate exec -it rabbitmq-1 -- rabbitmqctl join_cluster <EMAIL>
kubectl -n automate exec -it rabbitmq-1 -- rabbitmqctl start_app
kubectl -n automate exec -it rabbitmq-0 -- rabbitmqctl cluster_status
kubectl -n automate exec -it rabbitmq-1 -- rabbitmqctl cluster_status
```

Set the following properties in values.yaml
```shell
replicaCount: 2

clustering:
  enabled: true
```

### Forget rabbitMQ cluster

```shell
kubectl -n automate exec -it rabbitmq-0 -- rabbitmqctl forget_cluster_node <EMAIL>
```

Set the following properties in values.yaml
```shell
replicaCount: 1

clustering:
  enabled: false
```