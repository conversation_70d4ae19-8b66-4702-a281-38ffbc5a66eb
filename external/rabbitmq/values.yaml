replicaCount: 1

# logs: false # custom logging

#extraConfiguration: |
#  log.default.level = info
#  log.file = false
#  log.console = true
#  log.console.level = info
#  log.console.formatter = json

resources:
  limits:
    memory: 768Mi
  requests:
    memory: 768Mi

ingress:
  enabled: false
  hostname: INGRESS_HOSTNAME
  ingressClassName: nginx
  tls: true
  selfSigned: true

affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
        - matchExpressions:
            - key: agentpool
              operator: In
              values:
                - general
                - generaltemp

livenessProbe:
  enabled: true
  initialDelaySeconds: 120
  timeoutSeconds: 20
  periodSeconds: 30
  failureThreshold: 6
  successThreshold: 1

readinessProbe:
  enabled: true
  initialDelaySeconds: 10
  timeoutSeconds: 20
  periodSeconds: 30
  failureThreshold: 3
  successThreshold: 1

plugins: "rabbitmq_management rabbitmq_peer_discovery_k8s rabbitmq_federation rabbitmq_federation_management"

loadDefinition:
  enabled: true
  existingSecret: rabbitmq-load-definition

extraConfiguration: |
  load_definitions = /app/load_definition.json

auth:
  existingErlangSecret: rabbitmq-erlang-cookie

metrics:
  enabled: true

  prometheusRule:
    enabled: true
    namespace: "monitoring"
    ## Rules inspired from https://awesome-prometheus-alerts.grep.to/rules.html
    rules:
      - alert: RabbitmqDown
        expr: rabbitmq_up{service="{{ template "common.names.fullname" . }}"} == 0
        for: 5m
        labels:
          severity: error
        annotations:
          summary: Rabbitmq down (instance {{ "{{ $labels.instance }}" }})
          description: RabbitMQ node down
      - alert: ClusterDown
        expr: |
          sum(rabbitmq_running{service="{{ template "common.names.fullname" . }}"})
          < {{ .Values.replicaCount }}
        for: 5m
        labels:
          severity: error
        annotations:
          summary: Cluster down (instance {{ "{{ $labels.instance }}" }})
          description: |
            Less than {{ .Values.replicaCount }} nodes running in RabbitMQ cluster
            VALUE = {{ "{{ $value }}" }}
      - alert: ClusterPartition
        expr: rabbitmq_partitions{service="{{ template "common.names.fullname" . }}"} > 0
        for: 5m
        labels:
          severity: error
        annotations:
          summary: Cluster partition (instance {{ "{{ $labels.instance }}" }})
          description: |
            Cluster partition
            VALUE = {{ "{{ $value }}" }}
      - alert: OutOfMemory
        expr: |
          rabbitmq_node_mem_used{service="{{ template "common.names.fullname" . }}"}
          / rabbitmq_node_mem_limit{service="{{ template "common.names.fullname" . }}"}
          * 100 > 90
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: Out of memory (instance {{ "{{ $labels.instance }}" }})
          description: |
            Memory available for RabbmitMQ is low (< 10%)\n  VALUE = {{ "{{ $value }}" }}
            LABELS: {{ "{{ $labels }}" }}
      - alert: TooManyConnections
        expr: rabbitmq_connectionsTotal{service="{{ template "common.names.fullname" . }}"} > 1000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: Too many connections (instance {{ "{{ $labels.instance }}" }})
          description: |
            RabbitMQ instance has too many connections (> 1000)
            VALUE = {{ "{{ $value }}" }}\n  LABELS: {{ "{{ $labels }}" }}
