grafana:
  ingress:
    hosts:
      - grafana.kafka.stage.payex.net
    tls:
      - secretName: tls-grafana
        hosts:
          - grafana.kafka.stage.payex.net
    annotations:
      cert-manager.io/cluster-issuer: "dataplatform-cluster-issuer"
      cert-manager.io/common-name: "grafana.kafka.stage.payex.net"
      cert-manager.io/alt-name: "grafana.kafka.stage.payex.net"
      cert-manager.io/revision-history-limit: "1"
      external-dns.alpha.kubernetes.io/hostname: "grafana.kafka.stage.payex.net"
  adminPassword: p
  grafana.ini:
    feature_toggles:
      publicDashboards: true
    users:
      default_theme: light
  additionalDataSources:
    - name: Loki
      type: loki
      access: proxy
      url: http://loki-gateway.monitoring
      jsonData:
        httpHeaderName1: "X-Scope-OrgID"
      secureJsonData:
        httpHeaderValue1: "1"
  # deploymentStrategy:
  #   type: Recreate

alertmanager:
  config:
    global:
      resolve_timeout: 1m
      slack_api_url: "*******************************************************************************"
    route:
      group_by: ["alertname"]
      group_wait: 30s
      group_interval: 5m
      repeat_interval: 12h
      receiver: "slack"
      routes:
        - receiver: "null"
          matchers:
            - alertname =~ "InfoInhibitor|Watchdog|NoMessageForTooLong|KubeControllerManagerDown|KubeProxyDown|KubeSchedulerDown"
    receivers:
      - name: "null"
      - name: "slack"
        slack_configs:
          - channel: "#team-data-platform-alerts-stage"
            send_resolved: true
            icon_url: https://avatars3.githubusercontent.com/u/3380462
            title: "{{ range .Alerts }}{{ .Annotations.summary }}\n{{ end }}"
            text: "{{ range .Alerts }}{{ .Annotations.description }}\n{{ end }}"

prometheus:
  prometheusSpec:
    storageSpec:
      volumeClaimTemplate:
        spec:
          resources:
            requests:
              storage: 10Gi

prometheusOperator:
  admissionWebhooks:
    certManager:
      enabled: true
