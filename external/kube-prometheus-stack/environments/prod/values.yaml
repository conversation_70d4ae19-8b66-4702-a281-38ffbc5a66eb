grafana:
  admin:
    existingSecret: grafana-credentials
    userKey: admin-user
    passwordKey: admin-password
  ingress:
    hosts:
      - grafana.kafka.payex.net
    tls:
      - secretName: tls-grafana
        hosts:
          - grafana.kafka.payex.net
    annotations:
      cert-manager.io/cluster-issuer: "dataplatform-cluster-issuer"
      cert-manager.io/common-name: "grafana.kafka.payex.net"
      cert-manager.io/alt-name: "grafana.kafka.payex.net"
      cert-manager.io/revision-history-limit: "1"
      external-dns.alpha.kubernetes.io/hostname: "grafana.kafka.payex.net"

  # grafana.ini:
  #   feature_toggles:
  #     publicDashboards: true
  #   check_for_updates: false
  #   reporting_enabled: false
  #   server:
  #     enable_gzip: true
  #     root_url: "https://%(domain)s"
  #   security:
  #     disable_gravatar: false
  #   dashboards:
  #     min_refresh_interval: 60s
  #   users:
  #     auto_assign_org_role: Admin
  #   auth.github:
  #     enabled: true
  #     allow_sign_up: true
  #     auto_login: false
  #     client_id: c6c568fe901d34a52f58
  #     client_secret: $__env{client-secret}
  #     scopes: user:email,read:org
  #     auth_url: https://github.com/login/oauth/authorize
  #     token_url: https://github.com/login/oauth/access_token
  #     api_url: https://api.github.com/user
  #     allowed_organizations: payex,irori-ab
  #     # team_ids: 7418136
  additionalDataSources:
    - name: Loki
      type: loki
      access: proxy
      url: http://loki-gateway.monitoring
      jsonData:
        httpHeaderName1: "X-Scope-OrgID"
      secureJsonData:
        httpHeaderValue1: "1"
  envFromSecret: "github-app-grafana"
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: agentpool
                operator: In
                values:
                  - general
                  - generaltemp
  persistence:
    size: 10Gi

alertmanager:
  config:
    global:
      resolve_timeout: 1m
      slack_api_url: "*******************************************************************************"
    route:
      group_by: ["alertname"]
      group_wait: 30s
      group_interval: 5m
      repeat_interval: 12h
      receiver: "slack"
      routes:
        - receiver: "null"
          matchers:
            - alertname =~ "InfoInhibitor|Watchdog|NoMessageForTooLong|KubeControllerManagerDown|KubeProxyDown|KubeSchedulerDown"
    receivers:
      - name: "null"
      - name: "slack"
        slack_configs:
          - channel: "#team-data-platform"
            send_resolved: true
            icon_url: https://avatars3.githubusercontent.com/u/3380462
            title: "{{ range .Alerts }}{{ .Annotations.summary }}\n{{ end }}"
            text: "{{ range .Alerts }}{{ .Annotations.description }}\n{{ end }}"
  alertmanagerSpec:
    affinity:
      nodeAffinity:
        requiredDuringSchedulingIgnoredDuringExecution:
          nodeSelectorTerms:
            - matchExpressions:
                - key: agentpool
                  operator: In
                  values:
                    - general
                    - generaltemp
    podAntiAffinity: hard
    podAntiAffinityTopologyKey: topology.kubernetes.io/zone
    storage:
      volumeClaimTemplate:
        spec:
          resources:
            requests:
              storage: 10Gi

prometheus:
  prometheusSpec:
    affinity:
      nodeAffinity:
        requiredDuringSchedulingIgnoredDuringExecution:
          nodeSelectorTerms:
            - matchExpressions:
                - key: agentpool
                  operator: In
                  values:
                    - general
                    - generaltemp
    podAntiAffinity: hard
    podAntiAffinityTopologyKey: topology.kubernetes.io/zone
    storageSpec:
      volumeClaimTemplate:
        spec:
          resources:
            requests:
              storage: 10Gi

prometheusOperator:
  admissionWebhooks:
    certManager:
      enabled: true
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: agentpool
                operator: In
                values:
                  - general
                  - generaltemp

kube-state-metrics:
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: agentpool
                operator: In
                values:
                  - general
                  - generaltemp
