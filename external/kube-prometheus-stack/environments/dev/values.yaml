grafana:
  ingress:
    hosts:
      - grafana.kafka.dev.payex.net
    tls:
      - secretName: tls-grafana
        hosts:
          - grafana.kafka.dev.payex.net
    annotations:
      cert-manager.io/cluster-issuer: "dataplatform-cluster-issuer"
      cert-manager.io/common-name: "grafana.kafka.dev.payex.net"
      cert-manager.io/alt-name: "grafana.kafka.dev.payex.net"
      cert-manager.io/revision-history-limit: "1"
      external-dns.alpha.kubernetes.io/hostname: "grafana.kafka.dev.payex.net"
  adminPassword: p
  grafana.ini:
    feature_toggles:
      publicDashboards: true
    users:
      default_theme: light
  additionalDataSources:
    - name: Loki
      type: loki
      access: proxy
      url: http://loki-gateway.monitoring
      jsonData:
        httpHeaderName1: "X-Scope-OrgID"
      secureJsonData:
        httpHeaderValue1: "1"
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: agentpool
                operator: In
                values:
                  - general
                  - generaltemp

alertmanager:
  config:
    global:
      resolve_timeout: 1m
      slack_api_url: "*******************************************************************************"
    route:
      group_by: ["alertname"]
      group_wait: 30s
      group_interval: 5m
      repeat_interval: 12h
      receiver: "slack"
      routes:
        - receiver: "null"
          matchers:
            - alertname =~ "InfoInhibitor|Watchdog|NoMessageForTooLong|KubeControllerManagerDown|KubeProxyDown|KubeSchedulerDown"
    receivers:
      - name: "null"
      - name: "slack"
        slack_configs:
          - channel: "#team-data-platform-alerts-stage"
            send_resolved: true
            icon_url: https://avatars3.githubusercontent.com/u/3380462
            title: "{{ range .Alerts }}{{ .Annotations.summary }}\n{{ end }}"
            text: "{{ range .Alerts }}{{ .Annotations.description }}\n{{ end }}"
  alertmanagerSpec:
    affinity:
      nodeAffinity:
        requiredDuringSchedulingIgnoredDuringExecution:
          nodeSelectorTerms:
            - matchExpressions:
                - key: agentpool
                  operator: In
                  values:
                    - general
                    - generaltemp
    podAntiAffinity: hard
    podAntiAffinityTopologyKey: topology.kubernetes.io/zone

prometheus:
  prometheusSpec:
    affinity:
      nodeAffinity:
        requiredDuringSchedulingIgnoredDuringExecution:
          nodeSelectorTerms:
            - matchExpressions:
                - key: agentpool
                  operator: In
                  values:
                    - general
                    - generaltemp
    podAntiAffinity: hard
    podAntiAffinityTopologyKey: topology.kubernetes.io/zone
    storageSpec:
      volumeClaimTemplate:
        spec:
          resources:
            requests:
              storage: 10Gi

prometheusOperator:
  admissionWebhooks:
    certManager:
      enabled: true
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: agentpool
                operator: In
                values:
                  - general
                  - generaltemp

kube-state-metrics:
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: agentpool
                operator: In
                values:
                  - general
                  - generaltemp
