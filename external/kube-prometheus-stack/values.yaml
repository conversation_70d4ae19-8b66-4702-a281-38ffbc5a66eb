prometheus:
  prometheusSpec:
    scrapeInterval: "30s" # Default is 15s, increase it to reduce load
    scrapeTimeout: "10s" # Reduce if needed
    resources:
      requests:
        cpu: "500m"
        memory: "2Gi"
      limits:
        cpu: "1"
        memory: "4Gi"
    # make prometheus from kube-prometheus-stack in monitoring namespace aware of rules,
    # podmonitors, servicemonitors deployed in other namespaces without having to specify selectors.
    podMonitorSelectorNilUsesHelmValues: false
    probeSelectorNilUsesHelmValues: false
    ruleSelectorNilUsesHelmValues: false
    serviceMonitorSelectorNilUsesHelmValues: false
    storageSpec:
      volumeClaimTemplate:
        spec:
          storageClassName: default
          accessModes: ["ReadWriteOnce"]
          resources:
            requests:
              storage: 5Gi

prometheus-node-exporter:
  hostRootFsMount:
    enabled: false

prometheusOperator:
  admissionWebhooks:
    failurePolicy: Ignore

grafana:
  ingress:
    enabled: true
    ingressClassName: nginx
    path: "/"
  persistence:
    type: pvc
    enabled: true
    storageClassName: default
    accessModes:
      - ReadWriteOnce
    size: 6Gi
    finalizers:
      - kubernetes.io/pvc-protection
  sidecar:
    skipTlsVerify: true

alertmanager:
  alertmanagerSpec:
    storage:
      volumeClaimTemplate:
        spec:
          storageClassName: default
          accessModes: ["ReadWriteOnce"]
          resources:
            requests:
              storage: 5Gi
