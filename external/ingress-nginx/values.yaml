controller:
  allowSnippetAnnotations: "true"
  service:
    annotations:
      service.beta.kubernetes.io/azure-load-balancer-internal: "true"
      service.beta.kubernetes.io/azure-load-balancer-health-probe-request-path: "/healthz"
      service.beta.kubernetes.io/azure-load-balancer-internal-subnet: "aks_nodepool_subnet"
  config:
    enable-snippets: "true"
    use-forwarded-headers: "true"
    log-format-escape-json: "true"
    log-format-upstream:
      '{"timestamp": "$time_iso8601", "requestID": "$req_id", "proxyUpstreamName":
      "$proxy_upstream_name", "proxyAlternativeUpstreamName": "$proxy_alternative_upstream_name","upstreamStatus":
      "$upstream_status", "upstreamAddr": "$upstream_addr","httpRequest":{"requestMethod":
      "$request_method", "requestUrl": "$host$request_uri", "status": $status,"requestSize":
      "$request_length", "responseSize": "$upstream_response_length", "userAgent": "$http_user_agent",
      "remoteIp": "$remote_addr", "referer": "$http_referer", "latency": "$upstream_response_time s",
      "protocol":"$server_protocol"}}'
    annotations-risk-level: "Critical"

  extraArgs:
    enable-ssl-passthrough: ""
    # # Required for ExternalDNS
    # publish-service: ingress-nginx/ingress-nginx-controller
