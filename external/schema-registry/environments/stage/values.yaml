replicaCount: 2

# kafka:
#   enabled: false

# externalKafka:
#   brokers:
#     - SSL://data-platform-cluster-kafka-bootstrap.kafka.svc:9093
#   listener:
#     protocol: SSL

# auth:
#   kafka:
#     jksSecret: xyzSecret
#     keystorePassword: ""
#     truststorePassword: ""
#     clientAuthentication: NONE

ingress:
  hostname: schema-registry.kafka.stage.payex.net

  annotations:
    external-dns.alpha.kubernetes.io/hostname: schema-registry.kafka.stage.payex.net
    cert-manager.io/cluster-issuer: dataplatform-cluster-issuer
    cert-manager.io/common-name: schema-registry.kafka.stage.payex.net
    cert-manager.io/alt-name: schema-registry.kafka.stage.payex.net
    cert-manager.io/revision-history-limit: "1"
