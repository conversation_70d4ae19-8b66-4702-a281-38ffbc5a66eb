replicaCount: 2

ingress:
  hostname: schema-registry.kafka.dev.payex.net

  annotations:
    external-dns.alpha.kubernetes.io/hostname: schema-registry.kafka.dev.payex.net
    cert-manager.io/cluster-issuer: dataplatform-cluster-issuer
    cert-manager.io/common-name: schema-registry.kafka.dev.payex.net
    cert-manager.io/alt-name: schema-registry.kafka.dev.payex.net
    cert-manager.io/revision-history-limit: "1"
