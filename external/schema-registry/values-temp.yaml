# values.yaml för Bitnami Schema Registry
# Försök att hantera både <PERSON>-validering och startskriptets JKS-kontroll.

# Toppnivåvärde för JKS-lösenordet. Detta MÅSTE matcha värdet i Kubernetes Secret 'sr-jks-password'.
jksPasswordValue: "DittSuperStarkaLösenord123!" # <-- ANPASSA DETTA!

# 1. Använd inte den medföljande Kafka-instansen
kafka:
  enabled: false

# 2. Konfigurera anslutning till ditt befintliga externa Kafka-kluster
externalKafka:
  brokers:
    # Inkludera SSL:// prefixet här!
    - "SSL://data-platform-cluster-kafka-bootstrap.kafka.svc.cluster.local:9093"
  listener:
    protocol: SSL # Indikerar att SSL ska användas, triggar troligen JKS-krav i Helm NOTES.txt

# 3. <PERSON>ör att passera Helm-chartens NOTES.txt-validering. Ett tomt K8s Secret med detta namn måste finnas.
auth:
  kafka:
    jksSecret: "sr-dummy-jks-for-validation"

# 4. Volymer som behövs i podden
extraVolumes:
  # Volym för PEM-certifikaten från din KafkaUser 'schema-registry-user' Secret
  - name: sr-user-pem-credentials
    secret:
      secretName: schema-registry-user

  # Delad emptyDir-volym för JKS-filerna som genereras av init containern
  - name: jks-files-volume
    emptyDir: {}

# 5. Volymmonteringar (appliceras på podden, tillgängliga för init och huvudcontainer)
extraVolumeMounts:
  # För init containern att läsa PEM-filer från 'schema-registry-user' Secret
  - name: sr-user-pem-credentials
    mountPath: "/mnt/pem_certs" # Sökväg där PEM-filerna monteras
    readOnly: true
  # För både init containern (skriva) och huvudcontainern (läsa) JKS-filer
  - name: jks-files-volume
    mountPath: "/mnt/jks_files" # Sökväg till den delade JKS-volymen

# 6. Init Container för att generera JKS-filer
initContainers:
  - name: jks-generator
    image: "alpine:3.19"
    env:
      - name: JKS_PASSWORD
        valueFrom:
          secretKeyRef:
            name: "sr-jks-password" # Namnet på Kubernetes Secret du skapade för lösenordet
            key: "JKS_PASSWORD" # Nyckeln i det Secretet
      - name: PEM_CERTS_MOUNT_PATH
        value: "/mnt/pem_certs"
      - name: JKS_FILES_MOUNT_PATH
        value: "/mnt/jks_files"
    volumeMounts:
      - name: sr-user-pem-credentials
        mountPath: "/mnt/pem_certs"
        readOnly: true
      - name: jks-files-volume
        mountPath: "/mnt/jks_files"
    command:
      - /bin/sh
      - -cex
      - |
        echo "Schema Registry JKS Generator: Startar..."
        if ! command -v keytool > /dev/null || ! command -v openssl > /dev/null; then
          echo "Installerar openjdk11-jre-headless och openssl..."
          apk add --no-cache openjdk11-jre-headless openssl
        else
          echo "Verktyg (keytool, openssl) finns redan."
        fi

        TRUSTSTORE_FILE="${JKS_FILES_MOUNT_PATH}/truststore.jks"
        KEYSTORE_FILE="${JKS_FILES_MOUNT_PATH}/keystore.jks"

        CA_CERT_PATH="${PEM_CERTS_MOUNT_PATH}/ca.crt"
        STRIMZI_P12_FILE_PATH="${PEM_CERTS_MOUNT_PATH}/user.p12"
        STRIMZI_P12_PASSWORD_FILE_PATH="${PEM_CERTS_MOUNT_PATH}/user.password"

        echo "Säkerställer att JKS output-katalogen (${JKS_FILES_MOUNT_PATH}) finns..."
        mkdir -p "${JKS_FILES_MOUNT_PATH}"

        echo "Tar bort eventuella befintliga JKS-filer..."
        rm -f "${TRUSTSTORE_FILE}" "${KEYSTORE_FILE}"

        echo "Verifierar att nödvändiga filer från Strimzi Secret finns på ${PEM_CERTS_MOUNT_PATH}..."
        ls -l "${PEM_CERTS_MOUNT_PATH}"
        if [ ! -f "$CA_CERT_PATH" ] || [ ! -f "$STRIMZI_P12_FILE_PATH" ] || [ ! -f "$STRIMZI_P12_PASSWORD_FILE_PATH" ]; then
          echo "FEL: En eller flera nödvändiga filer (ca.crt, user.p12, user.password) saknas i ${PEM_CERTS_MOUNT_PATH}!"
          exit 1
        fi

        echo "Läser lösenord för Strimzi P12-filen från ${STRIMZI_P12_PASSWORD_FILE_PATH}..."
        STRIMZI_P12_PASSWORD=$(tr -d '\n\r' < "${STRIMZI_P12_PASSWORD_FILE_PATH}")

        if [ -z "$STRIMZI_P12_PASSWORD" ]; then
          echo "FEL: Lösenordsfilen för Strimzi P12 (${STRIMZI_P12_PASSWORD_FILE_PATH}) är tom eller kunde inte läsas korrekt!"
          exit 1
        fi
        echo "Lösenord för Strimzi P12 har lästs."

        echo "Skapar TrustStore JKS ($TRUSTSTORE_FILE) från $CA_CERT_PATH..."
        keytool -importcert -alias CARoot -file "$CA_CERT_PATH" \
                -keystore "$TRUSTSTORE_FILE" -storetype JKS \
                -storepass "$JKS_PASSWORD" -noprompt

        echo "Importerar Strimzi PKCS12-fil ($STRIMZI_P12_FILE_PATH) till KeyStore JKS ($KEYSTORE_FILE)..."
        keytool -importkeystore \
                -srckeystore "$STRIMZI_P12_FILE_PATH" -srcstoretype PKCS12 \
                -destkeystore "$KEYSTORE_FILE" -deststoretype JKS \
                -srcstorepass "$STRIMZI_P12_PASSWORD" \
                -deststorepass "$JKS_PASSWORD" \
                -noprompt

        echo "JKS-filer genererade i ${JKS_FILES_MOUNT_PATH}:"
        ls -l "${JKS_FILES_MOUNT_PATH}"
        echo "Schema Registry JKS Generator: Klar."

# 7. Miljövariabler för huvudcontainern (Schema Registry)
#    Dessa är VIKTIGA för att startskriptet ska hitta JKS-filerna på rätt ställe.
extraEnvVars:
  - name: SCHEMA_REGISTRY_KAFKASTORE_BOOTSTRAP_SERVERS
    value: "SSL://data-platform-cluster-kafka-bootstrap.kafka.svc.cluster.local:9093"
  - name: SCHEMA_REGISTRY_KAFKASTORE_SECURITY_PROTOCOL
    value: "SSL"
  - name: SCHEMA_REGISTRY_KAFKASTORE_TOPIC
    value: "_schemas"
  # Peka startskriptet (och Java-appen om den prioriterar env vars) till JKS-filerna
  - name: SCHEMA_REGISTRY_KAFKASTORE_SSL_TRUSTSTORE_LOCATION
    value: "/mnt/jks_files/truststore.jks"
  - name: SCHEMA_REGISTRY_KAFKASTORE_SSL_KEYSTORE_LOCATION
    value: "/mnt/jks_files/keystore.jks"
  # Sätt JKS-lösenorden
  - name: SCHEMA_REGISTRY_KAFKASTORE_SSL_TRUSTSTORE_PASSWORD
    valueFrom:
      secretKeyRef:
        name: "sr-jks-password" # Samma K8s Secret som init container använder
        key: "JKS_PASSWORD"
  - name: SCHEMA_REGISTRY_KAFKASTORE_SSL_KEYSTORE_PASSWORD
    valueFrom:
      secretKeyRef:
        name: "sr-jks-password"
        key: "JKS_PASSWORD"
  - name: SCHEMA_REGISTRY_KAFKASTORE_SSL_KEY_PASSWORD
    valueFrom:
      secretKeyRef:
        name: "sr-jks-password"
        key: "JKS_PASSWORD"

# 8. Konfiguration för Schema Registry Java-applikationen (schema-registry.properties)
#    Detta block säkerställer att Java-applikationen är korrekt konfigurerad.
configuration: |-
  listeners=http://0.0.0.0:8081
  # Följande kan vara redundanta om Java-appen också prioriterar miljövariablerna ovan,
  # men det skadar inte att vara explicit.
  kafkastore.bootstrap.servers=SSL://data-platform-cluster-kafka-bootstrap.kafka.svc.cluster.local:9093
  kafkastore.security.protocol=SSL
  kafkastore.topic=_schemas

  # JKS-inställningar
  kafkastore.ssl.truststore.location=/mnt/jks_files/truststore.jks
  kafkastore.ssl.truststore.password={{ .Values.jksPasswordValue }} # Använder ditt toppnivå-värde
  kafkastore.ssl.keystore.location=/mnt/jks_files/keystore.jks
  kafkastore.ssl.keystore.password={{ .Values.jksPasswordValue }}
  kafkastore.ssl.key.password={{ .Values.jksPasswordValue }}

# 9. Standardinställningar och minimala funktioner
replicaCount: 1
image:
  registry: docker.io
  repository: bitnami/schema-registry
  tag: 7.8.0-debian-12-r2 # Kontrollera att detta är den version du vill ha

ingress:
  enabled: false
autoscaling:
  enabled: false
pdb:
  create: false
