kafkaConnect:
  name: data-platform-connect-cluster
  version: 3.8.1
  replicas: 3
  bootstrapServers: data-platform-cluster-kafka-bootstrap.kafka.svc:9093
  clusterName: data-platform-cluster-kafka-cluster
  trustedCertificateSecret: data-platform-cluster-cluster-ca-cert
  authentication:
    secretName: data-platform-connect-cluster-user
  image:
    url: ghcr.io/payex/helm-charts-data-platform/data-platform-connect-cluster:latest
    pushSecretName: kafka-connect-image-push-secret
    pullSecretName: kafka-connect-image-pull-secret
  resources:
    requests:
      memory: "6Gi"
    limits:
      memory: "8Gi"
  jvmOptions:
    xmx: "4068m"
    xms: "4068m"
  letsEncryptCaCert:
    enabled: false
  nodeAffinity:
    value: "general"
kafkaConnector:
  bims:
    enabled: true
    version: 1.0.9
    repository: https://pkgs.dev.azure.com/SwedbankPayDataplatform/Dataplatform/_packaging/test/maven/v1
    sftp:
      hostname: ssh3.payex.com
      port: 22
      relativePath: incoming
      mdd:
        include: RAW(^ZJXBUT16_.*$)
      add:
        include: RAW(^ZJXBUT12_.*$)
      pdd:
        include: RAW(^ZJXBUT18_.*$)
      bundles:
        include: RAW(^ZJXTRAT5.*$)
      exceptions:
        include: RAW(^ZJXTRAT2.*$)
      elsa:
        include: RAW(^ZJXTRAT3.*$)
    claimcheck:
      checkinUncompressedBatchSizeOverBytes: 1 # default: 1
      azure:
        storageAccountEndpoint: https://storageaccount.blob.core.windows.net
  base24:
    enabled: true
    version: 1.3.0-********.065956
    repository: https://pkgs.dev.azure.com/SwedbankPayDataplatform/Dataplatform/_packaging/test/maven/v1
    sftp:
      hostname: ssh3.payex.com
      port: 22
      relativePath: base24
      include: RAW(^E[0-9]{8}$)
    claimcheck:
      checkinUncompressedBatchSizeOverBytes: 1 # default: 1
      azure:
        storageAccountEndpoint: https://storageaccount.blob.core.windows.net

  schemaRegistryUrl:
  schemaRegistrySslType:
  schemaRegistrySslLocation:

resourceNames:
  - base24-sftp-credentials
  - bims-sftp-credentials
  - base24-authorizations-raw-producer-sas
  - bims-mdd-raw-producer-sas
  - bims-add-raw-producer-sas
  - bims-pdd-raw-producer-sas
  - bims-bundles-raw-producer-sas
  - bims-elsa-raw-producer-sas
  - bims-exceptions-raw-producer-sas
  - azure-storage-datalake-sink-sas
