environment: stage

kafkaConnector:
  bims:
    sftp:
      mdd:
        include: RAW(^ZJXBUT16_.*$)
      add:
        include: RAW(^ZJXBUT12_.*$)
      pdd:
        include: RAW(^ZJXBUT18_.*$)
      bundles:
        include: RAW(^ZJXTRAT5_.*$)
      exceptions:
        include: RAW(^ZJXTRAT2_.*$)
      elsa:
        include: RAW(^ZJXTRAT3_.*$)
    claimcheck:
      azure:
        storageAccountEndpoint: https://stageclaimchecksa.blob.core.windows.net
  base24:
    sftp:
      include: RAW(^P[0-9]{7,8}$)
    claimcheck:
      azure:
        storageAccountEndpoint: https://stageclaimchecksa.blob.core.windows.net
  azureDataLake:
    enabled: false
    accountName: stageasdlsa
    tenantId: d357e4b0-a5d5-400f-b185-86fb574bef72
    fileSystemName: default
    clientId: 796ea082-b25d-47de-9bfd-09412f04de01

  schemaRegistryUrl: https://schema-registry.kafka.stage.payex.net
  schemaRegistrySslType: PEM
  schemaRegistrySslLocation: /opt/kafka/external-configuration/lets-encrypt-cert/ca.crt

kafkaConnect:
  letsEncryptCaCert:
    enabled: true
