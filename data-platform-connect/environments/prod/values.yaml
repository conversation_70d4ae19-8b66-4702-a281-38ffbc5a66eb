environment: prod

kafkaConnector:
  bims:
    sftp:
      relativePath: out
      mdd:
        include: RAW(^BUT85016_.*$)
      add:
        include: RAW(^BUT85012_.*$)
      pdd:
        include: RAW(^BUT85018_.*$)
      bundles:
        include: RAW(^TRA850T5_.*$)
      exceptions:
        include: RAW(^TRA850T2_.*$)
      elsa:
        include: RAW(^TRA850T3_.*$)
    claimcheck:
      azure:
        storageAccountEndpoint: https://prodclaimchecksa.blob.core.windows.net
  base24:
    claimcheck:
      azure:
        storageAccountEndpoint: https://prodclaimchecksa.blob.core.windows.net
