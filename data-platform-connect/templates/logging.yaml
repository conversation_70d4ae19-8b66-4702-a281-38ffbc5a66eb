apiVersion: v1
kind: ConfigMap
metadata:
  name: kafka-connect-logging
  namespace: kafka
data:
  log4j.properties: |-
    log4j.appender.CONSOLE=org.apache.log4j.ConsoleAppender
    log4j.appender.CONSOLE.layout=net.logstash.log4j.JSONEventLayoutV1
    kafka.root.logger.level=INFO
    log4j.rootLogger=${kafka.root.logger.level}, CONSOLE
    # kafka stuff
    # log4j.logger.org.apache.kafka.clients.Metadata=WARN
    # log4j.logger.org.apache.kafka.clients.NetworkClient=WARN
    # log4j.logger.org.apache.kafka.clients.consumer.internals.SubscriptionState=WARN
    # log4j.logger.org.apache.kafka.common.config.AbstractConfig=WARN
    # log4j.logger.org.apache.kafka.common.metrics.Metrics=WARN
    # kafka connect stuff
    log4j.logger.org.apache.kafka.connect.runtime.rest.RestServer=WARN
    # log4j.logger.org.apache.kafka.connect.runtime.distributed.DistributedHerder=WARN
    # log4j.logger.org.apache.kafka.connect.runtime.Loggers=WARN
    # camel kafka connect stuff
    # log4j.logger.org.apache.camel.kafkaconnector=WARN
    log4j.logger.org.apache.camel.component.file.remote.SftpOperations=ERROR
    # other
    # log4j.logger.se.irori.kafka.claimcheck=TRACE
    log4j.logger.com.payex.kafka.connect.source.ClaimCheckProcessor=INFO
    log4j.logger.org.eclipse.jetty.server=WARN
    # log4j.logger.org.apache.kafka.connect.runtime.ConnectorConfig$EnrichedConnectorConfig
    log4j.logger.com.payex.kafka.connect.transform.StructToGenericRecordTransform=DEBUG
    log4j.logger.com.payex.kafka.connect.CamelAzurestoragedatalakesinkSinkConnector=DEBUG
