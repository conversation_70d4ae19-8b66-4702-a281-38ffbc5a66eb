{{ if .Values.kafkaConnector.bims.enabled }}
kind: KafkaConnector
apiVersion: kafka.strimzi.io/v1beta2
metadata:
  name: bims-mdd-connector-claimcheck
  namespace: kafka
  labels:
    strimzi.io/cluster: {{ .Values.kafkaConnect.name }}
spec:
  tasksMax: 1
  class: com.payex.kafka.connect.SftpSourceConnector
  autoRestart:
    enabled: true
  config:
    topics: bims-mdd-raw

    key.converter: org.apache.kafka.connect.storage.StringConverter
    value.converter: com.payex.kafka.connect.source.ClaimCheckConnectConverter

    transforms: ClaimCheckTransformation
    transforms.ClaimCheckTransformation.type: com.payex.kafka.connect.source.ClaimCheckTransformation

    # these properties are injected into Camel's SFTP configuration classes
    camel.kamelet.sftp-source.connectionHost: {{ .Values.kafkaConnector.bims.sftp.hostname }}
    camel.kamelet.sftp-source.connectionPort: {{ .Values.kafkaConnector.bims.sftp.port }}
    camel.kamelet.sftp-source.username: ${secrets:kafka/bims-sftp-credentials:username}
    camel.kamelet.sftp-source.password: ${secrets:kafka/bims-sftp-credentials:password}
    camel.kamelet.sftp-source.directoryName: {{ .Values.kafkaConnector.bims.sftp.relativePath }}
    camel.kamelet.sftp-source.useUserKnownHostsFile: false
    camel.kamelet.sftp-source.include: {{ .Values.kafkaConnector.bims.sftp.mdd.include }}
    #camel.kamelet.sftp-source.delete: true
    # camel.kamelet.sftp-source.move: /processed/.done
    camel.kamelet.sftp-source.moveFailed: /processed/.error
    camel.kamelet.sftp-source.streamDownload: true
    camel.kamelet.sftp-source.stepwise: false
    camel.kamelet.sftp-source.bridgeErrorHandler: true
    camel.kamelet.sftp-source.throwExceptionOnConnectFailed: true
    camel.kamelet.sftp-source.autoCreate: false
    camel.kamelet.sftp-source.disconnect: true
    camel.kamelet.sftp-source.readLock: changed
    camel.kamelet.sftp-source.readLockMinLength: 0

      # these properties are injected into ClaimCheckProcessor which is instantiated in the Kamelet route yaml.
    camel.kamelet.sftp-source.claimCheckValueSerializer: se.irori.kafka.claimcheck.ClaimCheckSerializer # default
    camel.kamelet.sftp-source.claimCheckBackendClass: se.irori.kafka.claimcheck.azure.AzureBlobStorageClaimCheckBackend # default
    camel.kamelet.sftp-source.claimCheckAzBlobStorageAccountSastokenFrom: value:${secrets:kafka/bims-mdd-raw-producer-sas:token}
    # camel.kamelet.sftp-source.claimCheckAzBlobStorageAccountIdentityFrom: managed_id_env:AZURE_CLIENT_ID # not supported yet
    camel.kamelet.sftp-source.claimCheckAzBlobStorageAccountEndpoint: {{ .Values.kafkaConnector.bims.claimcheck.azure.storageAccountEndpoint }}
    camel.kamelet.sftp-source.claimCheckKeySerializer: org.apache.kafka.common.serialization.StringSerializer # default
    camel.kamelet.sftp-source.claimCheckCheckinUncompressedBatchSizeOverBytes: {{ .Values.kafkaConnector.bims.claimcheck.checkinUncompressedBatchSizeOverBytes }}
    camel.kamelet.sftp-source.claimCheckValueSerializerWrappedSerializer: se.irori.kafka.claimcheck.InputStreamSerializer # default



{{ end }}