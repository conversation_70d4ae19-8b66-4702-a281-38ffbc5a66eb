apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaConnect
metadata:
  name: {{ .Values.kafkaConnect.name }}
  namespace: kafka
  annotations:
    strimzi.io/use-connector-resources: "true"
spec:
  logging: 
    type: external
    valueFrom:
      configMapKeyRef:
        name: kafka-connect-logging
        key: log4j.properties
  version: {{ .Values.kafkaConnect.version }}
  replicas: {{ .Values.kafkaConnect.replicas }}
  bootstrapServers: {{ .Values.kafkaConnect.bootstrapServers }}
  tls:
    trustedCertificates:
      - secretName: {{ .Values.kafkaConnect.trustedCertificateSecret }}
        certificate: ca.crt
  authentication: 
    type: tls
    certificateAndKey:
      certificate: user.crt
      key: user.key
      secretName: {{ .Values.kafkaConnect.authentication.secretName }}
  config:
    group.id: {{ .Values.kafkaConnect.name }}
    offset.storage.topic: {{ .Values.kafkaConnect.name }}.offsets
    config.storage.topic: {{ .Values.kafkaConnect.name }}.configs
    status.storage.topic: {{ .Values.kafkaConnect.name }}.status
    config.storage.replication.factor: -1
    offset.storage.replication.factor: -1
    status.storage.replication.factor: -1
    key.converter: org.apache.kafka.connect.storage.StringConverter
    value.converter: org.apache.kafka.connect.json.JsonConverter
    key.converter.schemas.enable: false
    value.converter.schemas.enable: false
    config.providers: secrets,configmaps
    config.providers.secrets.class: io.strimzi.kafka.KubernetesSecretConfigProvider
    config.providers.configmaps.class: io.strimzi.kafka.KubernetesConfigMapConfigProvider
  rack:
    topologyKey: kubernetes.io/hostname
  metricsConfig:
    type: jmxPrometheusExporter
    valueFrom:
      configMapKeyRef:
        name: connect-metrics
        key: metrics-config.yml
  build:
    output:
      type: docker
      image: {{ .Values.kafkaConnect.image.url }}
      pushSecret: {{ .Values.kafkaConnect.image.pushSecretName }}
    plugins:
      - name: camel-azure-storage-datalake-sink-kafka-connector
        artifacts:
          - type: maven
            repository: {{ .Values.kafkaConnector.base24.repository }}
            group: com.payex
            artifact: camel-azure-storage-datalake-sink-kafka-connector
            version: 4.8.0-1.0-20241120.070406
      - name: camel-claimcheck-sftp-source-kafka-connector
        artifacts:
          - type: maven
            repository: {{ .Values.kafkaConnector.base24.repository }}
            group: com.payex
            artifact: camel-claimcheck-sftp-source-kafka-connector
            version: {{ .Values.kafkaConnector.base24.version }}
  resources:
    requests:
      memory: {{ .Values.kafkaConnect.resources.requests.memory }}
    limits:
      memory: {{ .Values.kafkaConnect.resources.limits.memory }}
  jvmOptions:
    "-Xmx": {{ .Values.kafkaConnect.jvmOptions.xmx | quote }}
    "-Xms": {{ .Values.kafkaConnect.jvmOptions.xms | quote }}
  template:
    pod:
      imagePullSecrets:
        - name: {{ .Values.kafkaConnect.image.pullSecretName }}
  {{ if .Values.kafkaConnect.letsEncryptCaCert.enabled }}
      volumes:
        - name: lets-encrypt-cert
          secret:
            secretName: lets-encrypt-cert
  {{ end }}
