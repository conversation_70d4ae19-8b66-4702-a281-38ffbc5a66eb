# Kafka Connect

## How to create a sample parquet file

```
brew install duckdb

duckdb -c "CREATE TABLE sample AS SELECT 'Alice' AS name, 24 AS age, 'New York' AS city UNION ALL SELECT 'Bob', 27, 'Los Angeles' UNION ALL SELECT 'Charlie', 22, 'Chicago' UNION ALL SELECT 'David', 32, '<PERSON>'; COPY sample TO 'sample.parquet' (FORMAT 'parquet');"

duckdb -c "SELECT * FROM 'sample.parquet';"
┌─────────┬───────┬─────────────┐
│  name   │  age  │    city     │
│ varchar │ int32 │   varchar   │
├─────────┼───────┼─────────────┤
│ Alice   │    24 │ New York    │
│ Bob     │    27 │ Los Angeles │
│ Charlie │    22 │ Chicago     │
│ David   │    32 │ Houston     │
└─────────┴───────┴─────────────┘

```

