![GitHub](https://img.shields.io/badge/GitHub-181717.svg?style=for-the-badge&logo=GitHub&logoColor=white)
![GitHub-Actions](https://img.shields.io/badge/GitHub%20Actions-2088FF.svg?style=for-the-badge&logo=GitHub-Actions&logoColor=white)
![helm](https://img.shields.io/badge/Helm-0F1689.svg?style=for-the-badge&logo=Helm&logoColor=white)
![kubernetes](https://img.shields.io/badge/Kubernetes-326CE5.svg?style=for-the-badge&logo=Kubernetes&logoColor=white)
![kafka](https://img.shields.io/badge/Apache%20Kafka-231F20.svg?style=for-the-badge&logo=Apache-Kafka&logoColor=white)
![bash](https://img.shields.io/badge/GNU%20Bash-4EAA25.svg?style=for-the-badge&logo=GNU-Bash&logoColor=white)
![prometheus](https://img.shields.io/badge/Prometheus-E6522C.svg?style=for-the-badge&logo=Prometheus&logoColor=white)
![grafana](https://img.shields.io/badge/Grafana-F46800.svg?style=for-the-badge&logo=Grafana&logoColor=white)

# Data Platform - Helm Charts for Strimzi Kafka


## Table of contents

1. [Install Strimzi Operator](#install-strimzi-operator)
1. [Install Prometheus Operator](#install-prometheus-operator)
1. [Install Kafka Cluster](#install-kafka-cluster)
1. [Create Kafka Topics and Users](#create-entities)
1. [Monitoring](#monitoring)
1. [Verify Setup](#verify-setup)
1. [Setup Local Development Environment](#local-dev)
 
## Helm Charts

The Helm Charts are packaged and pushed to a GitHub Registry under ghcr.io/payex/helm-charts.

### Trigger a build

You trigger a build by creating and pushing a Git tag on the following format to trigger build. 

Format: `[semantic version number]-[name of helm chart to build]`

Example:

```
git tag -a 1.0.0-data-platform-entities -m "Added new topic 'my-topic'"
git push --follow-tags

```


## Monitoring

### Update storage of Prometheus


* Disable auto-synk i app-of-apps-parent
* Disable auto-synk i kube-prometheus-stack
* Pause Prometheus operator and update storage size

```
kubectl patch prometheus/kube-prometheus-stack-prometheus \
--namespace monitoring \
--patch '{"spec": {"paused": true, "storage": {"volumeClaimTemplate": {"spec": {"resources": {"requests": {"storage":"20Gi"}}}}}}}' \
--type merge
```

* Update Prometheus PVC storage

```
for p in $(kubectl get -n monitoring pvc -l operator.prometheus.io/name=kube-prometheus-stack-prometheus -o jsonpath='{range .items[*]}{.metadata.name} {end}'); do \
  kubectl patch -n monitoring pvc/${p} --patch '{"spec": {"resources": {"requests": {"storage":"20Gi"}}}}'; \
done
```

* Delete statefulset

```
kubectl delete -n monitoring statefulset -l operator.prometheus.io/name=kube-prometheus-stack-prometheus --cascade=orphan
```

* Delete pod

```
kubectl get pods -n monitoring -o json | jq -r '.items[] | select(.spec.volumes[]?.persistentVolumeClaim.claimName == "prometheus-kube-prometheus-stack-prometheus-db-prometheus-kube-prometheus-stack-prometheus-0") | .metadata.name'
```

* Wait for external resizer to resize volume (might take a while)

Check events for action:

```
kubectl get events -n monitoring prometheus-kube-prometheus-stack-prometheus-db-prometheus-kube-prometheus-stack-prometheus
```

* Resume Prometheus operator

```
kubectl patch -n monitoring prometheus/kube-prometheus-stack-prometheus --patch '{"spec": {"paused": false" }}' --type merge
```

* Enable auto-synk i kube-prometheus-stack
* Enable auto-synk i app-of-apps-parent



