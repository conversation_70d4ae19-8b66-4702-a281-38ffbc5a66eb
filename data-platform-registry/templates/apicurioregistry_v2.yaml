apiVersion: registry.apicur.io/v1
kind: ApicurioRegistry
metadata:
  name: apicurio-registry
spec:
  configuration:
  env:
    - name: AUTH_ENABLED
      value: "true"
    - name: QUARKUS_OIDC_TENANT_ENABLED
      value: "true"
    - name: QUAR<PERSON><PERSON>_OIDC_AUTH_SERVER_URL
      value: "https://<din-curity-domän>/.../oauth/v2/oauth-anonymous"
    - name: QUARKUS_OIDC_CLIENT_ID
      value: "apicurio-registry"
    - name: QUARKUS_OIDC_CREDENTIALS_SECRET
      value: "<er-client-secret-från-curity>"
    - name: CLIENT_CREDENTIALS_BASIC_AUTH_ENABLED
      value: "true"
    - name: ROLE_BASED_AUTHZ_ENABLED
      value: "true"
    - name: ROLE_BASED_AUTHZ_SOURCE
      value: "TOKEN"
    # Eventuellt variabler för att mappa roller om de inte heter sr-developer etc. i ert Curity
    # - name: REGISTRY_AUTH_ROLES_DEVELOPER
    #   value: "rollen-från-curity-för-developer"
    persistence: kafkasql
    logLevel: INFO
    registryLogLevel: INFO
    kafkasql:
      bootstrapServers: {{ .Values.bootstrapServers }}
      security:
        tls:
          truststoreSecretName: data-platform-cluster-cluster-ca-cert
          keystoreSecretName: apicurio-registry-user
    ui:
      readOnly: {{ .Values.ui.readOnly }}

  deployment:
    replicas: {{ .Values.replicas }}
    managedResources:
      disableIngress: true
      disableNetworkPolicy: true
      disablePodDisruptionBudget: false

    podTemplateSpecPreview:
      spec:
        containers:
          - name: registry
            volumeMounts:
              - name: apicurio-credentials-volume
                mountPath: "/etc/apicurio-registry-credentials"
                readOnly: true
        volumes:
          - name: apicurio-credentials-volume
            secret:
              secretName: apicurio-credentials
