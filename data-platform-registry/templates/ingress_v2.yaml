apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: apicurio-registry-ingress
  annotations:
    external-dns.alpha.kubernetes.io/hostname: {{ .Values.hostname }}
    cert-manager.io/cluster-issuer: dataplatform-cluster-issuer
    cert-manager.io/common-name: {{ .Values.hostname }}
    cert-manager.io/alt-name: {{ .Values.hostname }}
    cert-manager.io/revision-history-limit: "1"
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - {{ .Values.hostname }}
      secretName: apicurio-registry-tls
  rules:
    - host: {{ .Values.hostname }}
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: apicurio-registry-service
                port:
                  number: 8080