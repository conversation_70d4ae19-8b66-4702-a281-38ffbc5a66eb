apiVersion: registry.apicur.io/v1
kind: ApicurioRegistry3
metadata:
  name: apicurio-registry
spec:
  app:
    # auth:
      # enabled: false
    env: 
      - name: REGISTRY_LOG_LEVEL
        value: DEBUG
    # features: 
    ingress: 
      enabled: false
    replicas: {{ .Values.replicas }}
    storage: 
      type: kafkasql
      kafkasql: 
        auth:
          enabled: false
        bootstrapServers: {{ .Values.bootstrapServers }}
        tls:
          truststoreSecretRef: 
            name: data-platform-cluster-cluster-ca-cert
            key: ca.p12
          truststorePasswordSecretRef:
            name: data-platform-cluster-cluster-ca-cert
            key: ca.password
          keystoreSecretRef:
            name: apicurio-registry-user
            key: user.p12
          keystorePasswordSecretRef:
            name: apicurio-registry-user
            key: user.password
  ui:
    enabled: true
    env:
      - name: REGISTRY_API_URL
        value: https://apicurio-registry.kafka.stage.payex.net/apis/registry/v3
      - name: REGISTRY_FEATURE_READ_ONLY
        value: true
    ingress:
      enabled: false
    replicas: 1
