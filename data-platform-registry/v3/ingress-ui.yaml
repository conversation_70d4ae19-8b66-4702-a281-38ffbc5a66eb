apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: apicurio-registry-ingress-ui
  annotations:
    cert-manager.io/cluster-issuer: "cloud-data-clusterissuer"
    cert-manager.io/common-name: "apicurio-registry-ui.kafka.stage.payex.net"
    cert-manager.io/alt-name: "apicurio-registry-ui.kafka.stage.payex.net"
    cert-manager.io/revision-history-limit: "1"
    external-dns.alpha.kubernetes.io/hostname: "apicurio-registry-ui.kafka.stage.payex.net"
spec:
  ingressClassName: nginx
  rules:
  - host: apicurio-registry-ui.kafka.stage.payex.net
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: apicurio-registry-ui-service
            port: 
              number: 8080
  tls:
  - hosts:
    - apicurio-registry-ui.kafka.stage.payex.net
    secretName: apicurio-registry-ui-tls
