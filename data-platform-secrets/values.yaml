tenantId: TENANT_ID
keyvaultName: KEY_VAULT_NAME
clientId: CLIENT_ID

replicas: 2

affinity:
  enabled: true

externalDns:
  azure:
    subscriptionId: SUBSCRIPTION_ID
    resourceGroup: RESOURCE_GROUP

namespaces:
  - name: kafka
    secretObjects:
      - secretName: kafka-connect-image-push-secret
        type: kubernetes.io/dockerconfigjson
        data:
          - objectName: "kafka-connect-image-push-secret"
            key: .dockerconfigjson
      - secretName: kafka-connect-image-pull-secret
        type: kubernetes.io/dockerconfigjson
        data:
          - objectName: "kafka-connect-image-pull-secret"
            key: .dockerconfigjson
      - secretName: data-platform-cluster-curity
        type: Opaque
        data:
          - objectName: "data-platform-cluster-curity"
            key: clientSecret
      - secretName: base24-sftp-credentials
        type: Opaque
        data:
          - objectName: "base24-sftp-username"
            key: username
          - objectName: "base24-sftp-password"
            key: password
      - secretName: bims-sftp-credentials
        type: Opaque
        data:
          - objectName: "bims-sftp-username"
            key: username
          - objectName: "bims-sftp-password"
            key: password
      - secretName: base24-authorizations-raw-producer-sas
        type: Opaque
        data:
          - objectName: "base24-authorizations-raw-producer-sas"
            key: token
      - secretName: base24-authorizations-raw-consumer-sas
        type: Opaque
        data:
          - objectName: "base24-authorizations-raw-consumer-sas"
            key: token
      - secretName: bims-mdd-raw-producer-sas
        type: Opaque
        data:
          - objectName: "bims-mdd-raw-producer-sas"
            key: token
      - secretName: bims-mdd-raw-consumer-sas
        type: Opaque
        data:
          - objectName: "bims-mdd-raw-consumer-sas"
            key: token
      - secretName: bims-add-raw-producer-sas
        type: Opaque
        data:
          - objectName: "bims-add-raw-producer-sas"
            key: token
      - secretName: bims-add-raw-consumer-sas
        type: Opaque
        data:
          - objectName: "bims-add-raw-consumer-sas"
            key: token
      - secretName: bims-pdd-raw-producer-sas
        type: Opaque
        data:
          - objectName: "bims-pdd-raw-producer-sas"
            key: token
      - secretName: bims-pdd-raw-consumer-sas
        type: Opaque
        data:
          - objectName: "bims-pdd-raw-consumer-sas"
            key: token
      - secretName: bims-bundles-raw-producer-sas
        type: Opaque
        data:
          - objectName: "bims-bundles-raw-producer-sas"
            key: token
      - secretName: bims-bundles-raw-consumer-sas
        type: Opaque
        data:
          - objectName: "bims-bundles-raw-consumer-sas"
            key: token
      - secretName: bims-elsa-raw-producer-sas
        type: Opaque
        data:
          - objectName: "bims-elsa-raw-producer-sas"
            key: token
      - secretName: bims-elsa-raw-consumer-sas
        type: Opaque
        data:
          - objectName: "bims-elsa-raw-consumer-sas"
            key: token
      - secretName: bims-exceptions-raw-producer-sas
        type: Opaque
        data:
          - objectName: "bims-exceptions-raw-producer-sas"
            key: token
      - secretName: bims-exceptions-raw-consumer-sas
        type: Opaque
        data:
          - objectName: "bims-exceptions-raw-consumer-sas"
            key: token
      - secretName: github-app-grafana
        type: Opaque
        data:
          - objectName: "github-app-grafana"
            key: client-secret
      - secretName: kafka-ui-credentials
        type: Opaque
        data:
          - objectName: "kafka-ui-auth-type"
            key: AUTH_TYPE
          - objectName: "kafka-ui-admin-user"
            key: SPRING_SECURITY_USER_NAME
          - objectName: "kafka-ui-admin-password"
            key: SPRING_SECURITY_USER_PASSWORD
    parameters:
      usePodIdentity: "false"
      useVMManagedIdentity: "false"
      objects: |
        array:
          - |
            objectName: "kafka-connect-image-push-secret"
            objectType: secret
          - |
            objectName: "kafka-connect-image-pull-secret"
            objectType: secret
          - |
            objectName: "data-platform-cluster-curity"
            objectType: secret
          - |
            objectName: "base24-sftp-username"
            objectType: secret
          - |
            objectName: "base24-sftp-password"
            objectType: secret
          - |
            objectName: "bims-sftp-username"
            objectType: secret
          - |
            objectName: "bims-sftp-password"
            objectType: secret
          - |
            objectName: "base24-authorizations-raw-producer-sas"
            objectType: secret
          - |
            objectName: "base24-authorizations-raw-consumer-sas"
            objectType: secret
          - |
            objectName: "bims-mdd-raw-producer-sas"
            objectType: secret
          - |
            objectName: "bims-mdd-raw-consumer-sas"
            objectType: secret
          - |
            objectName: "bims-add-raw-producer-sas"
            objectType: secret
          - |
            objectName: "bims-add-raw-consumer-sas"
            objectType: secret
          - |
            objectName: "bims-pdd-raw-producer-sas"
            objectType: secret
          - |
            objectName: "bims-pdd-raw-consumer-sas"
            objectType: secret
          - |
            objectName: "bims-bundles-raw-producer-sas"
            objectType: secret
          - |
            objectName: "bims-bundles-raw-consumer-sas"
            objectType: secret
          - |
            objectName: "bims-elsa-raw-producer-sas"
            objectType: secret
          - |
            objectName: "bims-elsa-raw-consumer-sas"
            objectType: secret
          - |
            objectName: "bims-exceptions-raw-producer-sas"
            objectType: secret
          - |
            objectName: "bims-exceptions-raw-consumer-sas"
            objectType: secret
          - |
            objectName: "github-app-grafana"
            objectType: secret
          - |
            objectName: "kafka-ui-auth-type"
            objectType: secret
          - |
            objectName: "kafka-ui-admin-user"
            objectType: secret
          - |
            objectName: "kafka-ui-admin-password"
            objectType: secret
  - name: monitoring
    secretObjects:
      - secretName: github-app-grafana
        type: Opaque
        data:
          - objectName: "github-app-grafana"
            key: client-secret
      - secretName: grafana-credentials
        type: Opaque
        data:
          - objectName: "grafana-admin-user"
            key: admin-user
          - objectName: "grafana-admin-password"
            key: admin-password
    parameters:
      usePodIdentity: "false"
      useVMManagedIdentity: "false"
      objects: |
        array:
          - |
            objectName: "github-app-grafana"
            objectType: secret
          - |
            objectName: "grafana-admin-user"
            objectType: secret
          - |
            objectName: "grafana-admin-password"
            objectType: secret
