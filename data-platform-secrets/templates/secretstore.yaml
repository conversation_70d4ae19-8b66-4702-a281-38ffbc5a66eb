{{- range .Values.namespaces }}
---
kind: Deployment
apiVersion: apps/v1
metadata:
  name: {{ .name }}-secrets-store
  namespace: {{ .name }}
spec:
  replicas: {{ $.Values.replicas | default 1 }}
  selector:
    matchLabels:
      app: {{ .name }}-secrets-store
  template:
    metadata:
      labels:
        app: {{ .name }}-secrets-store
    spec:
      serviceAccountName: {{ $.Values.serviceAccountName | default "workload-identity-sa" }}
      containers:
        - name: busybox
          image: k8s.gcr.io/e2e-test-images/busybox:1.29-1
          command:
            - "/bin/sleep"
            - "{{ $.Values.sleepDuration | default "10000" }}"
          volumeMounts:
            - name: secrets-store
              mountPath: "/mnt/secrets-store"
              readOnly: true
      volumes:
        - name: secrets-store
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: "{{ .name }}-secret-provider"
{{- end }}
