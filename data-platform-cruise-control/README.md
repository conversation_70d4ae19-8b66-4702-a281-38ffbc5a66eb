# Rebalancing the cluster

## Create KafkaRebalance resource w default goals
```
kubectl apply -f kafka-rebalance-full.yaml
```

## Check status until proposal is ready
```
kubectl describe kafkarebalance -n kafka data-platform-cluster-rebalance
```

## Approve proposal
```
kubectl annotate kafkarebalance -n kafka data-platform-cluster-rebalance strimzi.io/rebalance=approve
```

# Refresh proposal
```
kubectl annotate kafkarebalance -n kafka data-platform-cluster-rebalance strimzi.io/rebalance=refresh
```