#!/bin/bash

K8S_ENV=${1:-stage}

# Define the directory to search for ArgoCD Helm chart manifests
YAML_DIR="argocd-app-of-apps/environments/$K8S_ENV"

echo "Checking for Helm chart upgrades in environment $K8S_ENV..."

# Initialize variables
repo_url=""
chart_name=""
deployed_version=""

helm repo update &>/dev/null

# Find targetRevision and repoURL in YAML files and parse them
find "$YAML_DIR" -name '*.yaml' | xargs grep -E 'targetRevision|repoURL' | grep 'HEAD' -B3 | grep -vE 'HEAD|--|automate' | while IFS= read -r line; do
    if [[ "$line" == *repoURL* ]]; then
        repo_url=$(echo "$line" | sed -E 's/.*repoURL: //')
    elif [[ "$line" == *targetRevision* ]]; then
        chart_file=$(echo "$line" | cut -d: -f1)
        chart_name=$(basename "$chart_file" .yaml)
        deployed_version=$(echo "$line" | sed -E 's/.*targetRevision: //')

        if [[ -z "$repo_url" ]]; then
            echo "Error: repoURL not found for $chart_name"
            continue
        fi

        if [[ "$repo_url" != *http* ]]; then
            continue
        fi

        found_name=$(helm repo list | grep -F -- "$repo_url" | head -n 1 | awk '{print $1}')

        if [ -n "$found_name" ]; then
            chart_repo="$found_name"
        else
            echo "helm repo add NAME $repo_url"
            chart_repo="$chart_name-repo"
        fi

        # Get the latest version and app version from the Helm repo
        helm_data=$(helm search repo "$chart_repo/$chart_name" --output json | jq -r '.[0]')
        latest_version=$(echo "$helm_data" | jq -r '.version')
        app_version=$(echo "$helm_data" | jq -r '.app_version')

        if [[ "$latest_version" == "null" || -z "$latest_version" ]]; then
            echo "Error: Could not find chart $chart_name in $repo_url"
        else
            # Check if an upgrade is needed
            if [[ "$deployed_version" != "$latest_version" ]]; then
                echo "Upgrade available for $chart_name: $deployed_version -> $latest_version (App Version: $app_version)"
            fi
        fi

        # Reset variables for the next chart
        repo_url=""
    fi
done
