#!/bin/bash

# --- Konfiguration ---
# Ersätt dessa med URL:erna till dina Schema Registry-instanser
OLD_SR_URL="http://din-gamla-schema-registry-url:8081"
NEW_SR_URL="http://din-nya-schema-registry-url:8081" # Den som pekar på data-platform-cluster
# ---------------------

# Kontrollera om jq är installerat
if ! command -v jq &> /dev/null
then
    echo "FEL: jq kunde inte hittas. Installera jq för att köra detta skript."
    echo "Exempel: sudo apt-get install jq  eller  brew install jq"
    exit 1
fi

# Funktion för att hämta och normalisera den senaste schema-strängen för ett subject
# Argument: $1 = SR_URL, $2 = subject_name
get_latest_schema() {
    local sr_url="$1"
    local subject_name="$2"
    local response_body
    local http_code
    local schema_string
    local normalized_schema_string
    local temp_file

    # Skapa en temporär fil för curl-output på ett säkrare sätt
    temp_file=$(mktemp)
    # Hämta HTTP-kod och body separat
    http_code=$(curl --silent --write-out "%{http_code}" --output "$temp_file" -X GET "${sr_url}/subjects/${subject_name}/versions/latest")
    response_body=$(cat "$temp_file")
    rm "$temp_file"


    if [ "$http_code" -eq 200 ]; then
        schema_string=$(echo "$response_body" | jq -r '.schema')
        if [ -z "$schema_string" ] || [ "$schema_string" == "null" ]; then
             # Hantera fallet där .schema är null eller tom sträng, vilket kan hända om subject/version finns men schema saknas (ovanligt)
            echo "ERROR_EMPTY_SCHEMA_STRING (HTTP $http_code, Subject: $subject_name)"
            return
        fi

        # Försök att normalisera om det är en JSON-sträng (Avro/JSON Schema)
        # jq --sort-keys . kommer att omformatera JSON och sortera nycklar för konsekvent jämförelse.
        # Om schema_string inte är giltig JSON, kommer jq att misslyckas (exit code != 0).
        normalized_schema_string=$(echo "$schema_string" | jq --sort-keys . 2>/dev/null)
        if [ $? -eq 0 ]; then # Kontrollera om jq lyckades
            echo "$normalized_schema_string"
        else
            # Om inte giltig JSON (t.ex. Protobuf) eller om jq misslyckades, returnera originalsträngen.
            echo "$schema_string"
        fi
    elif [ "$http_code" -eq 404 ]; then
        echo "SCHEMA_NOT_FOUND"
    else
        echo "ERROR_FETCHING_SCHEMA (HTTP $http_code, Subject: $subject_name)"
        # echo "DEBUG: Felrespons för ${subject_name} från ${sr_url}: ${response_body}" >&2 # Avkommentera för felsökning
    fi
}

echo "Hämtar subjects från gamla Schema Registry: $OLD_SR_URL"
subjects_json=$(curl --silent -X GET "${OLD_SR_URL}/subjects")

# Kontrollera om curl lyckades
if [ $? -ne 0 ]; then
    echo "FEL: Kunde inte ansluta till gamla Schema Registry på $OLD_SR_URL."
    exit 1
fi

# Kontrollera om svaret är giltig JSON
if ! echo "$subjects_json" | jq -e . > /dev/null 2>&1; then
    echo "FEL: Ogiltigt JSON-svar från gamla Schema Registry: $subjects_json"
    exit 1
fi

subjects=$(echo "$subjects_json" | jq -r '.[]')

if [ -z "$subjects" ]; then
    echo "Inga subjects hittades i gamla Schema Registry."
    exit 0
fi

echo "Hittade följande subjects:"
echo "$subjects"
echo "--------------------------------------------------"

# Räknare för sammanfattning
total_subjects=0
matches=0
mismatches=0
old_sr_errors=0
new_sr_errors=0
new_sr_missing=0

for subject in $subjects; do
    total_subjects=$((total_subjects + 1))
    echo -e "\nJämför subject: $subject"

    schema_old=$(get_latest_schema "$OLD_SR_URL" "$subject")
    # Kontrollera fel för gamla SR direkt
    if [[ "$schema_old" == "ERROR_FETCHING_SCHEMA"* ]] || [[ "$schema_old" == "ERROR_EMPTY_SCHEMA_STRING"* ]]; then
        echo "  GAMLA SR: Fel vid hämtning av schema för $subject: $schema_old"
        old_sr_errors=$((old_sr_errors + 1))
        continue # Gå till nästa subject
    elif [ "$schema_old" == "SCHEMA_NOT_FOUND" ]; then # Borde inte hända om vi itererar subjects från gamla SR
        echo "  GAMLA SR: Schema för $subject oväntat inte hittat (fanns i subject-listan)."
        old_sr_errors=$((old_sr_errors + 1))
        continue
    fi

    schema_new=$(get_latest_schema "$NEW_SR_URL" "$subject")
    # Kontrollera fel för nya SR
    if [[ "$schema_new" == "ERROR_FETCHING_SCHEMA"* ]] || [[ "$schema_new" == "ERROR_EMPTY_SCHEMA_STRING"* ]]; then
        echo "  NYA SR: Fel vid hämtning av schema för $subject: $schema_new"
        new_sr_errors=$((new_sr_errors + 1))
        continue # Gå till nästa subject
    elif [ "$schema_new" == "SCHEMA_NOT_FOUND" ]; then
        echo "  SKILLNAD: Subject $subject finns i GAMLA SR men INTE i NYA SR."
        mismatches=$((mismatches + 1))
        new_sr_missing=$((new_sr_missing + 1))
        continue
    fi

    # Faktisk jämförelse av schemainnehåll
    if [ "$schema_old" == "$schema_new" ]; then
        echo "  MATCH: Senaste schemat för $subject är identiskt."
        matches=$((matches + 1))
    else
        echo "  SKILLNAD: Senaste schemat för $subject skiljer sig."
        mismatches=$((mismatches + 1))
        
        # Avkommentera nedan för att spara och diffa scheman (kan vara mycket output)
        # echo "    Detaljer om skillnaden:"
        # echo "$schema_old" > "/tmp/${subject}_old.schema"
        # echo "$schema_new" > "/tmp/${subject}_new.schema"
        # diff -u "/tmp/${subject}_old.schema" "/tmp/${subject}_new.schema" || true # || true för att skriptet inte ska avslutas om diff hittar skillnader
        # rm "/tmp/${subject}_old.schema" "/tmp/${subject}_new.schema"
        echo "    --- Schema från GAMLA SR ---"
        echo "$schema_old"
        echo "    --- Schema från NYA SR ---"
        echo "$schema_new"
        echo "    --------------------------"
    fi
done

echo -e "\n--- Sammanfattning ---"
echo "Totalt antal subjects från gamla SR bearbetade: $total_subjects"
echo "Identiska scheman: $matches"
echo "Scheman med skillnader eller som saknas i nya SR: $mismatches"
if [ "$new_sr_missing" -gt 0 ]; then
  echo "  Varav saknas helt i nya SR: $new_sr_missing"
fi
echo "Fel vid hämtning från gamla SR: $old_sr_errors"
echo "Fel vid hämtning från nya SR: $new_sr_errors"
echo "--------------------"

if [ "$mismatches" -gt 0 ] || [ "$old_sr_errors" -gt 0 ] || [ "$new_sr_errors" -gt 0 ]; then
    echo "VERIFIERING MISSLYCKAD: Det finns skillnader eller fel."
    exit 1
else
    echo "VERIFIERING LYCKAD: Alla senaste scheman är identiska."
    exit 0
fi