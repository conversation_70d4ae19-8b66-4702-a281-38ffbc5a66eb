#!/bin/bash

usage() {
    echo "Usage: $0 [-e environment] [-u client_id] [-n namespace] [-c cluster] [-t topic] [-s secret_namespace] [-r secret]"
    echo "Options:"
    echo "  -e  Environment: dev, stage, prod (default: stage)"
    echo "  -u  Client Id (default: automate-efecte)"
    echo "  -n  Kafka Namespace (default: kafka)"
    echo "  -c  Cluster Name (default: data-platform-cluster)"
    echo "  -t  Topic Name (default: automate-efecte.external-company-raw)"
    echo "  -s  Curity Secret Namespace (default: automate)"
    echo "  -r  Curity Secret (default: automate-efecte-curity)"
    exit 1
}

while getopts ":e:u:n:c:t:s:r:" opt; do
  case ${opt} in
    e )
      K8S_ENV=$OPTARG
      ;;
    u )
      CLIENT_ID=$OPTARG
      ;;
    n )
      KAFKA_NAMESPACE=$OPTARG
      ;;
    c )
      CLUSTER_NAME=$OPTARG
      ;;
    t )
      TOPIC_NAME=$OPTARG
      ;;
    s )
      CURITY_SECRET_NAMESPACE=$OPTARG
      ;;
    r )
      CURITY_SECRET=$OPTARG
      ;;
    \? )
      echo "Invalid option: $OPTARG" 1>&2
      usage
      ;;
    : )
      echo "Invalid option: $OPTARG requires an argument" 1>&2
      usage
      ;;
  esac
done
shift $((OPTIND -1))

# Set default values if not provided
K8S_ENV=${K8S_ENV:-stage}
CLIENT_ID=${CLIENT_ID:-automate-efecte}
KAFKA_NAMESPACE=${KAFKA_NAMESPACE:-kafka}
CLUSTER_NAME=${CLUSTER_NAME:-data-platform-cluster}
TOPIC_NAME=${TOPIC_NAME:-automate-efecte.external-company-raw}
CURITY_SECRET_NAMESPACE=${CURITY_SECRET_NAMESPACE:-automate}
CURITY_SECRET=${CURITY_SECRET:-automate-efecte-curity}
CA_CERT_URL="https://letsencrypt.org/certs/staging/letsencrypt-stg-root-x1.pem"

case $K8S_ENV in
  dev)
    SUBSCRIPTION=c8b46846-3156-4220-8dfc-af0fafb8d2b0
    RG=dev-cloud-data-aks-rg
    AKS=dev-cloud-data-aks
    DEFAULT_BROKER="bootstrap.kafka.dev.payex.net:9094"
    TOKEN_URL=https://id.dev.payex.com/swedbankpay/oauth-token
    ;;
  stage)
    SUBSCRIPTION=********-7f22-4468-a34d-d61125f01797
    RG=stage-cloud-data-aks-rg
    AKS=stage-cloud-data-aks
    DEFAULT_BROKER="bootstrap.kafka.stage.payex.net:9094"
    BROKER_SECRET=data-platform-cluster-broker-tls
    TOKEN_URL=https://id.stage.payex.com/swedbankpay/oauth-token
    ;;
  prod)
    SUBSCRIPTION=38cb90f4-bf95-4b2c-b952-93f468d17aa0
    RG=prod-cloud-data-aks-rg
    AKS=prod-cloud-data-aks
    DEFAULT_BROKER="bootstrap.kafka.payex.net:9094"
    BROKER_SECRET=data-platform-cluster-broker-tls
    TOKEN_URL=https://id.payex.com/swedbankpay/oauth-token
    ;;
  *)
    echo "Unsupported env"
    exit 1
    ;;
esac

az account set --subscription $SUBSCRIPTION

az aks get-credentials \
  --resource-group $RG \
  --name $AKS \
  --overwrite-existing \
  --admin

kubectl config current-context
kubectl cluster-info

BOOTSTRAP_SERVER=${DEFAULT_BROKER}

echo "BOOTSTRAP_SERVER=${BOOTSTRAP_SERVER}"
echo "CLIENT_ID=${CLIENT_ID}"
echo "KAFKA_NAMESPACE=${KAFKA_NAMESPACE}"
echo "CLUSTER_NAME=${CLUSTER_NAME}"
echo "TOKEN_URL=${TOKEN_URL}"

kubectl --namespace="${KAFKA_NAMESPACE}" get secret data-platform-cluster-broker-tls -o jsonpath='{.data.tls\.crt}' | base64 --decode > tls.crt

CLIENT_SECRET=$(kubectl get secrets -n $CURITY_SECRET_NAMESPACE $CURITY_SECRET -o yaml|yq '.data.clientSecret'|base64 -d)

case $K8S_ENV in
  stage)

curl -o ca.crt $CA_CERT_URL

CA_CERT=$(awk '$1=$1' ORS='\\n' < ca.crt)

cat <<EOF >client.properties
security.protocol=SASL_SSL
ssl.truststore.type=PEM
ssl.truststore.certificates=$CA_CERT
sasl.login.callback.handler.class=org.apache.kafka.common.security.oauthbearer.secured.OAuthBearerLoginCallbackHandler
sasl.mechanism=OAUTHBEARER
sasl.oauthbearer.token.endpoint.url=$TOKEN_URL
sasl.jaas.config= \\
  org.apache.kafka.common.security.oauthbearer.OAuthBearerLoginModule required \\
    scope='kafka' \\
    clientId='$CLIENT_ID' \\
    clientSecret='$CLIENT_SECRET';
EOF

    ;;

  prod)

cat <<EOF >client.properties
security.protocol=SASL_SSL
sasl.login.callback.handler.class=org.apache.kafka.common.security.oauthbearer.secured.OAuthBearerLoginCallbackHandler
sasl.mechanism=OAUTHBEARER
sasl.oauthbearer.token.endpoint.url=$TOKEN_URL
sasl.jaas.config= \\
  org.apache.kafka.common.security.oauthbearer.OAuthBearerLoginModule required \
    scope='kafka' \\
    clientId='$CLIENT_ID' \\
    clientSecret='$CLIENT_SECRET';
EOF

    ;;
esac

chmod 0600 client.properties

echo
echo "---| List topics using Kafka CLI:"
cat << EOF
kafka-topics --bootstrap-server "$BOOTSTRAP_SERVER" --command-config client.properties -list|sort
EOF

echo
echo "---| Check topic config using Kafka CLI:"
cat << EOF
kafka-configs --bootstrap-server "$BOOTSTRAP_SERVER" --entity-type topics --entity-name "${TOPIC_NAME}" --all --describe --command-config client.properties
EOF

echo
echo "---| Consume messages using Kafka CLI:"
cat << EOF
kafka-console-consumer --bootstrap-server "$BOOTSTRAP_SERVER" --topic "${TOPIC_NAME}" --group $CLIENT_ID-console --consumer.config client.properties --from-beginning --timeout-ms 2000
EOF

echo
echo "---| List consumer groups using Kafka CLI:"
cat << EOF
kafka-consumer-groups --list --bootstrap-server "$BOOTSTRAP_SERVER" --command-config client.properties
EOF

echo
echo "---| Describe consumer group using Kafka CLI:"
cat << EOF
kafka-consumer-groups --bootstrap-server "$BOOTSTRAP_SERVER" --command-config client.properties -describe -group $CLIENT_ID
EOF

