#!/bin/bash

# Define directories for stage and prod
STAGE_DIR="argocd-app-of-apps/environments/stage"
PROD_DIR="argocd-app-of-apps/environments/prod"
BASE_DIR="."  # Current directory where Helm charts are located

# Function to extract full resource names and versions from ArgoCD YAMLs
extract_versions() {
    local dir=$1
    find "$dir" -name '*.yaml' | while IFS= read -r file; do
        app_name=$(basename "$file" .yaml | tr -d '\r')  # Remove carriage returns
        version=$(grep -A1 'ghcr.io' "$file" | grep targetRevision | awk '{print $2}' | tr -d '"' | tr -d '\r')  # Remove quotes and carriage returns
        if [[ -n $version ]]; then
            echo "$app_name $version"
        fi
    done | LC_ALL=C sort
}

# Function to extract versions for "data-platform-*" resources from Chart.yaml in the base directory
extract_chart_versions() {
    find "$BASE_DIR" -mindepth 1 -maxdepth 2 -type f -name "Chart.yaml" | while IFS= read -r chart_file; do
        app_name=$(basename "$(dirname "$chart_file")")  # Extract folder name (Helm chart name)
        if [[ "$app_name" == data-platform-* ]]; then
            version=$(grep '^version:' "$chart_file" | awk '{print $2}' | tr -d '"' | tr -d '\r')  # Remove quotes and carriage returns
            if [[ -n $version ]]; then
                echo "$app_name $version"
            fi
        fi
    done | LC_ALL=C sort
}

# Extract versions for stage and prod
stage_versions=$(extract_versions "$STAGE_DIR")
stage_chart_versions=$(extract_chart_versions)  # Data-platform chart versions from base directory
prod_versions=$(extract_versions "$PROD_DIR")

# Merge stage ArgoCD versions and Helm chart versions
{
    echo "$stage_versions"
    echo "$stage_chart_versions"
} | LC_ALL=C sort > /tmp/stage_versions.txt  # Ensure proper sorting after merging

# Save prod versions
echo "$prod_versions" | LC_ALL=C sort > /tmp/prod_versions.txt

# Ensure both files are sorted for perfect comparison
sort -o /tmp/stage_versions.txt /tmp/stage_versions.txt
sort -o /tmp/prod_versions.txt /tmp/prod_versions.txt

# Debugging Step: Check what was extracted (Optional)
echo "Stage versions:"
cat /tmp/stage_versions.txt
echo "Prod versions:"
cat /tmp/prod_versions.txt

# Compare versions using `awk` to align and show proper version differences
echo "Comparing versions between stage and prod..."
awk '
{
    app_name = $1;
    version = $2;
    if (FNR == NR) {
        stage[app_name] = version;  # Store stage versions
        all_apps[app_name] = 1;
    } else {
        prod[app_name] = version;   # Store prod versions
        all_apps[app_name] = 1;
    }
}
END {
    for (app in all_apps) {
        if (!(app in prod)) {
            printf "%-30s %-10s > (only in stage)\n", app, stage[app];
        } else if (!(app in stage)) {
            printf "%-30s %-10s < (only in prod)\n", app, prod[app];
        } else if (stage[app] != prod[app]) {
            marker = (stage[app] > prod[app]) ? "▶" : "◀";  # Highlight greater version
            printf "%-30s %-10s %s %-10s\n", app, stage[app], marker, prod[app];
        }
    }
}' /tmp/stage_versions.txt /tmp/prod_versions.txt | LC_ALL=C sort

# Clean up temp files
rm -f /tmp/stage_versions.txt /tmp/prod_versions.txt
