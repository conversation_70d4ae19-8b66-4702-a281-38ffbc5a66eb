cat <<EOF | kubectl -n kafka apply -f -
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: kafka-client
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: kafka-client
  serviceName: kafka-client
  template:
    metadata:
      labels:
        app.kubernetes.io/component: kafka-client
        app.kubernetes.io/managed-by: data-platform
        app.kubernetes.io/name: kafka-client
        app.payex.com/service: kafka-client
        policy.payex.com/allowExternalImageRegistries: "true"
        policy.payex.com/allowReadWriteRootFilesystem: "true"
        policy.payex.com/allowAllPortOpenings: "true"
    spec:
      containers:
      - name: kafka-client
        image: ueisele/kcat:1.7.1-librdkafka2.2.0
        command:
        - sh
        - -c
        - "exec tail -f /dev/null"
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          readOnlyRootFilesystem: false

EOF

# cat kcat.config | kubectl exec -i -n kafka kafka-client-0  "--" sh -c "mkdir -p /home/<USER>/.config; cat > /home/<USER>/.config/kcat.conf"
# cat ca.crt | kubectl exec -i -n kafka kafka-client-0  "--" sh -c "cat > /home/<USER>/ca.crt" 