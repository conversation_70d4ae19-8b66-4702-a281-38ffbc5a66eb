#!/bin/bash

# Stoppa skriptet om något kommando misslyckas
set -e

echo "======================================================"
echo "    Skript för att skapa enkel Iceberg-tabell       "
echo "======================================================"

# --- Konfiguration ---
SPARK_VERSION="3.5.5"       # Uppdaterad Spark-version
SCALA_VERSION="2.13"       # Uppdaterad Scala-version för Spark 3.5.x
ICEBERG_VERSION="1.5.0"     # Iceberg 1.5.0 är kompatibel med Spark 3.5
APP_NAME="SimpleIcebergExample_Spark35"

# Sökvägar (relativa till där skriptet körs)
INSTALL_BASE_DIR="$(pwd)/iceberg_setup" # Huvudmapp för installationer
SPARK_INSTALL_DIR="${INSTALL_BASE_DIR}/spark"
# URL:en konstrueras nu automatiskt korrekt med de uppdaterade versionerna
SPARK_DOWNLOAD_URL="https://archive.apache.org/dist/spark/spark-${SPARK_VERSION}/spark-${SPARK_VERSION}-bin-hadoop3-scala${SCALA_VERSION}.tgz"
SPARK_TGZ_FILENAME="spark-${SPARK_VERSION}-bin-hadoop3-scala${SCALA_VERSION}.tgz"
SPARK_HOME="${SPARK_INSTALL_DIR}/spark-${SPARK_VERSION}-bin-hadoop3-scala${SCALA_VERSION}"

# Iceberg-inställningar (katalog, db, tabell kan vara samma)
ICEBERG_WAREHOUSE_DIR="${INSTALL_BASE_DIR}/iceberg_warehouse" # Mapp för Iceberg data/metadata
ICEBERG_CATALOG_NAME="local_hadoop_catalog" # Namn på katalogen vi definierar i Spark
ICEBERG_DB_NAME="enkel_db"
ICEBERG_TABLE_NAME="min_enkla_tabell"
# --- Slut på konfiguration ---

# 1. Kontrollera Java
echo ""
echo "[Steg 1/4] Kontrollerar Java..."
if ! command -v java > /dev/null; then
    echo ">>> FEL: Java hittades inte i din PATH."
    echo ">>> Installera Java (JDK 11 eller 17 rekommenderas)."
    echo ">>> På macOS med Homebrew: brew install openjdk@17"
    echo ">>> Se till att 'java' finns i din PATH."
    exit 1
fi
JAVA_VERSION=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}')
echo ">>> Java version ${JAVA_VERSION} hittades."

# 2. Ladda ner och packa upp Spark (om nödvändigt)
echo ""
echo "[Steg 2/4] Kontrollerar Apache Spark..."
mkdir -p "${SPARK_INSTALL_DIR}" # Skapa installationsmapp om den inte finns

if [ ! -d "${SPARK_HOME}" ]; then
    echo ">>> Spark hittades inte i ${SPARK_HOME}."
    # Kontrollera om arkivet redan är nedladdat
    if [ ! -f "${SPARK_INSTALL_DIR}/${SPARK_TGZ_FILENAME}" ]; then
        echo ">>> Laddar ner Spark ${SPARK_VERSION} från ${SPARK_DOWNLOAD_URL}..."
        curl -L -o "${SPARK_INSTALL_DIR}/${SPARK_TGZ_FILENAME}" "${SPARK_DOWNLOAD_URL}"
        echo ">>> Nedladdning klar."
    else
         echo ">>> Spark-arkiv (${SPARK_TGZ_FILENAME}) finns redan."
    fi
    echo ">>> Packar upp Spark (kan ta en stund)..."
    tar -xzf "${SPARK_INSTALL_DIR}/${SPARK_TGZ_FILENAME}" -C "${SPARK_INSTALL_DIR}"
    echo ">>> Spark uppackat i ${SPARK_HOME}"
else
    echo ">>> Spark finns redan i ${SPARK_HOME}"
fi

# 3. Skapa Warehouse-mapp
echo ""
echo "[Steg 3/4] Förbereder Iceberg warehouse-mapp..."
mkdir -p "${ICEBERG_WAREHOUSE_DIR}"
echo ">>> Warehouse-mapp: ${ICEBERG_WAREHOUSE_DIR}"

# 4. Kör Spark SQL för att skapa tabell och infoga data
echo ""
echo "[Steg 4/4] Startar spark-sql för att skapa Iceberg-tabell..."
echo ">>> Detta kan ta en stund första gången då Spark laddar ner Iceberg-paketet."

# Hela sökvägen till spark-sql-binären
SPARK_SQL_CMD="${SPARK_HOME}/bin/spark-sql"

# Använd absolut sökväg till warehouse för Spark-konfigurationen
ABS_WAREHOUSE_PATH=$(cd "${ICEBERG_WAREHOUSE_DIR}" && pwd)

# Kör Spark SQL med alla konfigurationer och SQL-kommandon via Here Document (<<EOF ... EOF)
"${SPARK_SQL_CMD}" \
  --name "${APP_NAME}" \
  --packages org.apache.iceberg:iceberg-spark-runtime-${SPARK_VERSION%.*}_${SCALA_VERSION}:${ICEBERG_VERSION} \
  --conf spark.sql.extensions=org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions \
  --conf spark.sql.catalog.${ICEBERG_CATALOG_NAME}=org.apache.iceberg.spark.SparkCatalog \
  --conf spark.sql.catalog.${ICEBERG_CATALOG_NAME}.type=hadoop \
  --conf spark.sql.catalog.${ICEBERG_CATALOG_NAME}.warehouse="file://${ABS_WAREHOUSE_PATH}" \
  --conf spark.sql.defaultCatalog=${ICEBERG_CATALOG_NAME} \
  <<EOF

-- Skapa databas om den inte finns (använder defaultCatalog som är satt ovan)
CREATE DATABASE IF NOT EXISTS ${ICEBERG_DB_NAME};

-- Använd databasen för följande kommandon
USE ${ICEBERG_DB_NAME};

-- Skapa Iceberg-tabellen (Parquet är standardformatet)
-- Släpp tabellen först om den finns från en tidigare körning (gör skriptet återkörbart)
DROP TABLE IF EXISTS ${ICEBERG_TABLE_NAME};

CREATE TABLE ${ICEBERG_TABLE_NAME} (
    id BIGINT,
    namn STRING,
    varde DOUBLE
)
USING iceberg
TBLPROPERTIES ('comment'='Enkel Iceberg-exempeltabell skapad av skript'); -- Valfri kommentar

-- Infoga lite data
INSERT INTO ${ICEBERG_TABLE_NAME} VALUES
(101, 'Dator', 15500.75),
(102, 'Skärm', 4200.00),
(103, 'Mus', 350.50);

-- Visa datat som infogats för att verifiera
SELECT * FROM ${ICEBERG_TABLE_NAME} ORDER BY id;

-- Visa tabellens schema (valfritt)
-- DESCRIBE TABLE ${ICEBERG_TABLE_NAME};

EOF
# Slut på Here Document

EXIT_CODE=$? # Fånga exit code från spark-sql

echo ""
if [ ${EXIT_CODE} -eq 0 ]; then
    echo "======================================================"
    echo ">>> SUCCESS! Skriptet slutfördes."
    echo ">>> Iceberg-tabellen '${ICEBERG_DB_NAME}.${ICEBERG_TABLE_NAME}' har skapats."
    echo ">>> Data och metadata finns under: ${ABS_WAREHOUSE_PATH}"
    echo ">>> Du kan inspektera mapparna 'data' och 'metadata' där."
    echo "======================================================"
else
    echo "======================================================"
    echo ">>> FEL! Något gick fel när spark-sql kördes (Exit Code: ${EXIT_CODE})."
    echo ">>> Kontrollera felmeddelandena ovan."
    echo "======================================================"
    exit ${EXIT_CODE}
fi