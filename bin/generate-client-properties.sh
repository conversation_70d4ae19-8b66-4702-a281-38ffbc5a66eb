#!/bin/bash

# Variables for namespace, cluster prefix, Kafka user, and broker pod
NAMESPACE="kafka"
CLUSTER_PREFIX="data-platform-cluster"
KAFKA_USER="super-user"
# BROKER_POD="$CLUSTER_PREFIX-broker-0"
BROKER_POD="schema-registry-0"
BOOTSTRAP_SERVER="$CLUSTER_PREFIX-kafka-bootstrap:9093"

# Ensure necessary tools are installed
command -v kubectl >/dev/null 2>&1 || { echo >&2 "kubectl not installed. Aborting."; exit 1; }
command -v yq >/dev/null 2>&1 || { echo >&2 "yq not installed. Aborting."; exit 1; }
command -v base64 >/dev/null 2>&1 || { echo >&2 "base64 not installed. Aborting."; exit 1; }

# Clear any existing files before creating new ones
> ca.crt
> user.key
> user.crt

# Retrieve and decode secrets
kubectl get secret -n $NAMESPACE ${CLUSTER_PREFIX}-cluster-ca-cert -o yaml | \
  yq '.data."ca.crt"' | base64 -d >> ca.crt || { echo "Failed to get CA certificate"; exit 1; }

kubectl get secret -n $NAMESPACE $KAFKA_USER -o yaml | \
  yq '.data."user.key"' | base64 -d >> user.key || { echo "Failed to get user key"; exit 1; }

kubectl get secret -n $NAMESPACE $KAFKA_USER -o yaml | \
  yq '.data."user.crt"' | base64 -d >> user.crt || { echo "Failed to get user certificate"; exit 1; }

# Convert files to a single-line format with newline literals for the client.properties
CA_CRT=$(awk '$1=$1' ORS='\\n' < ca.crt)
USER_KEY=$(awk '$1=$1' ORS='\\n' < user.key)
USER_CRT=$(awk '$1=$1' ORS='\\n' < user.crt)

# Create the client.properties on the Kafka broker pod
# kubectl -n $NAMESPACE exec -i $BROKER_POD -c kafka -- bash -c "cat > /tmp/client.properties" <<EOF
kubectl -n $NAMESPACE exec -i $BROKER_POD -- bash -c "cat > /tmp/client.properties" <<EOF
security.protocol=SSL
ssl.truststore.type=PEM
ssl.truststore.certificates=$CA_CRT
ssl.keystore.type=PEM
ssl.keystore.key=$USER_KEY
ssl.keystore.certificate.chain=$USER_CRT
EOF

# Clean up files
rm ca.crt user.key user.crt

echo
echo "---| List topics using Kafka CLI:"
cat << EOF
kubectl exec -n kafka $BROKER_POD -c kafka -- /opt/kafka/bin/kafka-topics.sh --bootstrap-server "$BOOTSTRAP_SERVER" --command-config /tmp/client.properties -list|sort
EOF

echo
echo "---| Consume messages using Avro console consumer:"
cat << EOF
kubectl exec -n kafka $BROKER_POD -- /opt/bitnami/schema-registry/bin/kafka-avro-console-consumer --bootstrap-server "$BOOTSTRAP_SERVER" --consumer.config /tmp/client.properties --topic base24-authorizations-curated --property schema.registry.url=http://schema-registry.kafka.svc:8081 --from-beginning
EOF

echo
echo "---| Consume messages using Avro console consumer:"
cat << EOF
kubectl exec -n kafka $BROKER_POD -- /opt/bitnami/schema-registry/bin/kafka-avro-console-consumer --bootstrap-server "$BOOTSTRAP_SERVER" --consumer.config /tmp/client.properties --topic base24-authorizations-curated --property schema.registry.url=http://apicurio-registry-service.kafka.svc:8080/apis/ccompat/v7 --from-beginning
EOF

echo
echo "---| Consume messages using console consumer:"
cat << EOF
kubectl exec -n kafka $BROKER_POD -c kafka -- /opt/kafka/bin/kafka-console-consumer.sh --topic base24-authorizations-curated --bootstrap-server data-platform-cluster-kafka-bootstrap.kafka.svc:9093 --from-beginning --consumer.config /tmp/client.properties  --formatter kafka.tools.DefaultMessageFormatter --property print.value=true --property print.key=true
EOF
