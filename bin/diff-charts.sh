#!/bin/bash

# Iterate over each folder in the current directory
for folder in */; do
    # # Skip the folder named "argocd-app-of-apps-parent/"
    # if [ "$folder" == "argocd-app-of-apps-parent/" ]; then
    #     continue
    # fi

    # Check if the item is a directory
    if [ -d "$folder" ]; then
        # Check if the folder contains a file named "Chart.yaml"
        if [ -f "${folder}Chart.yaml" ]; then
            # Extract the version number from Chart.yaml
            version=$(grep -E '^version:\s*' "${folder}Chart.yaml" | awk '{print $2}' | sed 's/^["'\''"]//;s/["'\''"]$//')
            # Construct the tag name using the version and folder name
            tag="${version}-${folder%/}" # Remove trailing slash
            # Replace spaces with underscores (_) in the tag name
            tag="${tag// /_}"
            # Check if the tag exists
            if git rev-parse -q --verify "refs/tags/$tag" >/dev/null; then
                # Check if there are any differences between the specified tag and the current state
                if git diff --quiet "refs/tags/$tag"..HEAD -- "$folder" -- ':!*/Chart.yaml'; then
                    continue
                fi
                # Echo the git diff command
                echo "git diff refs/tags/$tag..HEAD -- \"$folder\""
            fi
        fi
    fi
done
