#!/bin/bash

K8S_ENV=${1:-dev}

case $K8S_ENV in
  dev)
    SUBSCRIPTION=c8b46846-3156-4220-8dfc-af0fafb8d2b0
    RG=dev-cloud-data-aks-rg
    AKS=dev-cloud-data-aks
    ;;
  stage)
    SUBSCRIPTION=********-7f22-4468-a34d-d61125f01797
    RG=stage-cloud-data-aks-rg
    AKS=stage-cloud-data-aks
    ;;
  prod)
    SUBSCRIPTION=38cb90f4-bf95-4b2c-b952-93f468d17aa0
    RG=prod-cloud-data-aks-rg
    AKS=prod-cloud-data-aks
    ;;
  mp)
    SUBSCRIPTION=2448ea42-bbc8-4a7c-a516-be9d8b350091
    RG=dev-mp-1
    AKS=dev-mp-aks-1
    ;;
  *)
    echo "Unsupported env"
    exit 1
    ;;
esac

az account set --subscription $SUBSCRIPTION

# az aks get-credentials \
#   --resource-group $RG \
#   --name $AKS \
#   --overwrite-existing \
#   --admin

kubectl config use-context $AKS-admin
kubectl cluster-info
