#!/bin/bash

BOOTSTRAP_SERVER="bootstrap.kafka.stage.payex.net:9094"
CLIENT_ID="automate-efecte"
CLUSTER_NAME="data-platform-cluster"
TOPIC_NAME="automate-efecte.entity-updated"
TOKEN_URL="https://id.stage.payex.com/swedbankpay/oauth-token"
CLIENT_SECRET_NAMESPACE="automate"
CLIENT_SECRET="automate-efecte-curity"
CA_CERT_URL="https://letsencrypt.org/certs/staging/letsencrypt-stg-root-x1.pem"

usage() {
    echo "Usage: $0 [-b BOOTSTRAP_SERVER] [-u CLIENT_ID] [-c CLUSTER_NAME] [-t TOPIC_NAME] [-r TOKEN_URL] [-s CLIENT_SECRET_NAMESPACE] [-e CLIENT_SECRET]" 1>&2
    exit 1
}

while getopts ":b:u:n:c:t:r:s:e:" opt; do
    case ${opt} in
        b) BOOTSTRAP_SERVER=$OPTARG ;;
        c) CLUSTER_NAME=$OPTARG ;;
        t) TOPIC_NAME=$OPTARG ;;
        r) TOKEN_URL=$OPTARG ;;
        u) CLIENT_ID=$OPTARG ;;
        s) CLIENT_SECRET_NAMESPACE=$OPTARG ;;
        e) CLIENT_SECRET=$OPTARG ;;
        *)
        usage
        ;;
    esac
done
shift $((OPTIND -1))

echo "BOOTSTRAP_SERVER: $BOOTSTRAP_SERVER"
echo "CLUSTER_NAME: $CLUSTER_NAME"
echo "TOPIC_NAME: $TOPIC_NAME"
echo "TOKEN_URL: $TOKEN_URL"
echo "CLIENT_ID: $CLIENT_ID"
echo "CLIENT_SECRET_NAMESPACE: $CLIENT_SECRET_NAMESPACE"
echo "CLIENT_SECRET: $CLIENT_SECRET"

rm kcat.config 2> /dev/null
rm testdata.txt 2> /dev/null
rm ca.crt 2> /dev/null

CLIENT_SECRET=$(kubectl get secrets -n $CLIENT_SECRET_NAMESPACE $CLIENT_SECRET -o yaml | yq '.data.clientSecret' | base64 -d)

curl -o ca.crt $CA_CERT_URL

cat <<EOF >kcat.config
bootstrap.servers=$BOOTSTRAP_SERVER
ssl.ca.location=ca.crt
security.protocol=sasl_ssl
sasl.mechanisms=OAUTHBEARER
sasl.oauthbearer.method=oidc
sasl.oauthbearer.client.id=$CLIENT_ID
sasl.oauthbearer.client.secret=$CLIENT_SECRET
sasl.oauthbearer.token.endpoint.url=$TOKEN_URL
EOF

cat <<EOF >testdata.txt
1:{"id":1,"name":"one"}
2:{"id":2,"name":"two"}
3:{"id":3,"name":"three"}
EOF

echo "> Happy kcatting! <"
echo
echo "   |\---/|"
echo "   | o_o |"
echo "    \_^_/"
echo
echo "---| Produce messages:"
echo "kcat -F kcat.config -P -t ${TOPIC_NAME} -K: -T -l testdata.txt"
echo
echo "---| Consume messages:"
echo "kcat -F kcat.config -C -t ${TOPIC_NAME} -o beginning -e -q -K:"
echo
echo "---| List topics:"
echo "kcat -F kcat.config -L"

echo
echo "Available topics:"
kcat -F kcat.config -L | grep -oE 'topic "[^"]+"' | sed 's/^topic "//;s/"$//'
