name: Helm Publish

on:
  push:
    tags:
      - '[0-9]+.[0-9]+.[0-9]+-*'
      - '[0-9]+.[0-9]+-*'
env:
  REGISTRY: ghcr.io/payex/helm-charts
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Dump GitHub context
        env:
          GITHUB_CONTEXT: ${{ toJson(github) }}
        run: echo "$GITHUB_CONTEXT"

      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Derive name and version from tag
        run: |
          CHART_NAME=$(echo $GITHUB_REF_NAME | cut -d '-' -f2-)
          CHART_VERSION=$(echo $GITHUB_REF_NAME | cut -d '-' -f-1)

          echo "CHART_NAME=${CHART_NAME}" >> $GITHUB_ENV
          echo "CHART_VERSION=${CHART_VERSION}" >> $GITHUB_ENV

      - name: Exit if CHART_NAME does not exist in repository
        run: |
          if [ -f $CHART_NAME/Chart.yaml ] 
          then
              echo "$CHART_NAME/Chart.yaml exists."
          else
              echo "$CHART_NAME/Chart.yaml does not exist."
              throw "Error: $CHART_NAME/Chart.yaml does not exist."
          fi

      - name: Replace version in Charts.yaml with version from tag
        run: |
          sed -i "s/^version: .*/version: \"${CHART_VERSION}\"/g" ${CHART_NAME}/Chart.yaml

      - name: Push file to repo
        run: |
          git config --global user.name 'Github Actions'
          git config --global user.email '<EMAIL>'
          git commit -am "release: automatic release of Helm chart"
          git push origin HEAD:main

      - name: Install Helm
        uses: Azure/setup-helm@v4
        with:
          version: v3.11.1

      - name: Helm Lint
        run: |
          helm lint ${CHART_NAME} --values ${CHART_NAME}/values.yaml

      - name: Helm Template
        run: |
          helm template ${CHART_NAME} --values ${CHART_NAME}/values.yaml

      - name: Helm Registry Login
        run: |
          echo ${{ secrets.GITHUB_TOKEN }} | helm registry login ${REGISTRY} --username ${{ github.repository_owner }} --password-stdin

      - name: Helm Package
        run: |
          helm package ${{ github.workspace }}/${CHART_NAME}

      - name: Helm Push
        run: |
          helm push ${CHART_NAME}-${CHART_VERSION}.tgz oci://${REGISTRY}

      - name: Helm Registry Logout
        run: |
          helm registry logout ${REGISTRY}

      - name: Send Slack notification
        id: slack
        uses: slackapi/slack-github-action@v1.27.0
        with:
          payload: |
            {
              "text": "GitHub Action build result: ${{ job.status }}\n${{ github.event.pull_request.html_url || github.event.head_commit.url }}",
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "GitHub Action build result for ${{ env.CHART_NAME }} ${{ env.CHART_VERSION }}: ${{ job.status }}\n${{ github.event.pull_request.html_url || github.event.head_commit.url }}"
                  }
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK
