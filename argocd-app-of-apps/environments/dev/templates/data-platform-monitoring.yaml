apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: data-platform-monitoring
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "30"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: data-platform
  source:
    path: data-platform-monitoring    
    repoURL: https://github.com/PayEx/helm-charts-data-platform
    targetRevision: HEAD
    helm:
      releaseName: data-platform-monitoring
      valueFiles:
      - values.yaml
  destination:
    server: "https://kubernetes.default.svc"
    namespace: monitoring
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: true

