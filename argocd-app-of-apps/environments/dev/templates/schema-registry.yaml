apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: schema-registry
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "30"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: data-platform
  sources:
  - repoURL: https://charts.bitnami.com/bitnami
    chart: schema-registry
    helm: 
      releaseName: schema-registry
      valueFiles:
      - "$values/external/schema-registry/values.yaml"
      - "$values/external/schema-registry/environments/dev/values.yaml"    
    targetRevision: 16.0.0
  - repoURL: https://github.com/PayEx/helm-charts-data-platform
    ref: values    
    targetRevision: HEAD    
  destination:
    server: "https://kubernetes.default.svc"
    namespace: kafka
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: true

