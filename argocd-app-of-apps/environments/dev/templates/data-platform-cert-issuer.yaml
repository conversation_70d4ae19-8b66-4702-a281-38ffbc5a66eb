apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: data-platform-cert-issuer
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "5"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: data-platform
  source:
    path: data-platform-cert-issuer    
    repoURL: https://github.com/PayEx/helm-charts-data-platform
    targetRevision: HEAD
    helm:
      releaseName: data-platform-cert-issuer
      valueFiles:
      - "values.yaml"
      - "environments/dev/values.yaml"
      parameters:
      - name: "acme.dns01.spec.azureDNS.managedIdentity.clientID"
        value: "{{ .Values.clientId }}"    
  destination:
    server: "https://kubernetes.default.svc"
    namespace: cert-manager
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: true

