apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: data-platform-entities
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "20"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: data-platform
  source:
    path: data-platform-entities    
    repoURL: https://github.com/PayEx/helm-charts-data-platform
    targetRevision: HEAD
    helm:
      releaseName: data-platform-entities
      valueFiles:
      - "values.yaml"
      - "environments/dev/values.yaml"    
  destination:
    server: "https://kubernetes.default.svc"
    namespace: kafka
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: true

