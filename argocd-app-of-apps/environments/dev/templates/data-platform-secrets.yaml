apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: data-platform-secrets
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "5"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: data-platform
  source:
    path: data-platform-secrets    
    repoURL: https://github.com/PayEx/helm-charts-data-platform
    targetRevision: HEAD
    helm:
      releaseName: data-platform-secrets
      valueFiles:
      - "values.yaml"
      - "environments/dev/values.yaml"
      parameters:
      - name: "tenantId"
        value: "{{ .Values.tenantId }}"
      - name: "keyvaultName"
        value: "{{ .Values.keyvaultName }}"
      - name: "clientId"
        value: "{{ .Values.clientId }}"    
  destination:
    server: "https://kubernetes.default.svc"
    namespace: kafka
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: true

