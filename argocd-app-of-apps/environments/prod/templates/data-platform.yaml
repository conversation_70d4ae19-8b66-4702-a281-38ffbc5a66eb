apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: data-platform
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:  
  description: Data Platform Project
  sourceRepos:
  - '*'
  destinations:
  - namespace: kafka
    server: https://kubernetes.default.svc
  - namespace: monitoring
    server: https://kubernetes.default.svc
  - namespace: cert-manager
    server: https://kubernetes.default.svc
  - namespace: ingress-nginx
    server: https://kubernetes.default.svc
  - namespace: default
    server: https://kubernetes.default.svc
  - namespace: kube-system
    server: https://kubernetes.default.svc
  clusterResourceWhitelist:
  - group: '*'
    kind: '*'

