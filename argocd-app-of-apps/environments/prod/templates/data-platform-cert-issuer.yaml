apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: data-platform-cert-issuer
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "5"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: data-platform
  source:
    chart: data-platform-cert-issuer    
    repoURL: ghcr.io/payex/helm-charts
    targetRevision: 0.1.2
    helm:
      releaseName: data-platform-cert-issuer
      valueFiles:
      - "values.yaml"
      - "environments/prod/values.yaml"
      parameters:
      - name: "acme.dns01.spec.azureDNS.managedIdentity.clientID"
        value: "{{ .Values.clientId }}"    
  destination:
    server: "https://kubernetes.default.svc"
    namespace: cert-manager
  # syncPolicy:
  #   automated:
  #     prune: true
  #     selfHeal: true
  #     allowEmpty: true

