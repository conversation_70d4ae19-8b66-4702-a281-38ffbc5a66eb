apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: data-platform-entities
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "20"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: data-platform
  source:
    chart: data-platform-entities    
    repoURL: ghcr.io/payex/helm-charts
    targetRevision: 1.8.5
    helm:
      releaseName: data-platform-entities
      valueFiles:
      - "values-topics.yaml"
      - "values-users.yaml"
      - "teams/mx/values-topics.yaml"
      - "teams/mx/values-users.yaml"
      - "environments/prod/values.yaml"    
      - "values.yaml"    
  destination:
    server: "https://kubernetes.default.svc"
    namespace: kafka

