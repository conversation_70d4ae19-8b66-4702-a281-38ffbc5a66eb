apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: promtail
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: data-platform
  sources:
  - repoURL: https://grafana.github.io/helm-charts
    chart: promtail
    helm: 
      releaseName: promtail
      valueFiles:
      - "$values/external/promtail/values.yaml"
      - "$values/external/promtail/environments/prod/values.yaml"
    targetRevision: 6.17.0
  - repoURL: https://github.com/PayEx/helm-charts-data-platform
    ref: values    
    targetRevision: HEAD    
  destination:
    server: "https://kubernetes.default.svc"
    namespace: monitoring
  # syncPolicy:
  #   automated:
  #     prune: true
  #     selfHeal: true
  #     allowEmpty: true

