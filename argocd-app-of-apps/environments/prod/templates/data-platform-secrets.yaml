apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: data-platform-secrets
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "5"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: data-platform
  source:
    chart: data-platform-secrets    
    repoURL: ghcr.io/payex/helm-charts
    targetRevision: 1.7.8
    helm:
      releaseName: data-platform-secrets
      valueFiles:
      - "values.yaml"
      - "environments/prod/values.yaml"
      parameters:
      - name: "tenantId"
        value: "{{ .Values.tenantId }}"
      - name: "keyvaultName"
        value: "{{ .Values.keyvaultName }}"
      - name: "clientId"
        value: "{{ .Values.clientId }}"    
      - name: "affinity.enabled"
        value: "false"
  destination:
    server: "https://kubernetes.default.svc"
    namespace: kafka
  # syncPolicy:
  #   automated:
  #     prune: true
  #     selfHeal: true
  #     allowEmpty: true

