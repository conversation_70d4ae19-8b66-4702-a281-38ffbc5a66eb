apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: bims-elsa-transformer
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "100"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: automate
  source:
    chart: bims-elsa-transformer
    repoURL: ghcr.io/payex/helm-charts
    targetRevision: 0.0.4
    helm:
      releaseName: bims-elsa-transformer
      valueFiles:
      - "values.yaml"
      - "environments/prod/values.yaml"
      parameters:
      - name: "autoscaling.enabled"
        value: "false"
      - name: "ingress.host"
        value: "bims-elsa-transformer.{{ .Values.loadBalancerIP }}.nip.io"
      - name: affinity.nodeAffinity.nodeSelectorTerms.values[0]
        value: "general"
      - name: affinity.nodeAffinity.nodeSelectorTerms.values[1]
        value: "generaltemp"
  destination:
    server: "https://kubernetes.default.svc"
    namespace: automate
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: true

