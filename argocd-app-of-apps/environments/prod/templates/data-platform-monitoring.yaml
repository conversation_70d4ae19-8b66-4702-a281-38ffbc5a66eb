apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: data-platform-monitoring
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "30"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: data-platform
  source:
    chart: data-platform-monitoring    
    repoURL: ghcr.io/payex/helm-charts
    targetRevision: 2.3.7
    helm:
      releaseName: data-platform-monitoring
      valueFiles:
      - "values.yaml"    
  destination:
    server: "https://kubernetes.default.svc"
    namespace: monitoring
  # syncPolicy:
  #   automated:
  #     prune: true
  #     selfHeal: true
  #     allowEmpty: true

