apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: data-platform-cluster
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "10"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: data-platform
  source:
    chart: data-platform-cluster    
    repoURL: ghcr.io/payex/helm-charts
    targetRevision: 2.5.0
    helm:
      releaseName: data-platform-cluster
      valueFiles:
      - "values.yaml"
      - "environments/prod/values.yaml"    
      parameters:
      - name: "kafka.config.deleteTopicEnabled"
        value: "true"
      - name: "kafka.version"
        value: "3.8.0"
  destination:
    server: "https://kubernetes.default.svc"
    namespace: kafka
  # syncPolicy:
  #   automated:
  #     prune: true
  #     selfHeal: true
  #     allowEmpty: true

