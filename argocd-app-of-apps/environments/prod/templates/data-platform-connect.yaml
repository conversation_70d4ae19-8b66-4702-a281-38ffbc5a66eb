apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: data-platform-connect
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "30"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: data-platform
  source:
    chart: data-platform-connect
    repoURL: ghcr.io/payex/helm-charts
    targetRevision: 2.0.13
    helm:
      releaseName: data-platform-connect
      valueFiles:
      - "values.yaml"
      - "environments/prod/values.yaml"
      parameters:
      - name: "kafkaConnect.replicas"
        value: "4"
      - name: "kafkaConnect.resources.requests.memory"
        value: "6Gi"
      - name: "kafkaConnect.resources.limits.memory"
        value: "6Gi"
      - name: "kafkaConnect.version"
        value: "3.8.0"
  destination:
    server: "https://kubernetes.default.svc"
    namespace: kafka
  # syncPolicy:
  #   automated:
  #     prune: true
  #     selfHeal: true
  #     allowEmpty: true

