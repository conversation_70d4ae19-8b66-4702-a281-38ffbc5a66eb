apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: automate-arrangement-orchestrator
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "100"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: automate
  source:
    chart: automate-arrangement-orchestrator
    repoURL: ghcr.io/payex/helm-charts
    targetRevision: 1.0.17
    helm:
      releaseName: automate-arrangement-orchestrator
      valueFiles:
      - "values.yaml"
      - "environments/prod/values.yaml"
      parameters:
      - name: "autoscaling.enabled"
        value: "false"
      - name: "ingress.clusterIssuer"
        value: "cloud-data-clusterissuer"
      - name: "ingress.host"
        value: "automate-arrangement-orchestrator.{{ .Values.loadBalancerIP }}.nip.io"
      - name: affinity.nodeAffinity.nodeSelectorTerms.values[0]
        value: "general"
      - name: affinity.nodeAffinity.nodeSelectorTerms.values[1]
        value: "generaltemp"
  destination:
    server: "https://kubernetes.default.svc"
    namespace: automate
  # syncPolicy:
  #   automated:
  #     prune: true
  #     selfHeal: true
  #     allowEmpty: true

