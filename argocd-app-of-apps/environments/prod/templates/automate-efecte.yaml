apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: automate-efecte
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "100"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: automate
  source:
    chart: automate-efecte    
    repoURL: ghcr.io/payex/helm-charts
    targetRevision: 1.7.32
    helm:
      releaseName: automate-efecte
      valueFiles:
      - "values.yaml"
      - "environments/prod/values.yaml"
      parameters:
      - name: "ingress.clusterIssuer"
        value: "cloud-data-clusterissuer"
      - name: "autoscaling.enabled"
        value: "false"    
      - name: affinity.nodeAffinity.nodeSelectorTerms.values[0]
        value: "general"
      - name: affinity.nodeAffinity.nodeSelectorTerms.values[1]
        value: "generaltemp"
  destination:
    server: "https://kubernetes.default.svc"
    namespace: automate
  # syncPolicy:
  #   automated:
  #     prune: true
  #     selfHeal: true
  #     allowEmpty: true

