apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: automate-secrets
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: automate
  source:
    path: automate-secrets    
    repoURL: https://github.com/PayEx/helm-charts-data-platform
    targetRevision: HEAD
    helm:
      releaseName: automate-secrets
      parameters:
      - name: "tenantId"
        value: "{{ .Values.tenantId }}"
      - name: "keyvaultName"
        value: "{{ .Values.keyvaultName }}"
      - name: "clientId"
        value: "{{ .Values.clientId }}"    
  destination:
    server: "https://kubernetes.default.svc"
    namespace: automate
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: true

