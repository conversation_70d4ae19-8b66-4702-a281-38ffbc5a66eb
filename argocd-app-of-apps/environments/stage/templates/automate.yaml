apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: automate
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:  
  description: Automate
  sourceRepos:
  - '*'
  destinations:
  - namespace: monitoring
    server: https://kubernetes.default.svc
  - namespace: automate
    server: https://kubernetes.default.svc
  - namespace: kafka
    server: https://kubernetes.default.svc
  clusterResourceWhitelist:
  - group: '*'
    kind: '*'

