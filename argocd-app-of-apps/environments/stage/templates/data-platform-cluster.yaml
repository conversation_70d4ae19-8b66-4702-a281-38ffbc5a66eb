apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: data-platform-cluster
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "10"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: data-platform
  source:
    path: data-platform-cluster    
    repoURL: https://github.com/PayEx/helm-charts-data-platform
    targetRevision: HEAD
    helm:
      releaseName: data-platform-cluster
      parameters:
      - name: "watchNamespaces"
        value: "kafka,kafka-sandbox"
      valueFiles:
      - "values.yaml"
      - "environments/stage/values.yaml"    
  destination:
    server: "https://kubernetes.default.svc"
    namespace: kafka
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: true

