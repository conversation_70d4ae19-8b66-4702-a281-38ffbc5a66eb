apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: rabbitmq
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "10"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: automate
  sources:
  - repoURL: https://charts.bitnami.com/bitnami
    chart: rabbitmq
    helm: 
      releaseName: rabbitmq
      valueFiles:
      - "$values/external/rabbitmq/values.yaml"
      - "$values/external/rabbitmq/environments/stage/values.yaml"
      parameters:
      - name: "ingress.hostname"
        value: "rabbitmq.{{ .Values.loadBalancerIP }}.nip.io"    
    targetRevision: 14.3.1
  - repoURL: https://github.com/PayEx/helm-charts-data-platform
    ref: values    
    targetRevision: HEAD    
  destination:
    server: "https://kubernetes.default.svc"
    namespace: automate
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: true

