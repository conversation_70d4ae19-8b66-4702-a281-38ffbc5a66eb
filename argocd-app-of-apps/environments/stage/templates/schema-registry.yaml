apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: schema-registry
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "30"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: data-platform
  sources:
  - repoURL: registry-1.docker.io/bitnamicharts
    chart: schema-registry
    helm: 
      releaseName: schema-registry
      valueFiles:
      - "$values/external/schema-registry/values.yaml"
      - "$values/external/schema-registry/environments/stage/values.yaml"    
    targetRevision: 23.1.2
  - repoURL: https://github.com/PayEx/helm-charts-data-platform
    ref: values    
    targetRevision: HEAD    
  destination:
    server: "https://kubernetes.default.svc"
    namespace: kafka
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: true

