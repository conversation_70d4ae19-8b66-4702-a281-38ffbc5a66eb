apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: kafka-ui
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "40"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: data-platform
  sources:
  - repoURL: https://provectus.github.io/kafka-ui-charts
    chart: kafka-ui
    helm: 
      releaseName: kafka-ui
      valueFiles:
      - "$values/external/kafka-ui/values.yaml"
      - "$values/external/kafka-ui/environments/stage/values.yaml"
    targetRevision: 0.7.6
  - repoURL: https://github.com/PayEx/helm-charts-data-platform
    ref: values    
    targetRevision: HEAD    
  destination:
    server: "https://kubernetes.default.svc"
    namespace: kafka
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: true

