apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: flink-kubernetes-operator
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: data-platform
  sources:
    - repoURL: ghcr.io/apache/flink-kubernetes-operator
      chart: flink-kubernetes-operator
      helm:
        releaseName: flink-kubernetes-operator
        valueFiles:
          - "$values/external/flink-kubernetes-operator/values.yaml"
          - "$values/external/flink-kubernetes-operator/environments/stage/values.yaml"
      targetRevision: 1.12.1
    - repoURL: https://github.com/PayEx/helm-charts-data-platform
      ref: values
      targetRevision: HEAD
  destination:
    server: "https://kubernetes.default.svc"
    namespace: kafka
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: true
