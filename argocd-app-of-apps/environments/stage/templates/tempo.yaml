apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: tempo
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: data-platform
  sources:
  - repoURL: https://grafana.github.io/helm-charts
    chart: tempo
    helm: 
      releaseName: tempo
      valueFiles:
      - "$values/external/tempo/values.yaml"
      - "$values/external/tempo/environments/stage/values.yaml"
    targetRevision: 1.23.1
  - repoURL: https://github.com/PayEx/helm-charts-data-platform
    ref: values    
    targetRevision: HEAD    
  destination:
    server: "https://kubernetes.default.svc"
    namespace: monitoring
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: true

