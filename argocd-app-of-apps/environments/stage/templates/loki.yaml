apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: loki
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: data-platform
  sources:
  - repoURL: https://grafana.github.io/helm-charts
    chart: loki
    helm: 
      releaseName: loki
      valueFiles:
      - "$values/external/loki/values.yaml"
      - "$values/external/loki/environments/stage/values.yaml"
      parameters:
      - name: "serviceAccount.annotations.azure\\.workload\\.identity/client-id"
        value: "c4855e6b-8985-4246-93a5-3ad2df68fa25"
      - name: "serviceAccount.annotations.azure\\.workload\\.identity/tenant-id"
        value: "{{ .Values.tenantId }}"
    targetRevision: 6.30.1
  - repoURL: https://github.com/PayEx/helm-charts-data-platform
    ref: values    
    targetRevision: HEAD    
  destination:
    server: "https://kubernetes.default.svc"
    namespace: monitoring
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: true

