apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: external-dns
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "20"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: data-platform
  sources:
  - repoURL: registry-1.docker.io/bitnamicharts
    chart: external-dns
    helm: 
      releaseName: external-dns
      valueFiles:
      - "$values/external/external-dns/values.yaml"
      - "$values/external/external-dns/environments/stage/values.yaml"
      parameters:
      - name: "serviceAccount.annotations.azure\\.workload\\.identity/client-id"
        value: "{{ .Values.clientId }}"
    targetRevision: 8.7.1
  - repoURL: https://github.com/PayEx/helm-charts-data-platform
    ref: values    
    targetRevision: HEAD    
  destination:
    server: "https://kubernetes.default.svc"
    namespace: cert-manager
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: true

