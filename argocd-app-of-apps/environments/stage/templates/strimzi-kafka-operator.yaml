apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: strimzi-kafka-operator
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: data-platform
  sources:
  - repoURL: https://strimzi.io/charts/
    chart: strimzi-kafka-operator
    helm: 
      releaseName: strimzi-kafka-operator
      valueFiles:
      - "$values/external/strimzi-kafka-operator/values.yaml"    
      - "$values/external/strimzi-kafka-operator/environments/stage/values.yaml"    
    targetRevision: 0.45.0
  - repoURL: https://github.com/PayEx/helm-charts-data-platform
    ref: values    
    targetRevision: HEAD    
  destination:
    server: "https://kubernetes.default.svc"
    namespace: kafka
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: true
