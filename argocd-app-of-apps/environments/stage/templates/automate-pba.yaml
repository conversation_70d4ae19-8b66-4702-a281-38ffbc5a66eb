apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: automate-pba
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "100"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: automate
  source:
    chart: automate-pba    
    repoURL: ghcr.io/payex/helm-charts
    targetRevision: 1.1.24
    helm:
      releaseName: automate-pba
      valueFiles:
      - "values.yaml"
      - "environments/stage/values.yaml"
      parameters:
      - name: "automate-pba.automateSignup.enabled"
        value: "true"
      - name: "autoscaling.enabled"
        value: "false"
      - name: "ingress.clusterIssuer"
        value: "cloud-data-clusterissuer"
      - name: "ingress.host"
        value: "automate-pba.{{ .Values.loadBalancerIP }}.nip.io"
      - name: affinity.nodeAffinity.nodeSelectorTerms.values[0]
        value: "general"
      - name: affinity.nodeAffinity.nodeSelectorTerms.values[1]
        value: "generaltemp"
  destination:
    server: "https://kubernetes.default.svc"
    namespace: automate
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: true

