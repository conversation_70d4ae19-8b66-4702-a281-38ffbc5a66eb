apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: apicurio-registry-operator
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "0"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: data-platform
  source:
    repoURL: https://github.com/PayEx/helm-charts-data-platform
    path: external/apicurio-registry-operator
    targetRevision: HEAD
  destination:
    server: "https://kubernetes.default.svc"
    namespace: kafka
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: true
