apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: auth-stats
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "100"
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  project: automate
  source:
    chart: auth-stats
    repoURL: ghcr.io/payex/helm-charts
    targetRevision: 1.0.4
    helm:
      releaseName: auth-stats
      valueFiles:
      - "values.yaml"
      - "environments/stage/values.yaml"
      parameters:
      - name: "autoscaling.enabled"
        value: "false"
      - name: "replicaCount"
        value: "1"
      - name: "ingress.host"
        value: "auth-stats.{{ .Values.loadBalancerIP }}.nip.io"
      - name: affinity.nodeAffinity.nodeSelectorTerms.values[0]
        value: "general"
      - name: affinity.nodeAffinity.nodeSelectorTerms.values[1]
        value: "generaltemp"
  destination:
    server: "https://kubernetes.default.svc"
    namespace: automate
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: true

