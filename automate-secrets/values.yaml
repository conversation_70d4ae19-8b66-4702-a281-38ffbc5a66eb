tenantId: TENANT_ID
keyvaultName: KEY_VAULT_NAME
clientId: CLIENT_ID

affinity:
  enabled: true

namespaces:
  - name: automate
    secretObjects:
      - secretName: insight-image-pull-secret
        type: kubernetes.io/dockerconfigjson
        data:
          - objectName: "insight-image-pull-secret"
            key: .dockerconfigjson
      - secretName: automate-efecte-curity
        type: Opaque
        data:
          - objectName: "automate-efecte-curity"
            key: clientSecret
      - secretName: automate-pba-curity
        type: Opaque
        data:
          - objectName: "automate-pba-curity"
            key: clientSecret
      - secretName: automate-pos-curity
        type: Opaque
        data:
          - objectName: "automate-pos-curity"
            key: clientSecret
      - secretName: automate-visma-curity
        type: Opaque
        data:
          - objectName: "automate-visma-curity"
            key: clientSecret
      - secretName: automate-signup-curity
        type: Opaque
        data:
          - objectName: "automate-signup-curity"
            key: clientSecret
      - secretName: automate-tms-curity
        type: Opaque
        data:
          - objectName: "automate-tms-curity"
            key: clientSecret
      - secretName: automate-softpos-curity
        type: Opaque
        data:
          - objectName: "automate-softpos-curity"
            key: clientSecret
      - secretName: automate-softpos-api-key
        type: Opaque
        data:
          - objectName: "automate-softpos-api-key"
            key: apiKey
      - secretName: automate-softpos-linkmobility-client-id
        type: Opaque
        data:
          - objectName: "automate-softpos-linkmobility-client-id"
            key: clientId
      - secretName: automate-softpos-linkmobility-client-secret
        type: Opaque
        data:
          - objectName: "automate-softpos-linkmobility-client-secret"
            key: clientSecret
      - secretName: automate-softpos-emarketeer-api-key
        type: Opaque
        data:
          - objectName: "automate-softpos-emarketeer-api-key"
            key: apiKey
      - secretName: automate-softpos-merchant-portal-api-curity-token
        type: Opaque
        data:
          - objectName: "automate-softpos-merchant-portal-api-curity-token"
            key: clientSecret
      - secretName: authorizations-transformer-curity
        type: Opaque
        data:
          - objectName: "authorizations-transformer-curity"
            key: clientSecret
      - secretName: automate-arrangement-orchestrator-curity
        type: Opaque
        data:
          - objectName: "automate-arrangement-orchestrator-curity"
            key: clientSecret
      - secretName: bims-mdd-transformer-curity
        type: Opaque
        data:
          - objectName: "bims-mdd-transformer-curity"
            key: clientSecret
      - secretName: bims-add-transformer-curity
        type: Opaque
        data:
          - objectName: "bims-add-transformer-curity"
            key: clientSecret
      - secretName: bims-pdd-transformer-curity
        type: Opaque
        data:
          - objectName: "bims-pdd-transformer-curity"
            key: clientSecret
      - secretName: bims-bundles-transformer-curity
        type: Opaque
        data:
          - objectName: "bims-bundles-transformer-curity"
            key: clientSecret
      - secretName: bims-exceptions-transformer-curity
        type: Opaque
        data:
          - objectName: "bims-exceptions-transformer-curity"
            key: clientSecret
      - secretName: bims-elsa-transformer-curity
        type: Opaque
        data:
          - objectName: "bims-elsa-transformer-curity"
            key: clientSecret
      - secretName: slack-notifier-curity
        type: Opaque
        data:
          - objectName: "slack-notifier-curity"
            key: clientSecret
      - secretName: slack-webhook-url
        type: Opaque
        data:
          - objectName: "slack-webhook-url"
            key: clientSecret
      - secretName: workload-identity-client-id
        type: Opaque
        data:
          - objectName: "workload-identity-client-id"
            key: clientSecret
      - secretName: auth-stats-curity
        type: Opaque
        data:
          - objectName: "auth-stats-curity"
            key: clientSecret
      - secretName: automate-postgresql-initdb
        type: Opaque
        data:
          - objectName: "automate-postgresql-initdb"
            key: automate-postgresql-initdb.sql
      - secretName: automate-postgresql-password
        type: Opaque
        data:
          - objectName: "automate-postgresql-password"
            key: postgres-password
      - secretName: auth-stats-user-password
        type: Opaque
        data:
          - objectName: "auth-stats-user-password"
            key: password
      - secretName: auth-stats-blob-container-url
        type: Opaque
        data:
          - objectName: "auth-stats-blob-container-url"
            key: urlString
      - secretName: auth-stats-api-user-password
        type: Opaque
        data:
          - objectName: "auth-stats-api-user-password"
            key: password
      - secretName: automate-efecte-password
        type: Opaque
        data:
          - objectName: "automate-efecte-password"
            key: password
      - secretName: automate-arrangement-orchestrator-password
        type: Opaque
        data:
          - objectName: "automate-arrangement-orchestrator-password"
            key: password
      - secretName: efecte-credentials
        type: Opaque
        data:
          - objectName: "efecte-username"
            key: username
          - objectName: "efecte-password"
            key: password
      - secretName: rabbitmq-credentials
        type: Opaque
        data:
          - objectName: "rabbitmq-username"
            key: username
          - objectName: "rabbitmq-password"
            key: password
      - secretName: rabbitmq-load-definition
        type: Opaque
        data:
          - objectName: "rabbitmq-load-definition"
            key: load_definition.json
      - secretName: rabbitmq-erlang-cookie
        type: Opaque
        data:
          - objectName: "rabbitmq-erlang-cookie"
            key: rabbitmq-erlang-cookie
      - secretName: base24-authorizations-raw-consumer-sas
        type: Opaque
        data:
          - objectName: "base24-authorizations-raw-consumer-sas"
            key: token
      - secretName: bims-mdd-raw-consumer-sas
        type: Opaque
        data:
          - objectName: "bims-mdd-raw-consumer-sas"
            key: token
      - secretName: bims-add-raw-consumer-sas
        type: Opaque
        data:
          - objectName: "bims-add-raw-consumer-sas"
            key: token
      - secretName: bims-pdd-raw-consumer-sas
        type: Opaque
        data:
          - objectName: "bims-pdd-raw-consumer-sas"
            key: token
      - secretName: bims-bundles-raw-consumer-sas
        type: Opaque
        data:
          - objectName: "bims-bundles-raw-consumer-sas"
            key: token
      - secretName: bims-exceptions-raw-consumer-sas
        type: Opaque
        data:
          - objectName: "bims-exceptions-raw-consumer-sas"
            key: token
      - secretName: bims-elsa-raw-consumer-sas
        type: Opaque
        data:
          - objectName: "bims-elsa-raw-consumer-sas"
            key: token
    parameters:
      usePodIdentity: "false"
      useVMManagedIdentity: "false"
      objects: |
        array:
          - |
            objectName: "insight-image-pull-secret"
            objectType: secret
          - |
            objectName: "automate-efecte-curity"
            objectType: secret
          - |
            objectName: "automate-pba-curity"
            objectType: secret
          - |
            objectName: "automate-pos-curity"
            objectType: secret
          - |
            objectName: "automate-visma-curity"
            objectType: secret
          - |
            objectName: "automate-signup-curity"
            objectType: secret
          - |
            objectName: "automate-tms-curity"
            objectType: secret
          - |
            objectName: "automate-softpos-curity"
            objectType: secret
          - |
            objectName: "automate-softpos-api-key"
            objectType: secret
          - |
            objectName: "automate-softpos-linkmobility-client-id"
            objectType: secret
          - |
            objectName: "automate-softpos-linkmobility-client-secret"
            objectType: secret
          - |
            objectName: "automate-softpos-emarketeer-api-key"
            objectType: secret
          - |
            objectName: "automate-softpos-merchant-portal-api-curity-token"
            objectType: secret
          - |
            objectName: "authorizations-transformer-curity"
            objectType: secret
          - |
            objectName: "bims-mdd-transformer-curity"
            objectType: secret
          - |
            objectName: "bims-add-transformer-curity"
            objectType: secret
          - |
            objectName: "bims-pdd-transformer-curity"
            objectType: secret
          - |
            objectName: "bims-bundles-transformer-curity"
            objectType: secret
          - |
            objectName: "bims-elsa-transformer-curity"
            objectType: secret
          - |
            objectName: "bims-exceptions-transformer-curity"
            objectType: secret
          - |
            objectName: "auth-stats-curity"
            objectType: secret
          - |
            objectName: "automate-arrangement-orchestrator-curity"
            objectType: secret
          - |
            objectName: "slack-notifier-curity"
            objectType: secret
          - |
            objectName: "slack-webhook-url"
            objectType: secret
          - |
            objectName: "workload-identity-client-id"
            objectType: secret
          - |
            objectName: "automate-efecte-password"
            objectType: secret
          - |
            objectName: "automate-arrangement-orchestrator-password"
            objectType: secret
          - |
            objectName: "automate-postgresql-initdb"
            objectType: secret
          - |
            objectName: "auth-stats-user-password"
            objectType: secret
          - |
            objectName: "auth-stats-blob-container-url"
            objectType: secret
          - |
            objectName: "auth-stats-api-user-password"
            objectType: secret
          - |
            objectName: "automate-postgresql-password"
            objectType: secret
          - |
            objectName: "efecte-username"
            objectType: secret
          - |
            objectName: "efecte-password"
            objectType: secret
          - |
            objectName: "rabbitmq-load-definition"
            objectType: secret
          - |
            objectName: "rabbitmq-username"
            objectType: secret
          - |
            objectName: "rabbitmq-password"
            objectType: secret
          - |
            objectName: "rabbitmq-erlang-cookie"
            objectType: secret
          - |
            objectName: "base24-authorizations-raw-consumer-sas"
            objectType: secret
          - |
            objectName: "bims-mdd-raw-consumer-sas"
            objectType: secret
          - |
            objectName: "bims-add-raw-consumer-sas"
            objectType: secret
          - |
            objectName: "bims-pdd-raw-consumer-sas"
            objectType: secret
          - |
            objectName: "bims-bundles-raw-consumer-sas"
            objectType: secret
          - |
            objectName: "bims-elsa-raw-consumer-sas"
            objectType: secret
          - |
            objectName: "bims-exceptions-raw-consumer-sas"
            objectType: secret
