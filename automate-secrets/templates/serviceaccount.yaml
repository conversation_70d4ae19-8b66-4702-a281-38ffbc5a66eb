{{- range .Values.namespaces }}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ $.Values.serviceAccountName | default "workload-identity-sa" }}
  namespace: {{ required "Must set namespace" .name }}
  labels:
    azure.workload.identity/use: "true"
  annotations:
    azure.workload.identity/client-id: {{ $.Values.clientId }}
    azure.workload.identity/tenant-id: {{ $.Values.tenantId }}
{{- end }}
