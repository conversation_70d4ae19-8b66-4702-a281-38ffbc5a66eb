{{- range .Values.namespaces }}
---
apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: {{ .name }}-secret-provider
  namespace: {{ .name }}
spec:
  provider: azure
{{- with .secretObjects }}
  secretObjects:
{{ toYaml . | indent 4 }}
{{- end }}
{{- with .parameters }}
  parameters:
    clientID: {{ $.Values.clientId }}
    keyvaultName: {{ $.Values.keyvaultName }}
    tenantId: {{ $.Values.tenantId }}
{{ toYaml . | indent 4 }}
{{- end }}

{{- end }}
