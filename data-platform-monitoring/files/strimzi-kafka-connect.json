{"__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "7.4.5"}, {"type": "panel", "id": "stat", "name": "Stat"}, {"type": "datasource", "id": "prometheus", "name": "Prometheus"}, {"type": "panel", "id": "timeseries", "name": "Timeseries"}], "annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 18, "links": [], "panels": [{"collapsed": false, "datasource": "${DS_PROMETHEUS}", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 46, "panels": [], "targets": [{"datasource": "${DS_PROMETHEUS}", "refId": "A"}], "title": "Overview", "type": "row"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 1}, "id": 26, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_connect_worker_connector_count{strimzi_io_kind=~\"KafkaConnect.*\",strimzi_io_cluster=\"$strimzi_connect_cluster_name\"})", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 40}], "title": "Number of Connectors", "type": "stat"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 1}, "id": 27, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_connect_worker_task_count{strimzi_io_kind=~\"KafkaConnect.*\",strimzi_io_cluster=\"$strimzi_connect_cluster_name\"})", "format": "time_series", "intervalFactor": 2, "refId": "A", "step": 40}], "title": "Number of Tasks", "type": "stat"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 1}, "id": 36, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_consumer_connection_count{strimzi_io_kind=~\"KafkaConnect.*\",strimzi_io_cluster=\"$strimzi_connect_cluster_name\"})", "hide": false, "interval": "", "legendFormat": "#consumer connections", "refId": "A"}, {"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_producer_connection_count{strimzi_io_kind=~\"KafkaConnect.*\",strimzi_io_cluster=\"$strimzi_connect_cluster_name\"})", "interval": "", "legendFormat": "#producer connections", "refId": "B"}, {"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_admin_client_connection_count{strimzi_io_kind=~\"KafkaConnect.*\",strimzi_io_cluster=\"$strimzi_connect_cluster_name\"})", "interval": "", "legendFormat": "#admin connections", "refId": "C"}], "title": "Connection Count", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "description": "Bytes per second outgoing to Kafka from Connect worker. Includes data to internal topics.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "Bytes/sec", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "Bps"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 8}, "id": 29, "options": {"legend": {"calcs": ["mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(rate(kafka_producer_outgoing_byte_total{strimzi_io_kind=~\"KafkaConnect.*\",strimzi_io_cluster=\"$strimzi_connect_cluster_name\"}[5m])) by (kubernetes_pod_name)", "interval": "", "legendFormat": "{{kuberne<PERSON>_pod_name}}", "refId": "A"}], "title": "(Source/Producers) Outgoing Bytes per Second", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "description": "Bytes per second incoming to Kafka Connect worker from Kafka. Includes data from internal topics.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "Bytes/sec", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "Bps"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 8}, "id": 28, "options": {"legend": {"calcs": ["mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(rate(kafka_consumer_incoming_byte_total{strimzi_io_kind=~\"KafkaConnect.*\",strimzi_io_cluster=\"$strimzi_connect_cluster_name\"}[5m])) by (kubernetes_pod_name)", "interval": "", "legendFormat": "{{kuberne<PERSON>_pod_name}}", "refId": "A"}], "title": "(Sink/Consumers) Incoming Bytes per Second", "type": "timeseries"}, {"collapsed": false, "datasource": "${DS_PROMETHEUS}", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 15}, "id": 50, "panels": [], "targets": [{"datasource": "${DS_PROMETHEUS}", "refId": "A"}], "title": "Workers", "type": "row"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 12, "x": 0, "y": 16}, "id": 52, "options": {"displayMode": "gradient", "maxVizHeight": 300, "minVizHeight": 16, "minVizWidth": 8, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "auto", "valueMode": "color"}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_connect_coordinator_assigned_connectors{strimzi_io_kind=~\"KafkaConnect.*\",strimzi_io_cluster=\"$strimzi_connect_cluster_name\"}) by (kubernetes_pod_name)", "interval": "", "legendFormat": "{{kuberne<PERSON>_pod_name}}", "refId": "A"}], "title": "Assigned Connectors by Worker", "type": "bargauge"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "id": 56, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_connect_worker_rebalance_rebalancing{strimzi_io_kind=~\"KafkaConnect.*\",strimzi_io_cluster=\"$strimzi_connect_cluster_name\"})", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Rebalancing", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 12, "x": 0, "y": 20}, "id": 54, "options": {"displayMode": "gradient", "maxVizHeight": 300, "minVizHeight": 16, "minVizWidth": 8, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "auto", "valueMode": "color"}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_connect_coordinator_assigned_tasks{strimzi_io_kind=~\"KafkaConnect.*\",strimzi_io_cluster=\"$strimzi_connect_cluster_name\"}) by (kubernetes_pod_name)", "interval": "", "legendFormat": "{{kuberne<PERSON>_pod_name}}", "refId": "A"}], "title": "Assigned Tasks per Worker", "type": "bargauge"}, {"collapsed": false, "datasource": "${DS_PROMETHEUS}", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 24}, "id": 48, "panels": [], "targets": [{"datasource": "${DS_PROMETHEUS}", "refId": "A"}], "title": "Connectors and Tasks", "type": "row"}, {"columns": [], "datasource": "${DS_PROMETHEUS}", "description": "Connectors that have one or more tasks that are not in a running state, i.e. the state is one of paused, failed, unassigned or destroyed.", "fieldConfig": {"defaults": {"custom": {"cellOptions": {"type": "auto"}, "filterable": false, "inspect": false}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "fontSize": "100%", "gridPos": {"h": 9, "w": 12, "x": 0, "y": 25}, "id": 34, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "frameIndex": 1, "showHeader": true}, "pluginVersion": "7.4.5", "showHeader": true, "sort": {"col": 0, "desc": true}, "styles": [{"alias": "Time", "align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "date"}, {"alias": "Tasks", "align": "auto", "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "mappingType": 1, "pattern": "Value", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "Connector", "align": "auto", "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 0, "pattern": "Metric", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_connect_worker_connector_paused_task_count{namespace=\"$kubernetes_namespace\",strimzi_io_kind=~\"KafkaConnect.*\",strimzi_io_cluster=\"$strimzi_connect_cluster_name\"}) by (connector) != 0", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}, {"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_connect_worker_connector_failed_task_count{namespace=\"$kubernetes_namespace\",strimzi_io_kind=~\"KafkaConnect.*\",strimzi_io_cluster=\"$strimzi_connect_cluster_name\"}) by (connector) != 0", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "B"}, {"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_connect_worker_connector_unassigned_task_count{namespace=\"$kubernetes_namespace\",strimzi_io_kind=~\"KafkaConnect.*\",strimzi_io_cluster=\"$strimzi_connect_cluster_name\"}) by (connector) != 0", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "C"}, {"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_connect_worker_connector_destroyed_task_count{namespace=\"$kubernetes_namespace\",strimzi_io_kind=~\"KafkaConnect.*\",strimzi_io_cluster=\"$strimzi_connect_cluster_name\"}) by (connector) != 0", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "D"}], "title": "Connector Tasks with Issues", "transform": "timeseries_to_rows", "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {"Time": 5, "Value #A": 1, "Value #B": 2, "Value #C": 3, "Value #D": 4, "connector": 0}, "renameByName": {"Time": "Update Time", "Value #A": "Paused Tasks", "Value #B": "Failed Tasks", "Value #C": "Unassigned Tasks", "Value #D": "Destroyed Tasks", "connector": "Connector"}}}], "type": "table"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "Number of connector tasks", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 25}, "id": 35, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_connect_worker_connector_running_task_count{strimzi_io_kind=~\"KafkaConnect.*\",strimzi_io_cluster=\"$strimzi_connect_cluster_name\"})", "format": "time_series", "hide": false, "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "running", "metric": "", "refId": "A", "step": 4}, {"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_connect_worker_connector_paused_task_count{strimzi_io_kind=~\"KafkaConnect.*\",strimzi_io_cluster=\"$strimzi_connect_cluster_name\"})", "interval": "", "legendFormat": "paused", "refId": "B"}, {"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_connect_worker_connector_failed_task_count{strimzi_io_kind=~\"KafkaConnect.*\",strimzi_io_cluster=\"$strimzi_connect_cluster_name\"})", "interval": "", "legendFormat": "failed", "refId": "D"}, {"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_connect_worker_connector_unassigned_task_count{strimzi_io_kind=~\"KafkaConnect.*\",strimzi_io_cluster=\"$strimzi_connect_cluster_name\"})", "interval": "", "legendFormat": "unassigned", "refId": "C"}, {"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_connect_worker_connector_destroyed_task_count{strimzi_io_kind=~\"KafkaConnect.*\",strimzi_io_cluster=\"$strimzi_connect_cluster_name\"})", "instant": false, "interval": "", "legendFormat": "destroyed", "refId": "E"}], "title": "Connector Task State", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "left", "cellOptions": {"type": "auto"}, "filterable": false, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 34}, "id": 42, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": false, "displayName": "Connector"}]}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "exemplar": true, "expr": "sum(kafka_connect_connector_status{strimzi_io_kind=~\"KafkaConnect.*\",strimzi_io_cluster=\"$strimzi_connect_cluster_name\"} * on(connector) group_left(connector_class) kafka_connect_connector_connector_class{} * on (connector) group_left(connector_version) kafka_connect_connector_connector_version{} * on (connector) group_left(connector_type) kafka_connect_connector_connector_type{}) by (connector, status, connector_class, kubernetes_pod_name, connector_version, connector_type)", "format": "table", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Connector Status", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["Time", "connector", "kubernetes_pod_name", "status", "connector_class", "connector_type", "connector_version"]}}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {"Time": 6, "connector": 0, "connector_class": 2, "connector_type": 3, "connector_version": 4, "kubernetes_pod_name": 1, "status": 5}, "renameByName": {"Time": "Update Time", "connector": "Connector", "connector_class": "Class", "connector_type": "Type", "connector_version": "Version", "kubernetes_pod_name": "Worker Pod Name", "status": "Status"}}}], "type": "table"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": false, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Task"}, "properties": [{"id": "custom.align", "value": "left"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Total Record Failures"}, "properties": [{"id": "custom.align", "value": "left"}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 41}, "id": 41, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": false, "displayName": "Connector"}]}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "exemplar": true, "expr": "sum(kafka_connect_task_error_total_record_failures{strimzi_io_kind=~\"KafkaConnect.*\",strimzi_io_cluster=\"$strimzi_connect_cluster_name\"} * on(connector, task) group_left(status) kafka_connect_connector_task_status{}) by (connector, status, kubernetes_pod_name, task)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Connector Task Status", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["Time", "connector", "kubernetes_pod_name", "status", "task", "Value"]}}}, {"id": "organize", "options": {"excludeByName": {"connector": false}, "indexByName": {"Time": 5, "Value": 3, "connector": 0, "kubernetes_pod_name": 2, "status": 4, "task": 1}, "renameByName": {"Time": "Update Time", "Value": "Total Record Failures", "connector": "Connector", "kubernetes_pod_name": "Worker Pod Name", "status": "Status", "task": "Task"}}}], "type": "table"}, {"collapsed": false, "datasource": "${DS_PROMETHEUS}", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 49}, "id": 44, "panels": [], "targets": [{"datasource": "${DS_PROMETHEUS}", "refId": "A"}], "title": "JVM", "type": "row"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "Cores", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 50}, "id": 30, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(rate(container_cpu_usage_seconds_total{namespace=\"$kubernetes_namespace\",pod=~\"$strimzi_connect_cluster_name-connect-.+\",container=\"$strimzi_connect_cluster_name-connect\"}[5m])) by (pod)", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "{{pod}}", "metric": "", "refId": "A", "step": 4}], "title": "CPU Usage", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "Memory", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 50}, "id": 31, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum (jvm_memory_used_bytes{strimzi_io_kind=~\"KafkaConnect.*\",strimzi_io_cluster=\"$strimzi_connect_cluster_name\"}) by (kubernetes_pod_name)", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "{{kuberne<PERSON>_pod_name}}", "metric": "", "refId": "A", "step": 4}], "title": "JVM Memory", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "% time in GC", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "max": 100, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 50}, "id": 32, "options": {"legend": {"calcs": ["lastNotNull", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(rate(jvm_gc_collection_seconds_sum{strimzi_io_kind=~\"KafkaConnect.*\",strimzi_io_cluster=\"$strimzi_connect_cluster_name\"}[5m])) by (kubernetes_pod_name)", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "{{kuberne<PERSON>_pod_name}}", "metric": "", "refId": "A", "step": 4}], "title": "Time Spent in GC", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 50}, "id": 37, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(jvm_threads_current{namespace=\"$kubernetes_namespace\",strimzi_io_kind=~\"KafkaConnect.*\",strimzi_io_cluster=\"$strimzi_connect_cluster_name\"}) by (kubernetes_pod_name)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{kuberne<PERSON>_pod_name}}", "refId": "A"}], "title": "JVM Thread Count", "type": "timeseries"}], "refresh": "5s", "schemaVersion": 39, "tags": ["<PERSON><PERSON><PERSON>", "Kafka", "Kafka Connect"], "templating": {"list": [{"current": {}, "error": null, "hide": 0, "includeAll": false, "label": "datasource", "multi": false, "name": "DS_PROMETHEUS", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"current": {}, "datasource": "${DS_PROMETHEUS}", "definition": "", "hide": 0, "includeAll": false, "label": "Namespace", "multi": false, "name": "kubernetes_namespace", "options": [], "query": "query_result(kafka_connect_worker_connector_count{strimzi_io_kind=~\"KafkaConnect.*\"})", "refresh": 1, "regex": "/.*namespace=\"([^\"]*).*/", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {}, "datasource": "${DS_PROMETHEUS}", "definition": "", "hide": 0, "includeAll": false, "label": "Cluster Name", "multi": false, "name": "strimzi_connect_cluster_name", "options": [], "query": "query_result(kafka_connect_worker_connector_count{namespace=\"$kubernetes_namespace\", strimzi_io_kind=~\"KafkaConnect.*\"})", "refresh": 1, "regex": "/.*strimzi_io_cluster=\"([^\"]*).*/", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "Strimzi Kafka Connect", "version": 6, "weekStart": ""}