{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "", "editable": false, "fiscalYearStartMonth": 0, "gnetId": 19004, "graphTooltip": 0, "id": 33, "links": [], "liveNow": false, "panels": [{"collapsed": false, "datasource": {"type": "prometheus", "uid": "W0lFOlOVk"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 54, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "W0lFOlOVk"}, "refId": "A"}], "title": "Basic Statistics", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 0, "y": 1}, "id": 52, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "10.1.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "process_uptime_seconds{application=\"insight-settings\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "metric": "", "range": true, "refId": "A", "step": 14400}], "title": "Uptime - insight-settings", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "dateTimeAsIso"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 6, "y": 1}, "id": 56, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "10.1.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "process_start_time_seconds{application=\"insight-settings\"}*1000", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "metric": "", "range": true, "refId": "A", "step": 14400}], "title": "Start time - insight-settings", "type": "stat"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "W0lFOlOVk"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 4}, "id": 18, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "W0lFOlOVk"}, "refId": "A"}], "title": "HTTP Statistics", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 5}, "id": 4, "links": [], "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.5.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "irate(http_server_requests_seconds_count{ application=\"insight-settings\", uri!~\".*(prometheus|health).*\"}[$__rate_interval])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{method}} [{{status}}] - {{uri}}", "range": true, "refId": "A"}], "title": "Request Count - insight-settings", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "W0lFOlOVk"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 12}, "id": 8, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "W0lFOlOVk"}, "refId": "A"}], "title": "Logback Statistics", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 13}, "id": 14, "links": [], "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.5.1", "targets": [{"alias": "", "datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "irate(logback_events_total{application=\"insight-settings\", level=\"warn\"}[$__rate_interval])", "format": "time_series", "intervalFactor": 1, "legendFormat": "warn", "range": true, "rawSql": "SELECT\n  $__time(time_column),\n  value1\nFROM\n  metric_table\nWHERE\n  $__timeFilter(time_column)\n", "refId": "A"}], "title": "WARN logs - insight-settings", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 8, "y": 13}, "id": 10, "links": [], "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.5.1", "targets": [{"alias": "", "datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "irate(logback_events_total{application=\"insight-settings\", level=\"error\"}[$__rate_interval])", "format": "time_series", "intervalFactor": 1, "legendFormat": "error", "range": true, "rawSql": "SELECT\n  $__time(time_column),\n  value1\nFROM\n  metric_table\nWHERE\n  $__timeFilter(time_column)\n", "refId": "A"}], "title": "ERROR logs", "type": "timeseries"}], "refresh": "", "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-2d", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "Insight Dashboard (Public)", "uid": "d2f977b9-74bc-43b9-8bfa-a4d20813cff5", "version": 7, "weekStart": ""}