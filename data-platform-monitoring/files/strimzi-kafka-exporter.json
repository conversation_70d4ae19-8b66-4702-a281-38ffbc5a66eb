{"__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "7.4.5"}, {"type": "panel", "id": "stat", "name": "Stat"}, {"type": "datasource", "id": "prometheus", "name": "Prometheus"}, {"type": "panel", "id": "timeseries", "name": "Timeseries"}], "annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Kafka topics and consumer groups", "editable": true, "fiscalYearStartMonth": 0, "gnetId": 7589, "graphTooltip": 0, "id": 9, "links": [], "panels": [{"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 0, "y": 0}, "id": 27, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "count(kafka_topic_partitions{topic=~\"$topic\", namespace=~\"$kubernetes_namespace\"})", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "title": "Topics", "type": "stat"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 3, "y": 0}, "id": 28, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_topic_partitions{topic=~\"$topic\", namespace=~\"$kubernetes_namespace\"})", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "title": "Partitions", "type": "stat"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 6, "y": 0}, "id": 29, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_topic_partition_replicas{topic=~\"$topic\", namespace=\"$kubernetes_namespace\"})", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "title": "Replicas", "type": "stat"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 9, "y": 0}, "id": 30, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_topic_partition_in_sync_replica{topic=~\"$topic\", namespace=~\"$kubernetes_namespace\"})", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "title": "In Sync Replicas", "type": "stat"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgb(255, 255, 255)", "value": null}, {"color": "#F2495C", "value": 1}, {"color": "#d44a3a", "value": 2}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 12, "y": 0}, "id": 31, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_topic_partition_under_replicated_partition{topic=~\"$topic\", namespace=~\"$kubernetes_namespace\"})", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "title": "Under Replicated Partitions", "type": "stat"}, {"datasource": "${DS_PROMETHEUS}", "description": "Number of partitions which are at their minimum in sync replica count (| ISR | == | min.insync.replicas |)", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgb(255, 255, 255)", "value": null}, {"color": "#F2495C", "value": 1}, {"color": "#d44a3a", "value": 2}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 15, "y": 0}, "id": 33, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_cluster_partition_atminisr{topic=~\"$topic\", namespace=~\"$kubernetes_namespace\"})", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "title": "Partitions at minimum ISR", "type": "stat"}, {"datasource": "${DS_PROMETHEUS}", "description": "Number of partitions which are under their minimum in sync replica count (| ISR | < | min.insync.replicas |)", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgb(255, 255, 255)", "value": null}, {"color": "#F2495C", "value": 1}, {"color": "#d44a3a", "value": 2}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 18, "y": 0}, "id": 34, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_cluster_partition_underminisr{topic=~\"$topic\", namespace=~\"$kubernetes_namespace\"})", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "title": "Partitions under minimum ISR", "type": "stat"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "#F2495C", "value": 1}, {"color": "#d44a3a", "value": 2}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 21, "y": 0}, "id": 32, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_topic_partition_leader_is_preferred{topic=~\"$topic\", namespace=~\"$kubernetes_namespace\"} < bool 1)", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "title": "Partitions not on preferred node", "type": "stat"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 10, "w": 8, "x": 0, "y": 4}, "id": 14, "options": {"legend": {"calcs": ["lastNotNull", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 480}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(rate(kafka_topic_partition_current_offset{topic=~\"$topic\", namespace=~\"$kubernetes_namespace\"}[5m])) by (topic)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{topic}}", "refId": "B"}], "title": "Messages in per second", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 10, "w": 8, "x": 8, "y": 4}, "id": 18, "options": {"legend": {"calcs": ["lastNotNull", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 480}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(delta(kafka_consumergroup_current_offset{consumergroup=~\"$consumergroup\",topic=~\"$topic\", namespace=~\"$kubernetes_namespace\"}[5m])/300) by (consumergroup, topic)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{consumergroup}} (topic: {{topic}})", "refId": "A"}], "title": "Messages consumed per second", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 10, "w": 8, "x": 16, "y": 4}, "id": 12, "options": {"legend": {"calcs": ["lastNotNull", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 480}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_consumergroup_lag{consumergroup=~\"$consumergroup\",topic=~\"$topic\", namespace=~\"$kubernetes_namespace\"}) by (consumergroup, topic) ", "format": "time_series", "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{consumergroup}} (topic: {{topic}})", "refId": "A"}], "title": "Lag by  Consumer Group", "type": "timeseries"}, {"columns": [], "datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "fontSize": "100%", "gridPos": {"h": 10, "w": 12, "x": 0, "y": 14}, "hideTimeOverride": true, "id": 24, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pageSize": 10, "pluginVersion": "7.4.5", "scroll": true, "showHeader": true, "sort": {"col": 3, "desc": true}, "styles": [{"alias": "Time", "align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "date"}, {"alias": "Offset", "align": "auto", "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 0, "mappingType": 1, "pattern": "Value", "thresholds": [], "type": "number", "unit": "none"}, {"alias": "", "align": "auto", "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_consumergroup_current_offset{consumergroup=~\"$consumergroup\",topic=~\"$topic\", namespace=~\"$kubernetes_namespace\"}) by (consumergroup,partition,topic)", "format": "table", "instant": true, "intervalFactor": 1, "legendFormat": "{{topic}}", "refId": "A"}], "title": "Consumer Group Offsets", "transform": "table", "type": "table"}, {"columns": [], "datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "fontSize": "100%", "gridPos": {"h": 10, "w": 12, "x": 12, "y": 14}, "hideTimeOverride": true, "id": 25, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pageSize": 10, "pluginVersion": "7.4.5", "scroll": true, "showHeader": true, "sort": {"col": 4, "desc": true}, "styles": [{"alias": "Time", "align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "date"}, {"alias": "Lag", "align": "auto", "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 0, "mappingType": 1, "pattern": "Value", "thresholds": [], "type": "number", "unit": "none"}, {"alias": "", "align": "auto", "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_consumergroup_lag{consumergroup=~\"$consumergroup\",topic=~\"$topic\", namespace=~\"$kubernetes_namespace\"}) by (consumergroup,partition,topic)", "format": "table", "instant": true, "intervalFactor": 1, "legendFormat": "{{topic}}", "refId": "A"}], "title": "Consumer Group Lag", "transform": "table", "type": "table"}, {"columns": [], "datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "fontSize": "100%", "gridPos": {"h": 10, "w": 8, "x": 0, "y": 24}, "hideTimeOverride": true, "id": 20, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "7.4.5", "scroll": true, "showHeader": true, "sort": {"col": 0, "desc": true}, "styles": [{"alias": "Time", "align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "date"}, {"alias": "Partitions", "align": "auto", "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 0, "mappingType": 1, "pattern": "Value", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "", "align": "auto", "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_topic_partitions{topic=~\"$topic\", namespace=~\"$kubernetes_namespace\"}) by (topic)", "format": "table", "instant": true, "intervalFactor": 1, "legendFormat": "{{topic}}", "refId": "A"}], "title": "Number of Partitions", "transform": "table", "type": "table"}, {"columns": [], "datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "fontSize": "100%", "gridPos": {"h": 10, "w": 8, "x": 8, "y": 24}, "hideTimeOverride": true, "id": 22, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pageSize": 10, "pluginVersion": "7.4.5", "scroll": true, "showHeader": true, "sort": {"col": 3, "desc": true}, "styles": [{"alias": "Time", "align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "date"}, {"alias": "Offset", "align": "auto", "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 0, "mappingType": 1, "pattern": "Value", "thresholds": [], "type": "number", "unit": "none"}, {"alias": "", "align": "auto", "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_topic_partition_current_offset{topic=~\"$topic\", namespace=~\"$kubernetes_namespace\"}) by (partition,topic)", "format": "table", "instant": true, "intervalFactor": 1, "legendFormat": "{{topic}}", "refId": "A"}], "title": "Latest Offsets", "transform": "table", "type": "table"}, {"columns": [], "datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "fontSize": "100%", "gridPos": {"h": 10, "w": 8, "x": 16, "y": 24}, "hideTimeOverride": true, "id": 23, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pageSize": 10, "pluginVersion": "7.4.5", "scroll": true, "showHeader": true, "sort": {"col": 3, "desc": true}, "styles": [{"alias": "Time", "align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "date"}, {"alias": "Offset", "align": "auto", "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 0, "mappingType": 1, "pattern": "Value", "thresholds": [], "type": "number", "unit": "none"}, {"alias": "", "align": "auto", "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_topic_partition_oldest_offset{topic=~\"$topic\", namespace=~\"$kubernetes_namespace\"}) by (partition,topic)", "format": "table", "instant": true, "intervalFactor": 1, "legendFormat": "{{topic}}", "refId": "A"}], "title": "Oldest Offsets", "transform": "table", "type": "table"}], "refresh": "5s", "schemaVersion": 39, "tags": ["<PERSON><PERSON><PERSON>", "Kafka", "Kafka Exporter"], "templating": {"list": [{"current": {}, "error": null, "hide": 0, "includeAll": false, "label": "datasource", "multi": false, "name": "DS_PROMETHEUS", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"current": {}, "datasource": "${DS_PROMETHEUS}", "definition": "", "hide": 0, "includeAll": false, "label": "Namespace", "multi": false, "name": "kubernetes_namespace", "options": [], "query": "query_result(kafka_broker_info)", "refresh": 1, "regex": "/.*namespace=\"([^\"]*).*/", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {}, "datasource": "${DS_PROMETHEUS}", "definition": "", "hide": 0, "includeAll": false, "label": "Cluster Name", "multi": false, "name": "strimzi_cluster_name", "options": [], "query": "query_result(kafka_broker_info{namespace=\"$kubernetes_namespace\"})", "refresh": 1, "regex": "/.*strimzi_io_cluster=\"([^\"]*).*/", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {}, "datasource": "${DS_PROMETHEUS}", "definition": "label_values(kafka_consumergroup_current_offset{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\"}, consumergroup)", "hide": 0, "includeAll": true, "label": "Consumer Group", "multi": true, "name": "consumergroup", "options": [], "query": "label_values(kafka_consumergroup_current_offset{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\"}, consumergroup)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {"text": "All", "value": ["$__all"]}, "datasource": "${DS_PROMETHEUS}", "definition": "label_values(kafka_topic_partition_current_offset{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\"}, topic)", "hide": 0, "includeAll": true, "label": "Topic", "multi": true, "name": "topic", "options": [], "query": "label_values(kafka_topic_partition_current_offset{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\"}, topic)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "Strimzi Kafka Exporter", "version": 6, "weekStart": ""}