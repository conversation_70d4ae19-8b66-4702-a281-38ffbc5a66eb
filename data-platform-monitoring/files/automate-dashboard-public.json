{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 1104, "links": [], "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "MerchantPortal Fail"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}]}]}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 0}, "id": 20, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "round(sum(increase(automate_merchantportal_success_count_total[1d])))", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "MerchantPortal Success", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "round(sum(increase(automate_merchantportal_fail_count_total[1d])))", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "interval": "", "legendFormat": "MerchantPortal Fail", "range": true, "refId": "B", "useBackend": false}], "title": "MerchantPortal Success/Fail count today", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "PBA Fail"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}]}]}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 0}, "id": 1, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "round(sum(increase(automate_pba_success_count_total[1d])))", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "PBA Success", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "round(sum(increase(automate_pba_fail_count_total[1d])))", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "PBA Fail", "range": true, "refId": "B", "useBackend": false}], "title": "PBA Success/Fail count today", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "POS Fail"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}]}]}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 0}, "id": 5, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "round(sum(increase(automate_pos_success_count_total[1d])))", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "POS Success", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "round(sum(increase(automate_pos_fail_count_total[1d])))", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "POS Fail", "range": true, "refId": "B", "useBackend": false}], "title": "POS Success/Fail count today", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "TMS Fail"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}]}]}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 7}, "id": 3, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "round(sum(increase(automate_tms_success_count_total[1d])))", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "TMS Success", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "round(sum(increase(automate_tms_fail_count_total[1d])))", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "interval": "", "legendFormat": "TMS Fail", "range": true, "refId": "B", "useBackend": false}], "title": "TMS Success/Fail count today", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "SoftPOS Fail"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}]}]}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 7}, "id": 17, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "round(sum(increase(automate_softpos_success_count_total[1d])))", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "SoftPOS Success", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "builder", "expr": "round(sum(increase(automate_softpos_fail_count_total[1d])))", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "interval": "", "legendFormat": "SoftPOS Fail", "range": true, "refId": "B", "useBackend": false}], "title": "SoftPOS Success/Fail count today", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON>"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}]}]}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 7}, "id": 21, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "round(sum(increase(automate_visma_success_count_total[1d])))", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "Visma Success", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "builder", "expr": "round(sum(increase(automate_visma_fail_count_total[1d])))", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "interval": "", "legendFormat": "<PERSON><PERSON><PERSON>", "range": true, "refId": "B", "useBackend": false}], "title": "Visma Success/Fail count today", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1, "scaleDistribution": {"type": "linear"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 14}, "id": 15, "interval": "1d", "options": {"barRadius": 0, "barWidth": 0.5, "fullHighlight": false, "groupWidth": 0.7, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "orientation": "auto", "showValue": "auto", "stacking": "none", "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}, "xField": "Time", "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "pluginVersion": "11.6.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "round(\n  sum(increase(automate_tms_success_count_total[1d] offset -1d)) + \n  sum(increase(automate_softpos_success_count_total[1d] offset -1d))\n)", "instant": false, "interval": "1d", "legendFormat": "Successful automations", "minInterval": "1d", "range": true, "refId": "A", "relativeTime": "7d"}], "timeFrom": "now-7d/d", "title": "Successful automations", "type": "barchart"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1, "scaleDistribution": {"type": "linear"}, "thresholdsStyle": {"mode": "off"}}, "fieldMinMax": false, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 22}, "id": 13, "options": {"barRadius": 0, "barWidth": 0.5, "fullHighlight": false, "groupWidth": 0.7, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "orientation": "vertical", "showValue": "auto", "stacking": "none", "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}, "xField": "Time", "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "pluginVersion": "11.6.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "exemplar": false, "expr": "round(sum(increase(automate_arrangementorchestrator_agreement_count_total[24h])) by (highway))", "format": "time_series", "fullMetaSearch": false, "includeNullMetadata": true, "instant": true, "legendFormat": "{{highway}}", "range": false, "refId": "A", "useBackend": false}], "title": "Highway count", "type": "barchart"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1, "scaleDistribution": {"type": "linear"}, "thresholdsStyle": {"mode": "off"}}, "fieldMinMax": false, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 30}, "id": 14, "options": {"barRadius": 0, "barWidth": 0.5, "fullHighlight": false, "groupWidth": 0.7, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "orientation": "vertical", "showValue": "auto", "stacking": "none", "tooltip": {"mode": "single", "sort": "none"}, "xField": "Time", "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "pluginVersion": "11.4.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "exemplar": false, "expr": "round(sum(increase(automate_arrangementorchestrator_agreement_count_total[24h])) by (status))", "format": "time_series", "fullMetaSearch": false, "includeNullMetadata": true, "instant": true, "legendFormat": "{{status}}", "range": false, "refId": "A", "useBackend": false}], "title": "Status count", "type": "barchart"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1, "scaleDistribution": {"type": "linear"}, "thresholdsStyle": {"mode": "off"}}, "fieldMinMax": false, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 38}, "id": 16, "options": {"barRadius": 0, "barWidth": 0.78, "fullHighlight": false, "groupWidth": 0.94, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "orientation": "vertical", "showValue": "auto", "stacking": "none", "tooltip": {"mode": "single", "sort": "none"}, "xField": "Time", "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "pluginVersion": "11.4.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "exemplar": false, "expr": "round(sum(increase(automate_arrangementorchestrator_agreement_count_total[24h])) by (contract_type))", "format": "time_series", "fullMetaSearch": false, "includeNullMetadata": true, "instant": true, "legendFormat": "{{status}}", "range": false, "refId": "A", "useBackend": false}], "title": "Contract type", "type": "barchart"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "MerchantPortal Fail"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}]}]}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 46}, "id": 19, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "round(sum(increase(automate_merchantportal_success_count_total[30d])))", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "MerchantPortal Success", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "round(sum(increase(automate_merchantportal_fail_count_total[30d])))", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "MerchantPortal Fail", "range": true, "refId": "B", "useBackend": false}], "title": "MerchantPortal Success/Fail count last 30 days", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "PBA Fail"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}]}]}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 46}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "round(sum(increase(automate_pba_success_count_total[30d])))", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "PBA Success", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "round(sum(increase(automate_pba_fail_count_total[30d])))", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "PBA Fail", "range": true, "refId": "B", "useBackend": false}], "title": "PBA Success/Fail count last 30 days", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "POS Fail"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}]}]}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 46}, "id": 11, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "round(sum(increase(automate_pos_success_count_total[30d])))", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "POS Success", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "round(sum(increase(automate_pos_fail_count_total[30d])))", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "POS Fail", "range": true, "refId": "B", "useBackend": false}], "title": "POS Success/Fail count last 30 days", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "TMS Fail"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}]}]}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 53}, "id": 8, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "round(sum(increase(automate_tms_success_count_total[30d])))", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "TMS Success", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "round(sum(increase(automate_tms_fail_count_total[30d])))", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "TMS Fail", "range": true, "refId": "B", "useBackend": false}], "title": "TMS Success/Fail count last 30 days", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "SoftPOS Fail"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}]}]}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 53}, "id": 18, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "round(sum(increase(automate_softpos_success_count_total[30d])))", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "SoftPOS Success", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "round(sum(increase(automate_softpos_fail_count_total[30d])))", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "SoftPOS Fail", "range": true, "refId": "B", "useBackend": false}], "title": "SoftPOS Success/Fail count last 30 days", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 60}, "id": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "round(sum(increase(automate_pba_success_count_total[90d])))", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "PBA", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "round(sum(increase(automate_pos_success_count_total[90d])))", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "POS", "range": true, "refId": "B", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "round(sum(increase(automate_tms_success_count_total[90d])))", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "TMS", "range": true, "refId": "C", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "round(sum(increase(automate_softpos_success_count_total[90d])))", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "SoftPOS", "range": true, "refId": "D", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "round(sum(increase(automate_merchantportal_success_count_total[90d])))", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "MerchantPortal", "range": true, "refId": "E", "useBackend": false}], "title": "PBA/POS/TMS/SoftPOS/MerchantPortal Success", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 60}, "id": 12, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "round(sum(increase(automate_pba_fail_count_total[90d])))", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "PBA", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "round(sum(increase(automate_pos_fail_count_total[90d])))", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "POS", "range": true, "refId": "B", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "round(sum(increase(automate_tms_fail_count_total[90d])))", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "TMS", "range": true, "refId": "C", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "round(sum(increase(automate_softpos_fail_count_total[90d])))", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "SoftPOS", "range": true, "refId": "D", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "round(sum(increase(automate_merchantportal_fail_count_total[90d])))", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "MerchantPortal", "range": true, "refId": "E", "useBackend": false}], "title": "PBA/POS/TMS/SoftPOS/MerchantPortal Fail", "type": "timeseries"}], "preload": false, "schemaVersion": 41, "tags": [], "templating": {"list": [{"current": {"text": "Prometheus", "value": "prometheus"}, "includeAll": false, "label": "Data source", "name": "datasource", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "type": "datasource"}]}, "time": {"from": "now/d", "to": "now/d"}, "timepicker": {"hidden": true}, "timezone": "browser", "title": "Automate Dashboard Public", "uid": "ee5aljmx5wflse", "version": 2, "weekStart": "monday"}