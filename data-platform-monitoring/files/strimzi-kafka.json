{"__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "7.4.5"}, {"type": "panel", "id": "stat", "name": "Stat"}, {"type": "datasource", "id": "prometheus", "name": "Prometheus"}, {"type": "panel", "id": "timeseries", "name": "Timeseries"}], "annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 22, "links": [], "panels": [{"datasource": "${DS_PROMETHEUS}", "description": "Number of brokers online", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#d44a3a", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 0}, {"color": "#299c46", "value": 2}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 0, "y": 0}, "id": 46, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "7.4.5", "repeatDirection": "h", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "count(kafka_server_replicamanager_leadercount{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\"})", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Brokers Online", "type": "stat"}, {"datasource": "${DS_PROMETHEUS}", "description": "Number of active controllers in the cluster", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "#e5ac0e", "value": 2}, {"color": "#bf1b00"}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 3, "y": 0}, "id": 36, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_controller_kafkacontroller_activecontrollercount{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\"})", "format": "time_series", "hide": false, "intervalFactor": 1, "refId": "A"}], "title": "Active Controllers", "type": "stat"}, {"datasource": "${DS_PROMETHEUS}", "description": "Unclean leader election rate", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 2}, {"color": "#d44a3a"}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 6, "y": 0}, "id": 38, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(irate(kafka_controller_controllerstats_uncleanleaderelections_total{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\"}[5m]))", "format": "time_series", "hide": false, "intervalFactor": 1, "refId": "A"}], "title": "Unclean Leader Election Rate", "type": "stat"}, {"datasource": "${DS_PROMETHEUS}", "description": "Replicas that are online", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#d44a3a", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 0}, {"color": "#299c46", "value": 0}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 9, "y": 0}, "id": 40, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_server_replicamanager_partitioncount{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\"})", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "title": "Online Replicas", "type": "stat"}, {"datasource": "${DS_PROMETHEUS}", "description": "Number of under-replicated partitions (| ISR | < | all replicas |).", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#508642", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 1}, {"color": "#bf1b00", "value": 5}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 12, "y": 0}, "id": 30, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_server_replicamanager_underreplicatedpartitions{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\"})", "format": "time_series", "hide": false, "intervalFactor": 2, "refId": "A"}], "title": "Under Replicated Partitions", "type": "stat"}, {"datasource": "${DS_PROMETHEUS}", "description": "Number of partitions which are at their minimum in sync replica count (| ISR | == | min.insync.replicas |)", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"color": "#508642", "text": "0"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#508642", "value": null}, {"color": "#ef843c", "value": 1}, {"color": "#bf1b00", "value": 5}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 15, "y": 0}, "id": 102, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_cluster_partition_atminisr{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\"})", "format": "time_series", "hide": false, "intervalFactor": 2, "refId": "A"}], "title": "Partitions at minimum ISR", "type": "stat"}, {"datasource": "${DS_PROMETHEUS}", "description": "Number of partitions which are under their minimum in sync replica count (| ISR | < | min.insync.replicas |)", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"color": "#508642", "text": "0"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#508642", "value": null}, {"color": "#ef843c", "value": 1}, {"color": "#bf1b00", "value": 1}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 18, "y": 0}, "id": 103, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_cluster_partition_underminisr{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\"})", "format": "time_series", "hide": false, "intervalFactor": 2, "refId": "A"}], "title": "Partitions under minimum ISR", "type": "stat"}, {"datasource": "${DS_PROMETHEUS}", "description": "Number of partitions that don’t have an active leader and are hence not writable or readable", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#508642", "value": null}, {"color": "#ef843c", "value": 1}, {"color": "#bf1b00", "value": 1}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 21, "y": 0}, "id": 32, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_controller_kafkacontroller_offlinepartitionscount{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\"})", "format": "time_series", "hide": false, "intervalFactor": 1, "refId": "A"}], "title": "Offline Partitions Count", "type": "stat"}, {"collapsed": false, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 4}, "id": 28, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "Kafka", "type": "row"}, {"datasource": "${DS_PROMETHEUS}", "description": "Kafka broker pods memory usage", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 5}, "id": 82, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(container_memory_usage_bytes{namespace=\"$kubernetes_namespace\",pod=~\"$strimzi_cluster_name-$kafka_broker\",container=\"kafka\"}) by (pod)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{pod}}", "refId": "A"}], "title": "Memory Usage", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "description": "Aggregated Kafka broker pods CPU usage", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 5}, "id": 81, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(rate(container_cpu_usage_seconds_total{namespace=\"$kubernetes_namespace\",pod=~\"$strimzi_cluster_name-$kafka_broker\",container=\"kafka\"}[5m])) by (pod)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{pod}}", "refId": "A"}], "title": "CPU Usage", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "description": "Kafka broker pods disk usage", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 5}, "id": 83, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kubelet_volume_stats_available_bytes{namespace=\"$kubernetes_namespace\",persistentvolumeclaim=~\"data(-[0-9]+)?-$strimzi_cluster_name-(kafka|$pool_name)-[0-9]+\"}) by (persistentvolumeclaim)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{persistentvolumeclaim}}", "refId": "A"}], "title": "Available Disk Space", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "description": "Open File Descriptors", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 5}, "id": 107, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(process_open_fds{namespace=\"$kubernetes_namespace\",kubernetes_pod_name=~\"$strimzi_cluster_name-$kafka_broker\",container=\"kafka\"}) by (kubernetes_pod_name)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{kuberne<PERSON>_pod_name}}", "refId": "A"}], "title": "Open File Descriptors", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 12}, "id": 93, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(jvm_memory_used_bytes{namespace=\"$kubernetes_namespace\",kubernetes_pod_name=~\"$strimzi_cluster_name-$kafka_broker\",strimzi_io_name=\"$strimzi_cluster_name-kafka\"}) by (kubernetes_pod_name)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{kuberne<PERSON>_pod_name}}", "refId": "A"}], "title": "JVM Memory Used", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 12}, "id": 95, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(rate(jvm_gc_collection_seconds_sum{namespace=\"$kubernetes_namespace\",kubernetes_pod_name=~\"$strimzi_cluster_name-$kafka_broker\",strimzi_io_name=\"$strimzi_cluster_name-kafka\"}[5m])) by (kubernetes_pod_name)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{kuberne<PERSON>_pod_name}}", "refId": "A"}], "title": "JVM GC Time", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 12}, "id": 97, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(rate(jvm_gc_collection_seconds_count{namespace=\"$kubernetes_namespace\",kubernetes_pod_name=~\"$strimzi_cluster_name-$kafka_broker\",strimzi_io_name=\"$strimzi_cluster_name-kafka\"}[5m])) by (kubernetes_pod_name)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{kuberne<PERSON>_pod_name}}", "refId": "A"}], "title": "JVM GC Count", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "description": "JVM thread count", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 12}, "id": 108, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(jvm_threads_current{namespace=\"$kubernetes_namespace\",kubernetes_pod_name=~\"$strimzi_cluster_name-$kafka_broker\",strimzi_io_name=\"$strimzi_cluster_name-kafka\"}) by (kubernetes_pod_name)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{kuberne<PERSON>_pod_name}}", "refId": "A"}], "title": "JVM Thread Count", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "description": "Total incoming byte rate", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"color": "rgba(237, 129, 40, 0.89)", "text": "0"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#d44a3a", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 0}, {"color": "#299c46", "value": 2}]}, "unit": "Bps"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 19}, "id": 98, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "7.4.5", "repeatDirection": "h", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(irate(kafka_server_brokertopicmetrics_bytesin_total{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\",topic=~\"$kafka_topic\",topic!=\"\",kubernetes_pod_name=~\"$strimzi_cluster_name-$kafka_broker\"}[5m]))", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Total Incoming Byte Rate", "type": "stat"}, {"datasource": "${DS_PROMETHEUS}", "description": "Total outgoing byte rate", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"color": "rgba(237, 129, 40, 0.89)", "text": "0"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#d44a3a", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 0}, {"color": "#299c46", "value": 2}]}, "unit": "Bps"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 6, "y": 19}, "id": 99, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "7.4.5", "repeatDirection": "h", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(irate(kafka_server_brokertopicmetrics_bytesout_total{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\",topic=~\"$kafka_topic\",topic!=\"\",kubernetes_pod_name=~\"$strimzi_cluster_name-$kafka_broker\"}[5m]))", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Total Outgoing Byte Rate", "type": "stat"}, {"datasource": "${DS_PROMETHEUS}", "description": "Incoming messages rate", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"color": "rgba(237, 129, 40, 0.89)", "text": "0"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#d44a3a", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 0}, {"color": "#299c46", "value": 2}]}, "unit": "wps"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 12, "y": 19}, "id": 100, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "7.4.5", "repeatDirection": "h", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(irate(kafka_server_brokertopicmetrics_messagesin_total{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\",topic=~\"$kafka_topic\",topic!=\"\",kubernetes_pod_name=~\"$strimzi_cluster_name-$kafka_broker\"}[5m]))", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Incoming Messages Rate", "type": "stat"}, {"datasource": "${DS_PROMETHEUS}", "description": "Total produce request rate", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"color": "rgba(237, 129, 40, 0.89)", "text": "0"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#d44a3a", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 0}, {"color": "#299c46", "value": 2}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 19}, "id": 101, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "7.4.5", "repeatDirection": "h", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(irate(kafka_server_brokertopicmetrics_totalproducerequests_total{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\",topic=~\"$kafka_topic\",topic!=\"\",kubernetes_pod_name=~\"$strimzi_cluster_name-$kafka_broker\"}[5m]))", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Total Produce Request Rate", "type": "stat"}, {"datasource": "${DS_PROMETHEUS}", "description": "Byte rate", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 23}, "id": 44, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(irate(kafka_server_brokertopicmetrics_bytesin_total{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\",topic=~\"$kafka_topic\",topic!=\"\",kubernetes_pod_name=~\"$strimzi_cluster_name-$kafka_broker\"}[5m]))", "format": "time_series", "hide": false, "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "Total Incoming Byte Rate", "refId": "A"}, {"datasource": "${DS_PROMETHEUS}", "expr": "sum(irate(kafka_server_brokertopicmetrics_bytesout_total{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\",topic=~\"$kafka_topic\",topic!=\"\",kubernetes_pod_name=~\"$strimzi_cluster_name-$kafka_broker\"}[5m]))", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Total Outgoing Byte Rate", "refId": "B"}], "title": "Byte Rate", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 23}, "id": 58, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(irate(kafka_server_brokertopicmetrics_messagesin_total{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\",topic=~\"$kafka_topic\",topic!=\"\",kubernetes_pod_name=~\"$strimzi_cluster_name-$kafka_broker\"}[5m]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "Total Incoming Messages Rate", "refId": "D"}], "title": "Messages In Per Second", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "description": "Produce request rate", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 31}, "id": 50, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(irate(kafka_server_brokertopicmetrics_totalproducerequests_total{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\",topic=~\"$kafka_topic\",topic!=\"\",kubernetes_pod_name=~\"$strimzi_cluster_name-$kafka_broker\"}[5m]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "Total Produce Request Rate", "refId": "A"}, {"datasource": "${DS_PROMETHEUS}", "expr": "sum(irate(kafka_server_brokertopicmetrics_failedproducerequests_total{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\",topic=~\"$kafka_topic\",topic!=\"\",kubernetes_pod_name=~\"$strimzi_cluster_name-$kafka_broker\"}[5m]))", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Failed Produce Request Rate", "refId": "B"}], "title": "Produce Request Rate", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "description": "Fetch request rate", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 31}, "id": 56, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(irate(kafka_server_brokertopicmetrics_totalfetchrequests_total{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\",topic=~\"$kafka_topic\",topic!=\"\",kubernetes_pod_name=~\"$strimzi_cluster_name-$kafka_broker\"}[5m]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "Fetch Request Rate", "refId": "A"}, {"datasource": "${DS_PROMETHEUS}", "expr": "  sum(irate(kafka_server_brokertopicmetrics_failedfetchrequests_total{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\",topic=~\"$kafka_topic\",topic!=\"\",kubernetes_pod_name=~\"$strimzi_cluster_name-$kafka_broker\"}[5m]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "Failed Fetch Request Rate", "refId": "B"}], "title": "Fetch Request Rate", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "description": "Average percentage of time network processor is idle", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 39}, "id": 60, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_network_socketserver_networkprocessoravgidle_percent{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\",kubernetes_pod_name=~\"$strimzi_cluster_name-$kafka_broker\"}*100) by (kubernetes_pod_name)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{kuberne<PERSON>_pod_name}}", "refId": "A"}], "title": "Network Processor Avg Idle Percent", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "description": "Average percentage of time request handler threads are idle", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 39}, "id": 62, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_server_kafkarequesthandlerpool_requesthandleravgidle_percent{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\",kubernetes_pod_name=~\"$strimzi_cluster_name-$kafka_broker\"}*100) by (kubernetes_pod_name)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{kuberne<PERSON>_pod_name}}", "refId": "A"}], "title": "Request Handler Avg Idle <PERSON>", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "description": "<PERSON><PERSON> writes", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 47}, "id": 104, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(irate(kafka_server_kafkaserver_linux_disk_write_bytes{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\",kubernetes_pod_name=~\"$strimzi_cluster_name-$kafka_broker\"}[5m])) by (kubernetes_pod_name)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{kuberne<PERSON>_pod_name}}", "refId": "A"}], "title": "Disk Writes", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "description": "Disk reads", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 47}, "id": 105, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(irate(kafka_server_kafkaserver_linux_disk_read_bytes{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\",kubernetes_pod_name=~\"$strimzi_cluster_name-$kafka_broker\"}[5m])) by (kubernetes_pod_name)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{kuberne<PERSON>_pod_name}}", "refId": "A"}], "title": "Disk Reads", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "description": "Disk reads", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 47}, "id": 106, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_server_socket_server_metrics_connection_count{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\",kubernetes_pod_name=~\"$strimzi_cluster_name-$kafka_broker\"}) by (kubernetes_pod_name, listener)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{listener}}-{{kubernetes_pod_name}}", "refId": "A"}], "title": "Connection Count per Listener", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"custom": {"align": null, "filterable": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Log Size"}, "properties": [{"id": "unit", "value": "decbytes"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 55}, "id": 91, "links": [], "options": {"showHeader": true}, "pluginVersion": "7.4.5", "targets": [{"expr": "kafka_log_log_size{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\",kubernetes_pod_name=~\"$strimzi_cluster_name-$kafka_broker\",topic=~\"$kafka_topic\",partition=~\"$kafka_partition\"}", "format": "table", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "{{topic}}:{{partition}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Log Size", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true, "container": true, "endpoint": true, "instance": true, "job": true, "kubernetes_pod_name": true, "namespace": true, "node_ip": true, "node_name": true, "pod": true, "strimzi_io_broker_role": true, "strimzi_io_cluster": true, "strimzi_io_component_type": true, "strimzi_io_controller": true, "strimzi_io_controller_name": true, "strimzi_io_controller_role": true, "strimzi_io_kind": true, "strimzi_io_name": true, "strimzi_io_pod_name": true, "strimzi_io_pool_name": true}, "indexByName": {"Time": 0, "Value": 23, "__name__": 1, "container": 3, "endpoint": 4, "instance": 5, "job": 6, "kubernetes_pod_name": 7, "namespace": 8, "node_ip": 9, "node_name": 10, "partition": 11, "pod": 12, "strimzi_io_broker_role": 13, "strimzi_io_cluster": 14, "strimzi_io_component_type": 15, "strimzi_io_controller": 16, "strimzi_io_controller_name": 17, "strimzi_io_controller_role": 18, "strimzi_io_kind": 19, "strimzi_io_name": 20, "strimzi_io_pod_name": 21, "strimzi_io_pool_name": 22, "topic": 2}, "renameByName": {"Value": "Log Size", "partition": "Partition", "strimzi_io_name": "", "topic": "Topic"}}}], "type": "table"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 55}, "id": 110, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.4.5", "targets": [{"datasource": "${DS_PROMETHEUS}", "expr": "sum(kafka_server_zookeeperclientmetrics_zookeeperrequestlatencyms{namespace=\"$kubernetes_namespace\",kubernetes_pod_name=~\"$strimzi_cluster_name-$kafka_broker\",strimzi_io_name=\"$strimzi_cluster_name-kafka\"}) by (kubernetes_pod_name)", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{kuberne<PERSON>_pod_name}}", "refId": "A"}], "title": "ZK request latecy", "type": "timeseries"}], "refresh": "5s", "schemaVersion": 39, "tags": ["<PERSON><PERSON><PERSON>", "Kafka"], "templating": {"list": [{"current": {}, "error": null, "hide": 0, "includeAll": false, "label": "datasource", "multi": false, "name": "DS_PROMETHEUS", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"current": {}, "datasource": "${DS_PROMETHEUS}", "definition": "", "hide": 0, "includeAll": false, "label": "Namespace", "multi": false, "name": "kubernetes_namespace", "options": [], "query": "query_result(kafka_server_replicamanager_leadercount)", "refresh": 1, "regex": "/.*namespace=\"([^\"]*).*/", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {}, "datasource": "${DS_PROMETHEUS}", "definition": "", "hide": 0, "includeAll": false, "label": "Cluster Name", "multi": false, "name": "strimzi_cluster_name", "options": [], "query": "query_result(kafka_server_replicamanager_leadercount{namespace=\"$kubernetes_namespace\"})", "refresh": 1, "regex": "/.*strimzi_io_cluster=\"([^\"]*).*/", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {}, "datasource": "${DS_PROMETHEUS}", "definition": "", "hide": 2, "includeAll": true, "label": "Pool name", "multi": true, "name": "pool_name", "options": [], "query": "query_result(kafka_server_replicamanager_leadercount{namespace=\"$kubernetes_namespace\"})", "refresh": 1, "regex": "/.*strimzi_io_pool_name=\"([^\"]*).*/", "skipUrlSync": false, "sort": 3, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {}, "datasource": "${DS_PROMETHEUS}", "definition": "", "hide": 0, "includeAll": true, "label": "Broker", "multi": false, "name": "kafka_broker", "options": [], "query": "query_result(kafka_server_replicamanager_leadercount{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\"})", "refresh": 1, "regex": "/.*pod_name=\"$strimzi_cluster_name-([^\"]*).*/", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".+", "current": {}, "datasource": "${DS_PROMETHEUS}", "definition": "", "hide": 0, "includeAll": true, "label": "Topic", "multi": false, "name": "kafka_topic", "options": [], "query": "query_result(kafka_cluster_partition_replicascount{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\",kubernetes_pod_name=~\"$strimzi_cluster_name-$kafka_broker\"})", "refresh": 1, "regex": "/.*topic=\"([^\"]*).*/", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {}, "datasource": "${DS_PROMETHEUS}", "definition": "", "hide": 0, "includeAll": true, "label": "Partition", "multi": true, "name": "kafka_partition", "options": [], "query": "query_result(kafka_log_log_size{namespace=\"$kubernetes_namespace\",strimzi_io_cluster=\"$strimzi_cluster_name\",kubernetes_pod_name=~\"$strimzi_cluster_name-$kafka_broker\",topic=~\"$kafka_topic\"})", "refresh": 1, "regex": "/.*partition=\"([^\"]*).*/", "skipUrlSync": false, "sort": 3, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "<PERSON><PERSON><PERSON>", "version": 4, "weekStart": ""}