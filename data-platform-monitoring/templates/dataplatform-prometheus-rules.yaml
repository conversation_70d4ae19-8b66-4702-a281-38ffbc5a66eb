apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  labels:
    role: alert-rules
    app: {{ .Values.prometheus.labels.app }}
  name: dataplatform-prometheus-rules
spec:
  groups:
  - name: automate
    rules:
    - alert: EfecteErrorLogs
      expr: sum(rate(logback_events_total{application="automate-efecte", level="error"}[1m])) > 0
      for: 2m
      labels:
        severity: warning
      annotations:
        summary: 'automate-efecte got an error'
        description: 'An error was logged in automate-efecte.'
    - alert: AuthorizationsTransformerErrorLogs
      expr: sum(rate(logback_events_total{application="authorizations-transformer", level="error"}[1m])) > 0
      for: 2m
      labels:
        severity: warning
      annotations:
        summary: 'authorizations transformer got an error'
        description: 'An error was logged in authorizations transformer. Check logs for more information.'
    - alert: AuthorizationsTransformerFail
      expr: increase(authorizations_transformer_fail_count_total[5m]) > 0
      for: 10s
      labels:
        severity: warning
      annotations:
        summary: 'authorizations transformer encountered a error'
        description: 'An error was logged in authorizations transformer. Check logs for more information.'
    - alert: NoSuccessfulAuthorizationFilesProcessedForTooLong
      expr: increase(authorizations_transformer_success_count_total[{{ .Values.rules.authorization.processInterval | default "1h" }}]) == 0
      for: 1m
      labels:
        severity: warning
      annotations:
        summary: 'No successfully processed authorization files for an extended period'
        description: 'No authorization files have been successfully processed for an extended period, which may indicate an issue with the file processing or data flow. Check logs for more information.'
#    - alert: AuthorizationsFileGapDetected
#      expr: increase(authorizations_transformer_file_gaps_count_total[10m]) > 0
#      for: 10s
#      labels:
#        severity: warning
#      annotations:
#        summary: 'File gap detected in authorization processing'
#        description: 'A gap in file processing was detected in the Authorizations transformer. Check logs for more information.'
#    - alert: AuthorizationsFileCountOutOfBounds
#      expr: round(sum(increase(authorizations_transformer_success_count_total[24h]))) != 24
#        and round(sum(increase(authorizations_transformer_success_count_total[24h]))) != 288
#      for: 1h
#      labels:
#        severity: warning
#      annotations:
#        summary: "Successful process of authorizations file count is out of expected bounds"
#        description: "The application has processed an unexpected number of successful authorization files (not 24 or 288) in the last 24 hours. Actual count: {{`{{ $value }}`}}"
