apiVersion: v1
kind: ConfigMap
metadata:
  name: loki-alerting-rules
  labels:
    loki_rule: 'true'
data:
  rules.yaml: |
    groups:
      - name: data-platform-rules
        rules:
          - alert: ConsumerExceptionError
            expr: |
              sum by (app) (rate({app="authorizations-transformer", level="ERROR"} |= "Consumer exception" [5m])) > 0
            for: 5m
            labels:
              severity: critical
            annotations:
              summary: "Consumer exception error detected"
              description: "An error with the text 'Consumer exception' was detected for app 'authorizations-transformer'."
          - alert: TimeoutException - automate-efecte
            expr: |
              sum by (app) (rate({app="automate-efecte", namespace="automate", level="ERROR"} |= "TimeoutException" [5m])) > 0
            for: 5m
            labels:
              severity: critical
            annotations:
              summary: "automate-efecte got a TimeoutException"
              description: "An error with the text 'TimeoutException' was detected for app 'automate-efecte'."
          - alert: Kafka oauth service error
            expr: |
              sum by (app) (rate({app="kafka", namespace="kafka", level="ERROR"} |= "io.strimzi.kafka.oauth.services.ServiceException" [5m])) > 0
            for: 5m
            labels:
              severity: critical
            annotations:
              summary: "Kafka got a io.strimzi.kafka.oauth.services.ServiceException"
              description: "An error with the text 'io.strimzi.kafka.oauth.services.ServiceException' was detected for app 'kafka'."


