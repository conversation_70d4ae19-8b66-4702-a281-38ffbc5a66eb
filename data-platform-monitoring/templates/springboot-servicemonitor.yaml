apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: spring-boot-monitor
spec:
  selector:
    matchExpressions:
      - key: app.kubernetes.io/name
        operator: In
        values:
          - auth-stats-svc
          - authorizations-transformer-svc
          - automate-efecte-svc
          - automate-pba-svc
          - automate-pos-svc
          - automate-tms-svc
          - automate-softpos-svc
          - automate-arrangement-orchestrator-svc
          - automate-visma-svc
          - automate-signup-svc
  endpoints:
    - port: http-9090
      path: /actuator/prometheus
      scheme: https
      interval: 5s
      tlsConfig:
        insecureSkipVerify: true
  namespaceSelector:
    matchNames:
      - automate