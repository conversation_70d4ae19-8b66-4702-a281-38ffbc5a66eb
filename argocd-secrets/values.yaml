tenantId: TENANT_ID
keyvaultName: KEY_VAULT_NAME
clientId: CLIENT_ID

namespaces: 
  - name: argocd
    secretObjects: 
      - secretName: argocd-oci-access-token
        type: Opaque
        labels:
          argocd.argoproj.io/secret-type: repository
        data:
          - objectName: "argocd-oci-access-enable-oci"
            key: enableOCI
          - objectName: "argocd-oci-access-type"
            key: type
          - objectName: "argocd-oci-access-url"
            key: url
          - objectName: "argocd-oci-access-token"
            key: password
          - objectName: "argocd-oci-access-username"
            key: username
      - secretName: argocd-github-access-token
        type: Opaque
        labels:
          argocd.argoproj.io/secret-type: repository
        data:
          - objectName: "argocd-github-access-url"
            key: url
          - objectName: "argocd-github-access-token"
            key: password
          - objectName: "argocd-github-access-username"
            key: username
      - secretName: argocd-server-tls
        type: kubernetes.io/tls
        data: 
        - objectName: wildcard-payexnet-cert
          key: tls.key
        - objectName: wildcard-payexnet-cert
          key: tls.crt
    parameters:
      usePodIdentity: "false"
      useVMManagedIdentity: "false"
      objects: |
        array:
          - |
            objectName: "argocd-oci-access-enable-oci"
            objectType: secret
          - |
            objectName: "argocd-oci-access-type"
            objectType: secret
          - |
            objectName: "argocd-oci-access-url"
            objectType: secret
          - |
            objectName: "argocd-oci-access-token"
            objectType: secret
          - |
            objectName: "argocd-oci-access-username"
            objectType: secret
          - |
            objectName: "argocd-github-access-url"
            objectType: secret
          - |
            objectName: "argocd-github-access-token"
            objectType: secret
          - |
            objectName: "argocd-github-access-username"
            objectType: secret
          - |
            objectName: wildcard-payexnet-cert
            objectType: secret

