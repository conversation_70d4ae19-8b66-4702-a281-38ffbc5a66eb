apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: app-of-apps-parent
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  destination:
    server: https://kubernetes.default.svc
    namespace: argocd
  project: default
  source:
    path: argocd-app-of-apps/environments/{{ .Values.environment }}
    repoURL: https://github.com/PayEx/helm-charts-data-platform
    targetRevision: HEAD
    helm:
      valueFiles:
      - values.yaml
      parameters:
      - name: "tenantId"
        value: {{ .Values.tenantId }}
      - name: "subscriptionId"
        value: {{ .Values.subscriptionId }}
      - name: "keyvaultName"
        value: {{ .Values.keyvaultName }}
      - name: "clientId"
        value: {{ .Values.clientId }}
      - name: "loadBalancerIP"
        value: {{ .Values.loadBalancerIP }}

  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: true