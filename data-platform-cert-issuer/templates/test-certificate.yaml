{{- if .Values.test -}}
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: test-cert
  namespace: cert-manager
spec:
  commonName: {{ .Values.test.commonName }}
  subject:
    organizations:
      - Swedbank Pay AB
    organizationalUnits:
      - SP Architecture & Infrastructure
  secretName: test-cert
  privateKey:
    rotationPolicy: Always
  revisionHistoryLimit: 1  
  issuerRef:
    name: dataplatform-cluster-issuer
    kind: ClusterIssuer
    group: cert-manager.io
  usages:
    - digital signature
    - key encipherment
    - server auth
  dnsNames:
  - {{ .Values.test.commonName }}
{{- end -}}