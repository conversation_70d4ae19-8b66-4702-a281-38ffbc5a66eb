apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: data-platform-cluster-broker-tls
  namespace: kafka
spec:
  commonName: {{ .Values.kafka.broker.commonName }}
  subject:
    organizations:
      - Swedbank Pay AB
    organizationalUnits:
      - SP Architecture & Infrastructure
  secretName: data-platform-cluster-broker-tls
  privateKey:
    algorithm: RSA
    size: 4096
  revisionHistoryLimit: 1  
  issuerRef:
    name: {{ required "You must set issuer.name" .Values.issuer.name }}
    kind: ClusterIssuer
    group: cert-manager.io
  dnsNames:
  {{- range .Values.kafka.broker.dnsNames }}
  - {{ . }}
  {{- end }}
