apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: {{ required "You must set issuer.name" .Values.issuer.name }}
spec:
  {{- if .Values.acme }}
  acme:
    server: {{ required "You must set issuer.server" .Values.acme.server }}
    email: {{ required "You must set issuer.email" .Values.acme.email }}
    privateKeySecretRef:
      name: "{{ .Values.issuer.name }}-letsencrypt"
    solvers:
    {{- with .Values.acme.dns01.spec }}
      - dns01:
{{ toYaml . | indent 10 }} 
    {{- end }}
  {{- else }}
  ca:
    secretName: root-secret
  {{- end }}
