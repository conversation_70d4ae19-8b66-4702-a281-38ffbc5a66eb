apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: data-platform-cluster-kafka
  namespace: kafka
spec:
  commonName: {{ .Values.kafka.listener.commonName }}
  subject:
    organizations:
      - Swedbank Pay AB
    organizationalUnits:
      - SP Architecture & Infrastructure
  secretName: data-platform-cluster-listener-cert
  privateKey:
    algorithm: RSA
    size: 4096
  revisionHistoryLimit: 1  
  issuerRef:
    name: selfsigned-cluster-issuer
    kind: ClusterIssuer
    group: cert-manager.io
  dnsNames:
  {{- range .Values.kafka.listener.dnsNames }}
  - {{ . }}
  {{- end }}
  ipAddresses:
  {{- range .Values.kafka.listener.ipAddresses }}
  - {{ . }}
  {{- end }}