kafka:
  broker:
    commonName: bootstrap.kafka.stage.payex.net
    dnsNames:
      - bootstrap.kafka.stage.payex.net
      - broker-0.kafka.stage.payex.net
      - broker-1.kafka.stage.payex.net
      - broker-2.kafka.stage.payex.net
  listener:
    commonName: bootstrap.kafka.stage.payex.net
    dnsNames:
      - bootstrap.kafka.stage.payex.net
      - kafka01.stage.payex.net
    ipAddresses:  
      - ***********
      - ***********
      - ***********
      - ***********

issuer:
  name: dataplatform-cluster-issuer

acme:
  enabled: true
  server: https://acme-staging-v02.api.letsencrypt.org/directory
  email: <EMAIL>
  dns01:
    spec:
      azureDNS:
        resourceGroupName: stage-cloud-data-dns-zone-rg
        subscriptionID: 33279190-7f22-4468-a34d-d61125f01797
        hostedZoneName: kafka.stage.payex.net
        environment: AzurePublicCloud
        managedIdentity:
          clientID: USER_ASSIGNED_IDENTITY_CLIENT_ID
