kafka:
  broker:
    commonName: bootstrap.kafka.dev.payex.net
    dnsNames:
      - bootstrap.kafka.dev.payex.net
      - broker-0.kafka.dev.payex.net
      - broker-1.kafka.dev.payex.net
      - broker-2.kafka.dev.payex.net
  listener:
    commonName: bootstrap.kafka.dev.payex.net
    dnsNames:
      - bootstrap.kafka.dev.payex.net

issuer:
  name: dataplatform-cluster-issuer

acme:
  enabled: true
  server: https://acme-staging-v02.api.letsencrypt.org/directory
  email: <EMAIL>
  dns01:
    spec:
      azureDNS:
        resourceGroupName: dev-cloud-data-dns-zone-rg
        subscriptionID: c8b46846-3156-4220-8dfc-af0fafb8d2b0
        hostedZoneName: kafka.dev.payex.net
        environment: AzurePublicCloud
        managedIdentity:
          clientID: USER_ASSIGNED_IDENTITY_CLIENT_ID
