kafka:
  broker:
    commonName: bootstrap.kafka.payex.net
    dnsNames:
      - bootstrap.kafka.payex.net
      - broker-0.kafka.payex.net
      - broker-1.kafka.payex.net
      - broker-2.kafka.payex.net
  listener:
    commonName: bootstrap.kafka.payex.net
    dnsNames:
      - bootstrap.kafka.payex.net
      - kafka01.payex.net
    ipAddresses:  
      - ************
      - ************
      - ************
      - ************
issuer:
  name: dataplatform-cluster-issuer

acme:
  enabled: true
  server: https://acme-v02.api.letsencrypt.org/directory
  email: <EMAIL>
  dns01:
    spec:
      azureDNS:
        resourceGroupName: prod-cloud-data-dns-zone-rg
        subscriptionID: 38cb90f4-bf95-4b2c-b952-93f468d17aa0
        hostedZoneName: kafka.payex.net
        environment: AzurePublicCloud
        managedIdentity:
          clientID: USER_ASSIGNED_IDENTITY_CLIENT_ID
