kafka:
  domain: kafka.stage.payex.net
  # bootstrap: ***********
  # brokers:
  #   - ***********
  #   - ***********
  #   - ***********
  bootstrap: ***********
  brokers:
    - ***********
    - ***********
    - ***********
  lbSubnet: aks_nodepool_subnet
  authentication:
    oauth:
      validIssuerUri: https://id.stage.payex.com/swedbankpay
      jwksEndpointUri: https://id.stage.payex.com/swedbankpay/jwks
      userNameClaim: sub

bridge:
  ingress:
    host: kafka-bridge.***********.nip.io
