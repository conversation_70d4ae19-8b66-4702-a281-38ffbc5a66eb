kind: ConfigMap
apiVersion: v1
metadata:
  name: external-logging-kafka
data:
  log4j.properties: |-
    log4j.appender.CONSOLE=org.apache.log4j.ConsoleAppender
    log4j.appender.CONSOLE.layout=net.logstash.log4j.JSONEventLayoutV1
    kafka.root.logger.level=INFO
    log4j.rootLogger=${kafka.root.logger.level}, CONSOLE
    log4j.logger.org.I0Itec.zkclient.ZkClient=INFO
    log4j.logger.org.apache.zookeeper=INFO
    log4j.logger.kafka=INFO
    log4j.logger.org.apache.kafka=INFO
    log4j.logger.kafka.request.logger=WARN, CONSOLE
    log4j.logger.kafka.network.Processor=OFF
    log4j.logger.kafka.server.KafkaApis=OFF
    log4j.logger.kafka.network.RequestChannel$=WARN
    log4j.logger.kafka.controller=TRACE
    log4j.logger.kafka.log.LogCleaner=INFO
    log4j.logger.state.change.logger=TRACE
    log4j.logger.kafka.authorizer.logger=INFO
