kind: ConfigMap
apiVersion: v1
metadata:
  name: external-logging-cc
data:
  cruise-control-log4j.properties: |-
    # Root logger configuration
    log4j.rootLogger=INFO, stdout

    # Define the stdout appender to log to console
    log4j.appender.stdout=org.apache.log4j.ConsoleAppender
    log4j.appender.stdout.Target=System.out
    log4j.appender.stdout.layout=org.apache.log4j.PatternLayout

    # Use a pattern that outputs JSON format
    log4j.appender.stdout.layout.ConversionPattern={"timestamp":"%d{ISO8601}","level":"%p","logger":"%c","message":"%m","thread":"%t"}%n
