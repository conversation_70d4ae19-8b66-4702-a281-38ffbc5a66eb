apiVersion: kafka.strimzi.io/v1beta2
kind: Kafka
metadata:
  name: {{ required "Must set clusterName" .Values.kafka.clusterName }}
  annotations:
    strimzi.io/kraft: enabled
    strimzi.io/node-pools: enabled
spec:
  kafka:
    version: {{ .Values.kafka.version }}
    authorization:
      type: simple
      superUsers:
        - CN=super-user
    listeners:
      - name: tls
        port: 9093
        type: internal
        tls: true      
        authentication:
          type: tls    
      - name: external
        port: 9094
        type: loadbalancer
        tls: true
        authentication:
          type: oauth
          clientId: data-platform-cluster
          clientSecret:
            key: clientSecret
            secretName: data-platform-cluster-curity
          validIssuerUri: {{ .Values.kafka.authentication.oauth.validIssuerUri }}
          jwksEndpointUri: {{ .Values.kafka.authentication.oauth.jwksEndpointUri }}
          userNameClaim: {{ .Values.kafka.authentication.oauth.userNameClaim }}
          checkAccessTokenType: false
        configuration:
          brokerCertChainAndKey:
            secretName: data-platform-cluster-broker-tls
            certificate: tls.crt
            key: tls.key
          bootstrap:
            annotations:
              service.beta.kubernetes.io/azure-load-balancer-internal: "true"
              service.beta.kubernetes.io/azure-load-balancer-ipv4: {{ .Values.kafka.bootstrap }}
              service.beta.kubernetes.io/azure-load-balancer-internal-subnet: {{ $.Values.kafka.lbSubnet }}
              external-dns.alpha.kubernetes.io/hostname: bootstrap.{{ .Values.kafka.domain }}.
            alternativeNames: 
            - bootstrap.{{ .Values.kafka.domain }}
          brokers:
          {{- range $index, $broker := .Values.kafka.brokers }}
          - broker: {{ $index }}
            annotations:
              service.beta.kubernetes.io/azure-load-balancer-internal: "true"
              service.beta.kubernetes.io/azure-load-balancer-ipv4: {{ $broker }}
              service.beta.kubernetes.io/azure-load-balancer-internal-subnet: {{ $.Values.kafka.lbSubnet }}
              external-dns.alpha.kubernetes.io/hostname: broker-{{ $index }}.{{ $.Values.kafka.domain }}.
            advertisedHost: broker-{{ $index }}.{{ $.Values.kafka.domain }}
          {{- end }}
    readinessProbe:
      initialDelaySeconds: 15
      timeoutSeconds: 5
    livenessProbe:
      initialDelaySeconds: 15
      timeoutSeconds: 5
    config:
      offsets.topic.replication.factor: {{ .Values.kafka.config.offsetsTopicReplicationFactor }}
      transaction.state.log.replication.factor: {{ .Values.kafka.config.transaction.state.log.replicationFactor }}
      transaction.state.log.min.isr: {{ .Values.kafka.config.transaction.state.log.minIsr }}
      auto.create.topics.enable: false
      delete.topic.enable: {{ .Values.kafka.config.deleteTopicEnabled }}
      unclean.leader.elecion: false
      log.roll.hours: 24
      log.segment.bytes: 1073741824
      # topic defaults
      log.retention.hours: 168
      default.replication.factor: {{ .Values.kafka.config.defaultReplicationFactor }}
      min.insync.replicas: {{ .Values.kafka.config.minInsyncReplicas }}
      num.partitions: 12
    rack:
      topologyKey: topology.kubernetes.io/zone
    metricsConfig:
      type: jmxPrometheusExporter
      valueFrom:
        configMapKeyRef:
          name: kafka-metrics
          key: kafka-metrics-config.yaml
    logging:
      type: external
      valueFrom:
        configMapKeyRef:
          name: external-logging-kafka
          key: log4j.properties

    template:
      pod: 
        affinity:
          nodeAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
              nodeSelectorTerms:
              - matchExpressions:
                - key: agentpool
                  operator: In
                  values:
                  - general
                  - generaltemp
  
          podAntiAffinity:
            preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 50
              podAffinityTerm:
                labelSelector:
                  matchLabels:
                    strimzi.io/cluster: data-platform-cluster
                    strimzi.io/name: data-platform-cluster-kafka
                topologyKey: kubernetes.io/hostname
  kafkaExporter:
    topicRegex: ".*"
    groupRegex: ".*"
    template:
      pod: 
        affinity:
          nodeAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
              nodeSelectorTerms:
              - matchExpressions:
                - key: agentpool
                  operator: In
                  values:
                  - general
                  - generaltemp
  entityOperator:
    topicOperator:
      logging:
        type: external
        valueFrom:
          configMapKeyRef:
            name: external-logging-to
            key: log4j2.properties
    userOperator:
      logging:
        type: external
        valueFrom:
          configMapKeyRef:
            name: external-logging-uo
            key: log4j2.properties
    template:
      pod: 
        affinity:
          nodeAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
              nodeSelectorTerms:
              - matchExpressions:
                - key: agentpool
                  operator: In
                  values:
                  - general
                  - generaltemp
  {{- if gt (len .Values.kafka.brokers) 1 }}
  cruiseControl:
    config: 
      bootstrap.servers: data-platform-cluster-kafka-bootstrap:9093
      sample.store.topic.replication.factor: {{ .Values.kafka.config.defaultReplicationFactor }}
      partition.metric.sample.store.on.execution.topic.replication.factor: {{ .Values.kafka.config.defaultReplicationFactor }}
      auto.leader.rebalance.enable: true
      leader.imbalance.check.interval.ms: 300000 # Example: 5 minutes
      leader.imbalance.threshold: 5
    logging:
      type: external
      valueFrom:
        configMapKeyRef:
          name: external-logging-cc
          key: cruise-control-log4j.properties
    template:
      pod: 
        affinity:
          nodeAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
              nodeSelectorTerms:
              - matchExpressions:
                - key: agentpool
                  operator: In
                  values:
                  - general
                  - generaltemp
  {{- end }}
