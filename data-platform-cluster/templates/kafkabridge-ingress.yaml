{{- if .Values.bridge.enabled -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ required "Must set clusterName" .Values.kafka.clusterName }}-bridge
  annotations:
    cert-manager.io/cluster-issuer: {{ .Values.bridge.ingress.clusterIssuer }}
    cert-manager.io/common-name: {{ .Values.bridge.ingress.host }}
    cert-manager.io/alt-names: {{ .Values.bridge.ingress.host }}
    cert-manager.io/alt-names: {{ .Values.bridge.ingress.host }}
    cert-manager.io/revision-history-limit: "1"
spec:
  ingressClassName: nginx
  rules:
  - host: {{ required "Must set bridge ingress host" .Values.bridge.ingress.host }}
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: {{ required "Must set clusterName" .Values.kafka.clusterName }}-bridge-service
            port: 
              number: 8080
  tls:
  - hosts:
    - {{ .Values.bridge.ingress.host }}
    secretName: {{ .Values.bridge.ingress.host }}-tls
{{- end -}}    