{{- if .Values.bridge.enabled -}}
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaBridge
metadata:
  name: {{ required "Must set clusterName" .Values.kafka.clusterName }}
spec:
  replicas: 1
  bootstrapServers: data-platform-cluster-kafka-bootstrap.kafka.svc:9093
  http:
    port: 8080
  authentication:
    type: tls
    certificateAndKey:
      certificate: user.crt
      key: user.key
      secretName: {{ .Values.bridge.authentication.secretName }}
  enableMetrics: true
  tls:
    trustedCertificates:
      - secretName: {{ .Values.bridge.trustedCertificateSecret }}
        certificate: ca.crt
{{- end -}}