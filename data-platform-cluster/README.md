# How to verify OAuth2.0 access to Kafka

## Requirements

* OAuth2.0 client with Client Credentials flow enabled
* <PERSON><PERSON>zi `KafkaUser` with same name as the above client
* Strimzi `KafkaTopic` to which the above KafkaUser has access
* Route to `cloud-data` Azure subscription
* Access to PayEx Azure Private DNS
* AKS Cluster 
* `kubectl` installed on local machine
* OAuth client secret
* Kafka cluster CA cert


## Create client.properties


### Manually

* CA_CERT
* TOKEN_URL
* CLIENT_ID
* CLIENT_SECRET

```
cat <<EOF >client.properties
security.protocol=SASL_SSL
ssl.truststore.type=PEM
ssl.truststore.certificates=$CA_CERT
sasl.login.callback.handler.class=org.apache.kafka.common.security.oauthbearer.secured.OAuthBearerLoginCallbackHandler
sasl.mechanism=OAUTHBEARER
sasl.oauthbearer.token.endpoint.url=$TOKEN_URL
sasl.jaas.config= \
  org.apache.kafka.common.security.oauthbearer.OAuthBearerLoginModule required \
    scope='kafka' \
    clientId='$CLIENT_ID' \
    clientSecret='$CLIENT_SECRET';
EOF

chmod 0600 client.properties

```

### Script

Run the `generate-kafka-client-config.sh` to do this. Update variables below and run the script.

This will create a client.properties file which we will use to connect to the cluster.

```
❯ bin/generate-kafka-client-config.sh
BOOTSTRAP_SERVER=kafka01.stage.payex.net:9094
KAFKA_USER_NAME=automate-pba
KAFKA_NAMESPACE=kafka
CLUSTER_NAME=data-platform-cluster
TOKEN_URL=https://id.stage.payex.com/swedbankpay/oauth-token
Context "stage-cloud-data-aks-admin" modified.

```

## Install kafka-client pod

```
cat <<EOF | kubectl -n kafka apply -f -
apiVersion: v1
kind: Pod
metadata:
  name: kafka-client
  labels:
    app.kubernetes.io/component: kafka-client
    app.kubernetes.io/managed-by: data-platform
    app.kubernetes.io/name: kafka-client
    app.payex.com/service: kafka-client
spec:
  containers:
  - name: kafka-client
    image: confluentinc/cp-kafka:7.3.0
    securityContext:
      runAsNonRoot: true
      runAsUser: 1000
      readOnlyRootFilesystem: true
    command:
      - sh
      - -c
      - "exec tail -f /dev/null"
EOF

```

Verify that it has been installed correctly

```
kubectl exec -n kafka -it kafka-client -- kafka-topics --version
```


Copy client.properties to the pod

```
kubectl cp client.properties -n kafka kafka-client:/tmp
```

List all topics (that you are allowed to access)

```
kubectl exec -n kafka -it kafka-client -- kafka-topics --bootstrap-server "kafka01.stage.payex.net:9094" --command-config /tmp/client.properties -list
```

## Troubleshooting

### Check access to bootstrap host

Run netcat to verify access to bootstrap host and port

```
kubectl exec -n kafka -it kafka-client -- timeout 0.5 nc -vz kafka01.stage.payex.net 9094
```



