affinity:
  enabled: true

kafka:
  clusterName: data-platform-cluster
  domain: kafka
  version: 3.8.1
  bootstrap: 127.0.0.1
  brokers:
    - 127.0.0.1
    - 127.0.0.1
    - 127.0.0.1
  lbSubnet: nodepool
  authentication:
    oauth:
      validIssuerUri: https://id.payex.com/swedbankpay
      jwksEndpointUri: https://id.payex.com/swedbankpay/jwks
      userNameClaim: sub
  config:
    defaultReplicationFactor: 3
    interBrokerProtocolVersion: 3.7
    deleteTopicEnabled: false
    minInsyncReplicas: 2
    offsetsTopicReplicationFactor: 3
    transaction:
      state:
        log:
          minIsr: 2
          replicationFactor: 3

broker:
  replicas: 3
  storage:
    size: 200Gi
    deleteClaim: false

controller:
  replicas: 3
  storage:
    size: 100Gi
    deleteClaim: false

bridge:
  ingress:
    host: INGRESS_HOST
    clusterIssuer: dataplatform-cluster-issuer
  trustedCertificateSecret: data-platform-cluster-cluster-ca-cert
  authentication:
    secretName: kafka-bridge-user
