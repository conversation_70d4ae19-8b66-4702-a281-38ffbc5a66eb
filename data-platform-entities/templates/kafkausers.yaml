{{- $topics := dict }}
{{- range $name, $def := .Values.kafka.topics}}
{{- $_ := set $topics $name "true" }}
{{- end }}
{{- range $name, $def := .Values.kafka.users}}
---
{{- $userName := $name }}
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaUser
metadata:
  name: {{ required "Must set Kafka user name" $name }}
  labels:
    strimzi.io/cluster: {{ required "Must set Kafka cluster name" $.Values.kafka.clusterName }}
spec:
  {{- if .authentication }}
  authentication:
    type: {{ .authentication | default "tls" }}
  {{- end }}
  {{- if .topic_access }}
  authorization:
    type: simple
    acls:
    {{- range .topic_access }}
      {{- $topicName := .name }}
      {{- $patternType := (.patternType | default "literal") }}
      {{- $host := .host  }}
      {{- if and (eq $patternType "literal") (not (hasKey $topics $topicName)) }}
      {{-   fail (print "KafkaUser (" $userName ") ACL refers to missing KafkaTopic (" $topicName ") ") }}
      {{- end }}
      - resource:
          type: topic
          name: {{ required "Must set topic name for ACL" $topicName | quote }}
          patternType: {{ $patternType | default "literal"  }}
        operations: {{ required "Must specify topic operations" .operations | toJson }}
        host: {{ $host | default "*" | quote  }}
    {{- end }}
    {{- range .group_access }}
      - resource:
          type: group
          name: {{ required "Must set group name for ACL" .name | quote }}
          patternType: {{ .patternType | default "prefix"  }}
        operations: ["Read"]
        host: {{ .host | default "*" | quote  }}
    {{- end }}
  {{ end }} 
{{- end }}
