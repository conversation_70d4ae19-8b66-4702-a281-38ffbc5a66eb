{{- range $name, $def := .Values.kafka.topics}}
{{- $kubernetesResourceName := $name -}}
{{- if hasPrefix "_" $kubernetesResourceName -}}
  {{- $kubernetesResourceName = trimPrefix "_" $kubernetesResourceName -}}
{{- end -}}
{{- $kubernetesResourceName = replace "_" "-" $kubernetesResourceName -}}
{{ $kubernetesResourceName = lower $kubernetesResourceName }}
---
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  name: {{ $kubernetesResourceName }}
  labels:
    strimzi.io/cluster: {{ required "Must set Kafka cluster name" $.Values.kafka.clusterName }}
spec:
  topicName: {{ $name }}
  partitions: {{ required "Must set topic partitions" (.partitions | default $.Values.kafka.topic.default.partitions) }}
  replicas: {{ required "Must set topic replicas" (.replicas | default $.Values.kafka.topic.default.replicas) }}
{{- with .config }}
  config:
{{ toYaml . | indent 4 }}
{{- end }}

{{- end }}
