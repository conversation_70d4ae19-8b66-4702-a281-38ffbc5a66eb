kafka:
  users:
    mx-customers:
      topic_access:
        - name: mx.customers.v1
          operations: ["All"]
        - name: ims.merchants.v1
          operations: ["Read", "Describe"]
        - name: automate-efecte.agreement-events.v1
          operations: ["Read", "Describe"]
        - name: mx.arrangements.v1
          operations: ["Read", "Describe"]
        - name: mx.legalinfo.v1
          operations: ["Read", "Describe"]
        - name: mx.customers.legalinfo.v1
          operations: ["All"]
        - name: mx.acquiring-access.v1
          operations: [ "Read", "Describe" ]
      group_access:
        - name: mx-customers

    mx-arrangements:
      topic_access:
        - name: mx.arrangements.v1
          operations: ["All"]
        - name: psp.contracts.v1
          operations: ["Read", "Describe"]
        - name: mx.customers.v1
          operations: ["Read", "Describe"]
        - name: mx.legalinfo.v1
          operations: ["Read", "Describe"]
        - name: mx.customers.legalinfo.v1
          operations: ["Read", "Describe"]
        - name: mx.pba.lookup.merchant.v1
          operations: ["Read", "Describe"]
        - name: mx.tms.lookup.merchant.v1
          operations: ["Read", "Describe"]
        - name: mx.acquiring-merchants.v1
          operations: ["Read", "Describe"]
        - name: automate-efecte.agreement-events.v1
          operations: ["Read", "Describe"]
        - name: mx.customers.contactinfo.v1
          operations: [ "Read", "Describe" ]
        - name: mx.acquiring-access.v1
          operations: [ "Read", "Describe" ]
      group_access:
        - name: mx-arrangements

    mx-backoffice:
      topic_access:
        - name: mx.legalinfo.v1
          operations: ["All"]
        - name: mx.pba.reports.v1
          operations: [ "All" ]
        - name: mx.pxr.invoices.v1
          operations: [ "All" ]
        - name: automate-efecte.agreement-events.v1
          operations: ["Describe", "Read"]
        - name: mx.customers.v1
          operations: ["Describe", "Read"]
      group_access:
        - name: mx-backoffice

    mx-search:
      topic_access:
        - name: mx.search.customer.v1
          operations: ["All"]
        - name: mx.customers.v1
          operations: ["Read", "Describe"]
        - name: mx.pxr.invoices.v1
          operations: ["Read", "Describe"]
      group_access:
        - name: mx-search

    psp-contracts:
      topic_access:
        - name: mx.arrangements.v1
          operations: ["Read", "Describe"]
        - name: psp.contracts.v1
          operations: ["All"]
      group_access:
        - name: psp-contracts

    mx-pba-lookup:
      topic_access:
        - name: mx.pba.lookup.merchant.v1
          operations: ["All"]
        - name: mx.arrangements.v1
          operations: ["Read", "Describe"]
      group_access:
        - name: mx-pba-lookup

    mx-tms-lookup:
      topic_access:
        - name: mx.tms.lookup.merchant.v1
          operations: ["All"]
        - name: mx.arrangements.v1
          operations: ["Read", "Describe"]
      group_access:
        - name: mx-tms-lookup

    mx-acquiring:
      topic_access:
        - name: mx.acquiring-merchants.v1
          operations: ["All"]
        - name: mx.acquiring-access.v1
          operations: ["All"]
        - name: mx.arrangements.v1
          operations: [ "Read", "Describe" ]
      group_access:
        - name: mx-acquiring

    mx-workflows:
      topic_access:
        - name: mx.arrangements.v1
          operations: ["All"]
        - name: mx.customers.v1
          operations: ["Describe", "Read"]
      group_access:
        - name: mx-workflows

    mx-ims:
      topic_access:
        - name: bims-mdd-curated
          operations: ["Describe", "Read"]
          patternType: prefix
        - name: bims-add-curated
          operations: ["Describe", "Read"]
          patternType: prefix
        - name: bims-pdd-curated
          operations: ["Describe", "Read"]
          patternType: prefix
        - name: bims-bundles-curated
          operations: ["Describe", "Read"]
          patternType: prefix
        - name: bims-exceptions-curated
          operations: ["Describe", "Read"]
          patternType: prefix
      group_access:
        - name: mx-ims

    mx-legalinfo:
      topic_access:
        - name: mx.customers.contactinfo.v1
          operations: [ "Read" ]
      group_access:
        - name: mx-legalinfo

    mx-contactinfo:
      topic_access:
        - name: mx.customers.contactinfo.v1
          operations: [ "All" ]
      group_access:
        - name: mx-contactinfo