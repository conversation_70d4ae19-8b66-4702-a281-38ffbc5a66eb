kafka:
  topics:
    _schemas:
      replicas: 3
      partitions: 1
      config:
        cleanup.policy: compact
        min.insync.replicas: 2
        retention.ms: -1
    kafkasql-journal:
      replicas: 3
      partitions: 1
      config:
        cleanup.policy: compact
        min.insync.replicas: 2
        retention.ms: -1
        retention.bytes: -1
    kafkasql-snapshots:
      replicas: 3
      partitions: 1
      config:
        min.insync.replicas: 2
        retention.ms: 259200000 # 3 days
    registry-events:
      replicas: 3
      partitions: 1
      config:
        min.insync.replicas: 2
        retention.ms: ********* # 7 days
    mm2-offset-syncs.data-platform-cluster.internal:
      replicas: 3
      partitions: 1
    mm2-checkpoints.schema-registry-cluster.internal:
      replicas: 3
      partitions: 1
      config:
        cleanup.policy: compact
    strimzi.cruisecontrol.metrics:
      replicas: 3
      partitions: 12
    strimzi.cruisecontrol.modeltrainingsamples:
      replicas: 3
      partitions: 32
    strimzi.cruisecontrol.partitionmetricsamples:
      replicas: 3
      partitions: 32
    automate-efecte.agreement-events.v1:
      replicas: 3
      partitions: 12
      config:
        retention.ms: -1
    automate-efecte.entity-updated:
      config:
        retention.ms: 31536000000 #1 year
    automate-efecte.external-company-raw:
      config:
        retention.ms: ********* #1 week
    automate-visma.merchant-events.v1:
      config:
        retention.ms: 31536000000 #1 year
    automate-tms.terminal-events.v1:
      config:
        retention.ms: 31536000000 #1 year
    automate-pos.merchant-events.v1:
      config:
        retention.ms: 31536000000 #1 year
    automate-pba.merchant-events.v1:
      config:
        retention.ms: 31536000000 #1 year
    automate-softpos.merchant-events.v1:
      config:
        retention.ms: 31536000000 #1 year
    automate-softpos.orders.v1:
      config:
        retention.ms: 31536000000 #1 year
    automate-softpos.errors.v1:
      config:
        retention.ms: 31536000000 #1 year
    bims.add:
      config:
        retention.ms: -1
    bims.mdd:
      config:
        retention.ms: -1
    bims.settlements:
      config:
        retention.ms: -1
    base24-authorizations-curated:
      config:
        retention.ms: 86400000 # 24 hour
    base24-authorizations-error:
      config:
        retention.ms: ********** # 30 days
    base24-authorizations-raw:
      config:
        #compression.type: gzip
        retention.ms: 86400000 # 24 hour
        segment.ms: 86400000 # < 24 hour
    bims-mdd-raw:
      config:
        #compression.type: gzip
        retention.ms: ********* # 1 week
        segment.ms: ********* # < 1 week
    bims-add-raw:
      config:
        #compression.type: gzip
        retention.ms: ********* # 1 week
        segment.ms: ********* # < 1 week
    bims-pdd-raw:
      config:
        #compression.type: gzip
        retention.ms: ********* # 1 week
        segment.ms: ********* # < 1 week
    bims-bundles-raw:
      config:
        #compression.type: gzip
        retention.ms: ********* # 1 week
        segment.ms: ********* # < 1 week
    bims-elsa-raw:
      config:
        #compression.type: gzip
        retention.ms: ********* # 1 week
        segment.ms: ********* # < 1 week
    bims-exceptions-raw:
      config:
        #compression.type: gzip
        retention.ms: ********* # 1 week
        segment.ms: ********* # < 1 week
    bims-mdd-curated:
      config:
        cleanup.policy: "compact"
        retention.ms: -1 # Forever
    bims-add-curated:
      config:
        retention.ms: ********* # 1 week
    bims-pdd-curated:
      config:
        cleanup.policy: "compact"
        retention.ms: -1 # Forever
    bims-bundles-curated:
      config:
        cleanup.policy: "delete"
        retention.ms: ********* # 1 week
    bims-exceptions-curated:
      config:
        cleanup.policy: "delete"
        retention.ms: ********* # 1 week
    bims-elsa-curated:
      config:
        retention.ms: 5184000000  # 60 days
    bims-mdd-error:
      config:
        retention.ms: ********** # 30 days
    bims-add-error:
      config:
        retention.ms: ********** # 30 days
    bims-pdd-error:
      config:
        retention.ms: ********** # 30 days
    bims-bundles-error:
      config:
        retention.ms: ********** # 30 days
    bims-exceptions-error:
      config:
        retention.ms: ********** # 30 days
    bims-elsa-error:
      config:
        retention.ms: ********** # 30 days
    ims.merchants.v1:
      config:
        retention.ms: ********* #1 week
    psp.contracts.v1:
      config:
        retention.ms: ********* #1 week
    kafka-health-indicator.v1:
      config:
        retention.ms: 31536000000 #1 year
    auth-stats-stats-store-repartition:
      config:
        retention.ms: 172800000 #2 days
    auth-stats-authorizations-store-repartition:
      config:
        retention.ms: 172800000 #2 days
    auth-stats-stats-store-changelog:
      config:
        retention.ms: 172800000 #2 days
    auth-stats-authorizations-store-changelog:
      config:
        retention.ms: 172800000 #2 days
