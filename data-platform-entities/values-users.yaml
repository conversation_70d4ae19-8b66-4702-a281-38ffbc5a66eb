kafka:
  users:
    super-user:
      authentication: tls
    apicurio-registry-user:
      authentication: tls
      topic_access:
        - name: kafkasql-journal
          operations: ["All"]
        - name: kafkasql-snapshots
          operations: ["All"]
        - name: registry-events
          operations: ["All"]
      group_access:
        - name: apicurio

    automate-efecte:
      topic_access:
        - name: automate-efecte.agreement-events.v1
          operations: ["All"]
        - name: automate-efecte.entity-updated
          operations: ["All"]
        - name: automate-efecte.external-company-raw
          operations: ["All"]
        - name: automate-visma.merchant-events.v1
          operations: ["Describe", "Read"]
        - name: automate-tms.terminal-events.v1
          operations: ["Describe", "Read"]
        - name: automate-pos.merchant-events.v1
          operations: ["Describe", "Read"]
        - name: automate-pba.merchant-events.v1
          operations: ["Describe", "Read"]
        - name: automate-softpos.orders.v1
          operations: ["Describe", "Read"]
        - name: mx.customers.v1
          operations: ["Describe", "Read"]
        - name: kafka-health-indicator.v1
          operations: ["All"]
      group_access:
        - name: automate-efecte

    automate-arrangement-orchestrator:
      topic_access:
        - name: automate-efecte.agreement-events.v1
          operations: ["All"]
        - name: automate-efecte.entity-updated
          operations: ["All"]
        - name: automate-efecte.external-company-raw
          operations: ["All"]
        - name: automate-visma.merchant-events.v1
          operations: ["Describe", "Read"]
        - name: automate-tms.terminal-events.v1
          operations: ["Describe", "Read"]
        - name: automate-pos.merchant-events.v1
          operations: ["Describe", "Read"]
        - name: automate-pba.merchant-events.v1
          operations: ["Describe", "Read"]
        - name: automate-softpos.orders.v1
          operations: ["Describe", "Read"]
        - name: mx.customers.v1
          operations: ["Describe", "Read"]
        - name: kafka-health-indicator.v1
          operations: ["All"]
      group_access:
        - name: automate-arrangement-orchestrator
        - name: automate-efecte

    automate-pba:
      topic_access:
        - name: automate-efecte.agreement-events.v1
          operations: ["Describe", "Read"]
        - name: automate-pba.merchant-events.v1
          operations: ["All"]
      group_access:
        - name: automate-pba

    automate-pos:
      topic_access:
        - name: automate-efecte.agreement-events.v1
          operations: ["Describe", "Read"]
        - name: automate-pos.merchant-events.v1
          operations: ["All"]
      group_access:
        - name: automate-pos

    automate-visma:
      topic_access:
        - name: automate-efecte.agreement-events.v1
          operations: ["Describe", "Read", "Write"]
        - name: automate-visma.merchant-events.v1
          operations: ["All"]
      group_access:
        - name: automate-visma

    automate-tms:
      topic_access:
        - name: automate-efecte.agreement-events.v1
          operations: ["Describe", "Read"]
        - name: automate-tms.terminal-events.v1
          operations: ["All"]
      group_access:
        - name: automate-tms

    automate-softpos:
      topic_access:
        - name: automate-efecte.agreement-events.v1
          operations: ["Describe", "Read"]
        - name: automate-softpos.orders.v1
          operations: ["All"]
        - name: automate-softpos.errors.v1
          operations: ["All"]
      group_access:
        - name: automate-softpos

    authorizations-transformer:
      topic_access:
        - name: base24-authorizations-raw
          operations: ["Describe", "Read"]
        - name: base24-authorizations-curated
          operations: ["All"]
        - name: base24-authorizations-error
          operations: ["All"]
      group_access:
        - name: authorizations-transformer
        - name: authorizations-transformer-error

    bims-mdd-transformer:
      topic_access:
        - name: bims-mdd-raw
          operations: ["Describe", "Read"]
        - name: bims-mdd-curated
          operations: ["All"]
        - name: bims-mdd-error
          operations: ["All"]
      group_access:
        - name: bims-mdd-transformer

    bims-add-transformer:
      topic_access:
        - name: bims-add-raw
          operations: ["Describe", "Read"]
        - name: bims-add-curated
          operations: ["All"]
        - name: bims-add-error
          operations: ["All"]
      group_access:
        - name: bims-add-transformer

    bims-pdd-transformer:
      topic_access:
        - name: bims-pdd-raw
          operations: ["Describe", "Read"]
        - name: bims-pdd-curated
          operations: ["All"]
        - name: bims-pdd-error
          operations: ["All"]
      group_access:
        - name: bims-pdd-transformer

    bims-bundles-transformer:
      topic_access:
        - name: bims-bundles-raw
          operations: ["Describe", "Read"]
        - name: bims-bundles-curated
          operations: ["All"]
        - name: bims-bundles-error
          operations: ["All"]
      group_access:
        - name: bims-bundles-transformer

    bims-exceptions-transformer:
      topic_access:
        - name: bims-exceptions-raw
          operations: ["Describe", "Read"]
        - name: bims-exceptions-curated
          operations: ["All"]
        - name: bims-exceptions-error
          operations: ["All"]
      group_access:
        - name: bims-exceptions-transformer

    bims-elsa-transformer:
      topic_access:
        - name: bims-elsa-raw
          operations: ["Describe", "Read"]
        - name: bims-elsa-curated
          operations: ["All"]
        - name: bims-elsa-error
          operations: ["All"]
      group_access:
        - name: bims-elsa-transformer

    auth-stats:
      topic_access:
        - name: base24-authorizations-curated
          operations: ["All"]
        - name: base24-authorizations-error
          operations: ["All"]
        - name: auth-stats-stats-store-repartition
          operations: ["All"]
        - name: auth-stats-authorizations-store-repartition
          operations: ["All"]
        - name: auth-stats-stats-store-changelog
          operations: ["All"]
        - name: auth-stats-authorizations-store-changelog
          operations: ["All"]
      group_access:
        - name: auth-stats

    data-platform-connect-cluster-user:
      authentication: tls
      topic_access:
        - name: data-platform-connect-cluster
          operations: ["All"]
          patternType: prefix
        - name: bims.settlements
          operations: ["Write"]
          patternType: prefix
        - name: bims.mdd
          operations: ["Write"]
          patternType: prefix
        - name: bims.add
          operations: ["Write"]
          patternType: prefix
        - name: base24-authorizations-raw
          operations: ["Write"]
          patternType: prefix
        - name: base24-authorizations-curated
          operations: ["Describe", "Read", "Write"]
          patternType: prefix
        - name: base24-authorizations-error
          operations: ["Write"]
          patternType: prefix
        - name: bims-mdd-raw
          operations: ["Write"]
          patternType: prefix
        - name: bims-add-raw
          operations: ["Write"]
          patternType: prefix
        - name: bims-pdd-raw
          operations: ["Write"]
          patternType: prefix
        - name: bims-bundles-raw
          operations: ["Write"]
          patternType: prefix
        - name: bims-exceptions-raw
          operations: ["Write"]
          patternType: prefix
        - name: bims-elsa-raw
          operations: ["Write"]
          patternType: prefix
        - name: bims-mdd-curated
          operations: ["Describe", "Read", "Write"]
          patternType: prefix
        - name: bims-add-curated
          operations: ["Describe", "Read", "Write"]
          patternType: prefix
        - name: bims-pdd-curated
          operations: ["Describe", "Read", "Write"]
          patternType: prefix
        - name: bims-bundles-curated
          operations: ["Describe", "Read", "Write"]
          patternType: prefix
        - name: bims-elsa-curated
          operations: ["Describe", "Read", "Write"]
          patternType: prefix
        - name: bims-exceptions-curated
          operations: ["Describe", "Read", "Write"]
          patternType: prefix
      group_access:
        - name: "data-platform-connect-cluster"

    flink-authorizations-user:
      authentication: tls
      topic_access:
        - name: base24-authorizations-curated
          operations: ["Describe", "Read"]
      group_access:
        - name: flink

    signup:
      topic_access:
        - name: automate-efecte.entity-updated
          operations: ["Describe", "Read"]
      group_access:
        - name: signup

    schema-registry-user:
      authentication: tls
      topic_access:
        - name: _schemas
          operations: ["All"]
      group_access:
        - name: schema-registry
